class TestModel {
  final String divisionId;
  final String costCenterId;
  final String costCenterName;
  final String accountancyId;
  final String hyperionId;
  final String posId;
  final bool canBeDeleted;

  TestModel({
    required this.divisionId,
    required this.costCenterId,
    required this.costCenterName,
    required this.accountancyId,
    required this.hyperionId,
    required this.posId,
    required this.canBeDeleted,
  });

  factory TestModel.fromJson(Map<String, dynamic> json) {
    return TestModel(
      divisionId: json['division_id'],
      costCenterId: json['cost_center_id'],
      costCenterName: json['cost_center_name'],
      accountancyId: json['accountancy_id'],
      hyperionId: json['hyperion_id'],
      posId: json['pos_id'],
      canBeDeleted: json['can_be_deleted'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'division_id': divisionId,
      'cost_center_id': costCenterId,
      'cost_center_name': costCenterName,
      'accountancy_id': accountancyId,
      'hyperion_id': hyperionId,
      'pos_id': posId,
      'can_be_deleted': canBeDeleted,
    };
  }

  @override
  String toString() {
    return toJson().toString();
  }
}