import 'package:app/receivings_module/models/filter/filter_value.dart';
import 'package:app/shared/cubits/paged_entity_list/cubit.dart';
import 'package:app/shared/repositories/test_task.dart';
import 'package:app/shared/types/app_error.dart';
import 'package:app/shared/types/result.dart';
import 'package:app/test_module/models/test_task.dart';

class TestCubit extends PagedEntityListCubit<TestModel, EmptyStateExtension> {
  TestCubit(
    this._repository, {
    required Map<String, FilterValueModel> defaultFilterValues,
    required bool isOnline,
    required String language,
    int pageSize = 15,
  }) : super(
          defaultFilterValues: defaultFilterValues,
          initialStateExtension: emptyStateExtension,
          isOnline: isOnline,
          pageSize: pageSize,
        );

  final TestTaskRepository _repository;

  @override
  Future<Result<List<TestModel>, AppError>> loadEntities(
    int pageSize,
    int pageIndex,
    Map<String, FilterValueModel>? filterValues,
  ) {
    print('loadEntities');
    return _repository.loadEntities(
      filterValues: filterValues!,
      page: pageIndex,
      pageSize: pageSize,
      language: 'en',
    );
  }
  
}

class TestStateExtension {
  final String language;
  final status;

  const TestStateExtension({
    required this.language,
    required this.status,
  });
}
