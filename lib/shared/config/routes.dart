import 'package:app/approvals/screens/approval_request/approval_request.dart';
import 'package:app/approvals/screens/booking_approval_requests/booking_approval_requests.dart';
import 'package:app/approvals/screens/capex_approval_requests/capex_approval_request.dart';
import 'package:app/approvals/screens/capex_approval_requests/capex_approval_requests.dart';
import 'package:app/approvals/screens/invoices/invoice.dart';
import 'package:app/approvals/screens/invoices/invoices.dart';
import 'package:app/approvals/screens/purchase_requests/purchase_requests.dart';
import 'package:app/approvals/screens/receiving_approval_requests/receiving_approval_requests.dart';
import 'package:app/catalog_module/screens/products_search.dart';
import 'package:app/ims/screens/expired_documents.dart';
import 'package:app/ims/screens/mapping_products.dart';
import 'package:app/inventory_control/screens/inventory_lists.dart';
import 'package:app/invoices_archive_module/screens/invoices_archive.dart';
import 'package:app/login/screens/cost_center_lookup.dart';
import 'package:app/login/screens/entrance_screen.dart';
import 'package:app/login/screens/login.dart';
import 'package:app/login/screens/password_reset.dart';
import 'package:app/login/screens/set_pin.dart';
import 'package:app/ordering_module/screens/order_lists.dart';
import 'package:app/ordering_module/screens/orders.dart' as ordering;
import 'package:app/receivings_module/screens/process_receivings.dart';
import 'package:app/shared/screens/announcements.dart';
import 'package:app/shared/screens/init_storage.dart';
import 'package:app/shared/screens/notifications.dart';
import 'package:app/shared/screens/pin_confirmation.dart';
import 'package:app/shared/screens/setup.dart';
import 'package:app/shared/screens/start.dart';
import 'package:app/shared/screens/sync.dart';
import 'package:app/shopping_cart_module/screens/orders_to_approve.dart';
import 'package:app/shopping_cart_module/screens/orders_to_send.dart';
import 'package:app/shopping_cart_module/screens/overview.dart';
import 'package:app/suppliers_module/screens/documents.dart';
import 'package:app/suppliers_module/screens/suppliers.dart';
import 'package:app/transfers_module/screens/bookings/bookings.dart';
import 'package:app/transfers_module/screens/transfer_lists/transfer_list_orders.dart';
import 'package:app/transfers_module/screens/transfer_lists/transfer_lists.dart';
import 'package:flutter/material.dart';

// -- IDs --
const ANNOUNCEMENTS_SCREEN_ROUTE_ID = 1633795140;
const NOTIFICATIONS_SCREEN_ROUTE_ID = 1633795141;
const INVENTORY_CONTROL_SCREEN_ROUTE_ID = 1633795142;
const SHOPPING_CART_OVERVIEW_SCREEN_ROUTE_ID = 1633795143;
const SHOPPING_CART_ORDERS_TO_APPROVE_SCREEN_ROUTE_ID = 1633795144;
const SHOPPING_CART_ORDERS_TO_SEND_SCREEN_ROUTE_ID = 1633795145;
const CATALOG_SCREEN_ROUTE_ID = 1633795146;
const ORDER_LISTS_SCREEN_ROUTE_ID = 1633795147;
const ORDERS_SCREEN_ROUTE_ID = 1633795148;
const RECEIVINGS_SCREEN_ROUTE_ID = 1633795149;
const INVOICES_ARCHIVE_SCREEN_ROUTE_ID = 1633795150;
const PURCHASE_REQUESTS_SCREEN_ROUTE_ID = 1633795151;
const BOOKING_APPROVAL_REQUESTS_SCREEN_ROUTE_ID = 1633795152;
const RECEIVING_APPROVAL_REQUESTS_SCREEN_ROUTE_ID = 1645004707;
const INVOICES_SCREEN_ROUTE_ID = 1633795153;
const TRANSFER_LISTS_SCREEN_ROUTE_ID = 1633795154;
const TRANSFER_LIST_ORDERS_SCREEN_ROUTE_ID = 1633795156;
const BOOKINGS_SCREEN_ROUTE_ID = 1633795155;
const SUPPLIERS_SCREEN_ROUTE_ID = 1633795157;
const RECEIVING_ORDERS_SCREEN_ROUTE_ID = 1633795158;
const IMS_ITEMS_MAPPING_SCREEN_ROUTE_ID = 1657720249;
const IMS_EXPIRING_DOCUMENTS_ROUTE_ID = 1686642718;

// -- Startup --
const START_SCREEN_ROUTE = '/start';
const SETUP_SCREEN_ROUTE = '/setup';
const SET_PIN_SCREEN_ROUTE = '/set_pin';
const INIT_STORAGE_AFTER_SET_PIN_SCREEN_ROUTE = '/init_storage_and_sync';
const INIT_STORAGE_USUAL_SCREEN_ROUTE = '/init_storage';
const SYNC_SCREEN_ROUTE = '/sync';

// -- Login --
const LOGIN_SCREEN_ROUTE = '/login';
const PASSWORD_RESET_SCREEN_ROUTE = '/reset_password';

// -- Cost centers --
const ENTRANCE_SCREEN_ROUTE = '/entrance';
const LOGIN_COST_CENTER_LOOKUP_SCREEN_ROUTE = '/cost_center_lookup';

// -- Others --
const ANNOUNCEMENTS_SCREEN_ROUTE = '/messages';
const NOTIFICATIONS_SCREEN_ROUTE = '/notifications';

// -- Inventory control --
const INVENTORY_CONTROL_SCREEN_ROUTE = '/inventory_control';

// -- Shopping cart --
const SHOPPING_CART_OVERVIEW_SCREEN_ROUTE = '/shopping_cart_overview';
const SHOPPING_CART_ORDERS_TO_APPROVE_SCREEN_ROUTE =
    '/shopping_cart_order_to_approve';
const SHOPPING_CART_ORDERS_TO_SEND_SCREEN_ROUTE =
    '/shopping_cart_order_to_send';

// -- Advanced Search --
const CATALOG_SCREEN_ROUTE = '/catalog';

// -- Ordering --
const ORDER_LISTS_SCREEN_ROUTE = '/order_lists';
const ORDERS_SCREEN_ROUTE = '/orders';

// -- Receivings --
const PROCESS_RECEIVINGS_SCREEN_ROUTE = '/process_receivings';

// -- Invoices archive --
const INVOICES_ARCHIVE_SCREEN_ROUTE = '/invoices_archive';

// -- Approval center --
const PURCHASE_REQUESTS_SCREEN_ROUTE = '/purchase_requests';
const PURCHASE_REQUEST_SCREEN_ROUTE = '/purchase_request';
const BOOKING_APPROVAL_REQUESTS_SCREEN_ROUTE = '/booking_approval_requests';
const BOOKING_APPROVAL_REQUEST_SCREEN_ROUTE = '/booking_approval_request';
const RECEIVING_APPROVAL_REQUESTS_SCREEN_ROUTE = '/receiving_approval_requests';
const RECEIVING_APPROVAL_REQUEST_SCREEN_ROUTE = '/receiving_approval_request';
const CAPEX_MY_APPROVAL_REQUESTS_OVERVIEW_SCREEN_ROUTE =
    '/capex_approval_requests';
const CAPEX_APPROVAL_REQUEST_SCREEN_ROUTE = '/capex_approval_request';
const CAPEX_APPROVAL_REQUEST_SCREEN_WITH_BAK_TO_LIST_ROUTE =
    '/capex_approval_request_back_to_list';
const INVOICES_SCREEN_ROUTE = '/invoices';
const INVOICE_SCREEN_ROUTE = '/invoice';

// -- Transfer Lists --
const TRANSFER_LISTS_SCREEN_ROUTE = '/transfer_lists';
const TRANSFER_LIST_ORDERS_SCREEN_ROUTE = '/transfer_list_orders';
const BOOKINGS_SCREEN_ROUTE = '/bookings';

// -- Suppliers --
const SUPPLIERS_SCREEN_ROUTE = '/suppliers';
const SUPPLIER_DOCUMENTS_ROUTE = '/supplier_documents';

// -- PDF view --
const PDF_VIEW_SCREEN_ROUTE = '/pdf_view';

// -- Utils --
const PIN_CONFIRMATION_SCREEN_ROUTE = '/pin_confirmation';

// -- IMS --
const IMS_MAPPING_PRODUCTS_SCREEN_ROUTE = '/ims_mapping_products';
const IMS_EXPIRING_DOCUMENTS_SCREEN_ROUTE = '/ims_expired_documents';

// Tabs
// TODO: review
const int PURCHASE_REQUEST_SCREEN_ROUTE_DETAILS_TAB = 0;
const int PURCHASE_REQUEST_SCREEN_ROUTE_MESSAGES_TAB = 1;
const int PURCHASE_REQUEST_SCREEN_ROUTE_LOG_TAB = 2;
const int PURCHASE_REQUEST_SCREEN_ROUTE_DOCUMENTS_TAB = 3;

const int BOOKING_APPROVAL_REQUEST_SCREEN_ROUTE_DETAILS_TAB = 0;
const int BOOKING_APPROVAL_REQUEST_SCREEN_ROUTE_MESSAGES_TAB = 1;
const int BOOKING_APPROVAL_REQUEST_SCREEN_ROUTE_LOG_TAB = 2;
const int BOOKING_APPROVAL_REQUEST_SCREEN_ROUTE_DOCUMENTS_TAB = 3;

const int CAPEX_APPROVAL_REQUESTS_SCREEN_ROUTE_ACTIVE_TAB = 0;
const int CAPEX_APPROVAL_REQUESTS_SCREEN_ROUTE_ACTIVE_ASSIGNED_TO_ME_TAB = 11;
const int CAPEX_APPROVAL_REQUESTS_SCREEN_ROUTE_AUTHORIZED_TAB = 1;
const int CAPEX_APPROVAL_REQUESTS_SCREEN_ROUTE_CLOSED_TAB = 2;

const int CAPEX_APPROVAL_REQUEST_SCREEN_ROUTE_DEFAULT_TAB = 0;
const int CAPEX_APPROVAL_REQUEST_SCREEN_ROUTE_DOCUMENTS_TAB = 2;
const int CAPEX_APPROVAL_REQUEST_SCREEN_ROUTE_MESSAGES_TAB = 3;

const int APPROVAL_REQUEST_SCREEN_ROUTE_DETAILS_TAB = 0;
const int APPROVAL_REQUEST_SCREEN_ROUTE_MESSAGES_TAB = 1;
const int APPROVAL_REQUEST_SCREEN_ROUTE_LOG_TAB = 2;
const int APPROVAL_REQUEST_SCREEN_ROUTE_DOCUMENTS_TAB = 3;

const int INVOICE_SCREEN_ROUTE_DETAILS_TAB = 0;
const int INVOICE_SCREEN_ROUTE_LOG_TAB = 1;

const int ARCHIVED_INVOICE_SCREEN_ROUTE_DETAILS_TAB = 0;
const int ARCHIVED_INVOICE_SCREEN_ROUTE_LOG_TAB = 1;

// TODO: review other screens
const Map<String, Map<String, dynamic>> ROUTES_OPTIONS = {
  START_SCREEN_ROUTE: {
    'showNotifications': false,
    'redirectAvailable': false,
  },
  SETUP_SCREEN_ROUTE: {
    'showNotifications': false,
    'redirectAvailable': false,
  },
  SET_PIN_SCREEN_ROUTE: {
    'showNotifications': false,
    'redirectAvailable': false,
  },
  INIT_STORAGE_USUAL_SCREEN_ROUTE: {
    'showNotifications': false,
    'redirectAvailable': false,
  },
  INIT_STORAGE_AFTER_SET_PIN_SCREEN_ROUTE: {
    'showNotifications': false,
    'redirectAvailable': false,
  },
  ENTRANCE_SCREEN_ROUTE: {
    'showNotifications': false,
    'redirectAvailable': false,
  },
  SYNC_SCREEN_ROUTE: {
    'showNotifications': false,
    'redirectAvailable': false,
  },
  LOGIN_SCREEN_ROUTE: {
    'showNotifications': false,
    'redirectAvailable': false,
  },
  PASSWORD_RESET_SCREEN_ROUTE: {
    'showNotifications': false,
    'redirectAvailable': false,
  },
  LOGIN_COST_CENTER_LOOKUP_SCREEN_ROUTE: {
    'showNotifications': false,
    'redirectAvailable': false,
  },
  PDF_VIEW_SCREEN_ROUTE: {
    'showNotifications': false,
    'redirectAvailable': false,
  },
  PIN_CONFIRMATION_SCREEN_ROUTE: {
    'showNotifications': false,
    'redirectAvailable': false,
  },
};

class RoutesConfiguration {
  RoutesConfiguration();

  final Map<String, WidgetBuilder> routes = {
    // -- Startup --
    START_SCREEN_ROUTE: (_) => const StartScreen(),
    SETUP_SCREEN_ROUTE: (_) => const SetupScreen(),
    SET_PIN_SCREEN_ROUTE: SetPinScreen.route().builder,
    INIT_STORAGE_USUAL_SCREEN_ROUTE: (_) => const InitStorageScreen.regular(),
    INIT_STORAGE_AFTER_SET_PIN_SCREEN_ROUTE: (_) =>
        const InitStorageScreen.afterSettingPin(),
    ENTRANCE_SCREEN_ROUTE: EntranceScreen.route().builder,
    SYNC_SCREEN_ROUTE: (_) => const SyncScreen(),

    // -- Login --
    LOGIN_SCREEN_ROUTE: (_) => const LoginScreen(),
    PASSWORD_RESET_SCREEN_ROUTE: (_) => const PasswordResetScreen(),

    // -- Cost centers --
    LOGIN_COST_CENTER_LOOKUP_SCREEN_ROUTE: (_) =>
        const LoginCostCenterLookupScreen(),

    // -- Others --
    ANNOUNCEMENTS_SCREEN_ROUTE: AnnouncementsScreen.route().builder,
    NOTIFICATIONS_SCREEN_ROUTE: NotificationsScreen.route().builder,

    // -- Inventory --
    INVENTORY_CONTROL_SCREEN_ROUTE: InventoryListsScreen.route().builder,

    // -- Shopping cart --
    SHOPPING_CART_OVERVIEW_SCREEN_ROUTE: CartOverviewScreen.route().builder,
    SHOPPING_CART_ORDERS_TO_APPROVE_SCREEN_ROUTE: (_) =>
        const CartOrdersToApproveScreen(),
    SHOPPING_CART_ORDERS_TO_SEND_SCREEN_ROUTE:
        CartOrdersToSendScreen.route().builder,

    // -- Advanced Search --
    CATALOG_SCREEN_ROUTE: CatalogScreen.route().builder,

    // -- Ordering --
    ORDER_LISTS_SCREEN_ROUTE: OrderListsScreen.route().builder,
    ORDERS_SCREEN_ROUTE: ordering.OrdersScreen.route().builder,

    // -- Receivings --
    PROCESS_RECEIVINGS_SCREEN_ROUTE: ProcessReceivingsScreen.route().builder,

    // -- Invoices archive --
    INVOICES_ARCHIVE_SCREEN_ROUTE: InvoicesArchiveScreen.route().builder,

    // -- Approval center --
    PURCHASE_REQUESTS_SCREEN_ROUTE: PurchaseRequestsScreen.route().builder,
    PURCHASE_REQUEST_SCREEN_ROUTE: (_) =>
        const ApprovalRequestScreen.purchaseRequest(),
    BOOKING_APPROVAL_REQUESTS_SCREEN_ROUTE:
        BookingApprovalRequestsScreen.route().builder,
    RECEIVING_APPROVAL_REQUESTS_SCREEN_ROUTE:
        ReceivingApprovalRequestsScreen.route().builder,
    BOOKING_APPROVAL_REQUEST_SCREEN_ROUTE: (_) =>
        const ApprovalRequestScreen.booking(),
    RECEIVING_APPROVAL_REQUEST_SCREEN_ROUTE: (_) =>
        const ApprovalRequestScreen.receiving(),
    CAPEX_MY_APPROVAL_REQUESTS_OVERVIEW_SCREEN_ROUTE:
        CapexApprovalRequestsScreen.route(
      initialView: CapexApprovalRequestsScreenInitialView.activeAssignedToMe,
    ).builder,
    CAPEX_APPROVAL_REQUEST_SCREEN_ROUTE: (_) =>
        const CapexApprovalRequestScreen(),
    CAPEX_APPROVAL_REQUEST_SCREEN_WITH_BAK_TO_LIST_ROUTE: (_) =>
        const CapexApprovalRequestScreen(
          isRedirected: true,
        ),
    INVOICES_SCREEN_ROUTE: InvoicesScreen.route().builder,
    INVOICE_SCREEN_ROUTE: (_) => const InvoiceScreen(),

    // -- Transfer lists --
    TRANSFER_LISTS_SCREEN_ROUTE: TransferListsScreen.route().builder,
    TRANSFER_LIST_ORDERS_SCREEN_ROUTE: TransferListOrdersScreen.route().builder,
    BOOKINGS_SCREEN_ROUTE: BookingsScreen.route().builder,

    // -- Suppliers --
    SUPPLIERS_SCREEN_ROUTE: SuppliersScreen.route().builder,
    SUPPLIER_DOCUMENTS_ROUTE: (_) => const SupplierDocumentsScreen(),

    // -- Utils --
    PIN_CONFIRMATION_SCREEN_ROUTE: (_) => const PinConfirmationScreen(),

    // -- IMS --
    IMS_MAPPING_PRODUCTS_SCREEN_ROUTE: ImsMappingProductsScreen.route().builder,
    IMS_EXPIRING_DOCUMENTS_SCREEN_ROUTE:
        ImsExpiredDocumentsScreen.route().builder,
  };

  Map<int, String> routesMapping = {
    ANNOUNCEMENTS_SCREEN_ROUTE_ID: ANNOUNCEMENTS_SCREEN_ROUTE,
    NOTIFICATIONS_SCREEN_ROUTE_ID: NOTIFICATIONS_SCREEN_ROUTE,
    INVENTORY_CONTROL_SCREEN_ROUTE_ID: INVENTORY_CONTROL_SCREEN_ROUTE,
    SHOPPING_CART_OVERVIEW_SCREEN_ROUTE_ID: SHOPPING_CART_OVERVIEW_SCREEN_ROUTE,
    SHOPPING_CART_ORDERS_TO_APPROVE_SCREEN_ROUTE_ID:
        SHOPPING_CART_ORDERS_TO_APPROVE_SCREEN_ROUTE,
    SHOPPING_CART_ORDERS_TO_SEND_SCREEN_ROUTE_ID:
        SHOPPING_CART_ORDERS_TO_SEND_SCREEN_ROUTE,
    CATALOG_SCREEN_ROUTE_ID: CATALOG_SCREEN_ROUTE,
    ORDER_LISTS_SCREEN_ROUTE_ID: ORDER_LISTS_SCREEN_ROUTE,
    ORDERS_SCREEN_ROUTE_ID: ORDERS_SCREEN_ROUTE,
    RECEIVINGS_SCREEN_ROUTE_ID: PROCESS_RECEIVINGS_SCREEN_ROUTE,
    INVOICES_ARCHIVE_SCREEN_ROUTE_ID: INVOICES_ARCHIVE_SCREEN_ROUTE,
    PURCHASE_REQUESTS_SCREEN_ROUTE_ID: PURCHASE_REQUESTS_SCREEN_ROUTE,
    BOOKING_APPROVAL_REQUESTS_SCREEN_ROUTE_ID:
        BOOKING_APPROVAL_REQUESTS_SCREEN_ROUTE,
    RECEIVING_APPROVAL_REQUESTS_SCREEN_ROUTE_ID:
        RECEIVING_APPROVAL_REQUESTS_SCREEN_ROUTE,
    INVOICES_SCREEN_ROUTE_ID: INVOICES_SCREEN_ROUTE,
    TRANSFER_LISTS_SCREEN_ROUTE_ID: TRANSFER_LISTS_SCREEN_ROUTE,
    TRANSFER_LIST_ORDERS_SCREEN_ROUTE_ID: TRANSFER_LIST_ORDERS_SCREEN_ROUTE,
    BOOKINGS_SCREEN_ROUTE_ID: BOOKINGS_SCREEN_ROUTE,
    SUPPLIERS_SCREEN_ROUTE_ID: SUPPLIERS_SCREEN_ROUTE,
    RECEIVING_ORDERS_SCREEN_ROUTE_ID: PROCESS_RECEIVINGS_SCREEN_ROUTE,
    IMS_ITEMS_MAPPING_SCREEN_ROUTE_ID: IMS_MAPPING_PRODUCTS_SCREEN_ROUTE,
    IMS_EXPIRING_DOCUMENTS_ROUTE_ID: IMS_EXPIRING_DOCUMENTS_SCREEN_ROUTE,
  };

  /// Maps `sso-redirect` param value from SSO link to the route
  Map<String, String> ssoLinksRoutesMapping = {
    'supplier-expired-documents': SUPPLIER_DOCUMENTS_ROUTE,
    'supplier-prolonged-documents': SUPPLIER_DOCUMENTS_ROUTE,
    'supplier-expiring-soon-documents': SUPPLIER_DOCUMENTS_ROUTE,
    'approve-capex': CAPEX_APPROVAL_REQUEST_SCREEN_WITH_BAK_TO_LIST_ROUTE,
    'approve-capex-documents':
        CAPEX_APPROVAL_REQUEST_SCREEN_WITH_BAK_TO_LIST_ROUTE,
    'approve-capex-messages':
        CAPEX_APPROVAL_REQUEST_SCREEN_WITH_BAK_TO_LIST_ROUTE,
    'approve-capex-overview': CAPEX_MY_APPROVAL_REQUESTS_OVERVIEW_SCREEN_ROUTE,
    'none': '',
  };

  /// The list of `sso-redirect` params where we should skip announcement
  /// screen while redirecting
  List<String> ssoLinksSkipAnnouncements = [
    'approve-capex',
    'approve-capex-overview',
  ];

  /// Maps `sso-redirect` param value from SSO link to the tab index
  Map<String, int> ssoLinksTabIndexMapping = {
    'approve-capex': CAPEX_APPROVAL_REQUEST_SCREEN_ROUTE_DEFAULT_TAB,
    'approve-capex-documents':
        CAPEX_APPROVAL_REQUEST_SCREEN_ROUTE_DOCUMENTS_TAB,
    'approve-capex-messages': CAPEX_APPROVAL_REQUEST_SCREEN_ROUTE_MESSAGES_TAB,
  };

  /// Maps sso link params to the route arguments
  Map<String, String> ssoLinksArgumentsMapping = {
    'sso-redirect': 'originalRoute',
    'sso-supplier': 'supplierId',
    'sso-betrieb': 'divisionId',
    'sso-id': 'id',
  };
}
