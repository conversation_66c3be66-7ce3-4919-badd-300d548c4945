import 'package:app/ims/models/exchange_rate.dart';
import 'package:app/ims/models/filter/mapping_products_config.dart';
import 'package:app/ims/models/filter/store_products_config.dart';
import 'package:app/ims/models/filter/stores_config.dart';
import 'package:app/ims/models/mapping_product.dart';
import 'package:app/ims/models/product.dart';
import 'package:app/ims/models/store.dart';
import 'package:app/receivings_module/models/filter/filter_value.dart';
import 'package:app/shared/config/common.dart';
import 'package:app/shared/config/errors.dart';
import 'package:app/shared/repositories/repository.dart';
import 'package:app/shared/services/api/api.dart';
import 'package:app/shared/services/preferences/cache.dart';
import 'package:app/shared/services/preferences/field.dart';
import 'package:app/shared/types/types.dart';
import 'package:app/test_module/models/test_task.dart';

class TestTaskRepository extends ApiRepository {
  TestTaskRepository({
    required this.preferencesCacheService,
    required ApiService apiService,
  }) : super(apiService);

  final PreferencesCacheService preferencesCacheService;

  Future<Result<List<TestModel>, AppError>> loadEntities({
    required Map<String, FilterValueModel> filterValues,
    required int page,
    required int pageSize,
    required String language
  }) async {
    try {
      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;
      final costCenterId =
          preferencesCacheService.getField(PreferencesField.costCenterId);

      final query = filterValues[IMS_MAPPING_PRODUCTS_FREE_TEXT_FID]!.value;

      late String orderByField;
      late String orderType;

      switch (filterValues[IMS_MAPPING_PRODUCTS_SORTING_FID]!.value) {
        case IMS_MAPPING_PRODUCTS_SORT_BY_STOCK_ITEM_ID_ASC_ID:
          orderByField = 'stock_item_id';
          orderType = 'ASC';
          break;

        case IMS_MAPPING_PRODUCTS_SORT_BY_STOCK_ITEM_ID_DESC_ID:
          orderByField = 'stock_item_id';
          orderType = 'DESC';
          break;

        case IMS_MAPPING_PRODUCTS_SORT_BY_NAME_ASC_ID:
          orderByField = 'stock_item_name';
          orderType = 'ASC';
          break;

        case IMS_MAPPING_PRODUCTS_SORT_BY_NAME_DESC_ID:
          orderByField = 'stock_item_name';
          orderType = 'DESC';
          break;

        default:
          assert(false);
          break;
      }

      
      final result = await api.searchImsMappingProducts(
        divisionId: divisionId,
        costCenterId: costCenterId,
        query: query,
        language: language,
        orderByField: orderByField,
        orderType: orderType,
        page: page,
        pageSize: pageSize,
      );

      return result.match(
        ok: (response) => Ok(
          List<Map<String, dynamic>>.from(response.getData())
              .map(TestModel.fromJson)
              .toList(),
        ),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }
}


