import 'dart:io';
import 'dart:typed_data';

import 'package:app/shared/services/api/auth/auth.dart';
import 'package:app/shared/services/api/response.dart';
import 'package:app/shared/types/types.dart';

abstract class ApiService {
  void setAuthenticator(Authenticator authenticator);

  void unsetAuthenticator();

  Future<Result<Response, AppError>> ping({
    Duration timeout = const Duration(seconds: 5),
  });

  // -- User --

  Future<Result<Response, AppError>> verifyUserBySecurityToken();

  Future<Result<Response, AppError>> loginUser({
    required String login,
    required String password,
    required String platform,
    required String platformCode,
    required String appVersion,
    String? deviceId,
    String? otpToken,
  });

  Future<Result<Response, AppError>> logoutUser();

  Future<Result<Response, AppError>> resetPassword(String login);

  Future<Result<Response, AppError>> setNewPassword({
    required String login,
    required String oldPassword,
    required String newPassword,
  });

  // -- Auth --

  Future<Result<Response, AppError>> refreshJWTToken({
    required String refreshToken,
  });

  Future<Result<Response, AppError>> getSsoIdentityProvidersList();

  Future<Result<Response, AppError>> getUserThemeConfig(String divisionId);

  Future<Result<Response, AppError>> getUserFeatureFlags({
    required String divisionId,
    required String costCenterId,
  });

  // -- Division --

  Future<Result<Response, AppError>> getOneDivisionById(String unitId);

  // -- Catalog --

  Future<Result<Response, AppError>> rebuildCatalog({
    required String divisionId,
    required String costCenterId,
  });

  Future<Result<Response, AppError>> getProductOffers({
    required String divisionId,
    required String costCenterId,
    required String metaId,
    required String language,
  });

  Future<Result<Response, AppError>> getOfferById({
    required String divisionId,
    required String costCenterId,
    required int positionId,
    required String itemType,
    required String language,
  });

  Future<Result<Response, AppError>> getCurrencies({
    required String divisionId,
  });

  // -- Cart --

  Future<Result<Response, AppError>> getProductTotals({
    required String divisionId,
    required String costCenterId,
    required int positionId,
    required String type,
  });

  Future<Result<Response, AppError>> getCartProducts({
    required String divisionId,
    required String costCenterId,
    required String language,
    required String filter,
  });

  Future<Result<Response, AppError>> getCartProductsOverview({
    required String divisionId,
    required String costCenterId,
  });

  Future<Result<Response, AppError>> getCartOrdersOverview({
    required String divisionId,
    required String costCenterId,
    required String filter,
  });

  Future<Result<Response, AppError>> getCartOrdersToApprove({
    required String divisionId,
    required String costCenterId,
    required String language,
  });

  Future<Result<Response, AppError>> requestCartOrderApproval({
    required String divisionId,
    required String costCenterId,
    required String purchaseRequestId,
    required String type,
    required String approverId,
    String? comment,
  });

  Future<Result<Response, AppError>> getCartOrdersToSend({
    required String divisionId,
    required String costCenterId,
  });

  Future<Result<Response, AppError>> sendCartOrders({
    required String divisionId,
    required String costCenterId,
    required List<Map<String, dynamic>> orders,
  });

  Future<Result<Response, AppError>> getCartNote({
    required String divisionId,
    required String costCenterId,
  });

  Future<Result<Response, AppError>> updateCartNote({
    required String divisionId,
    required String costCenterId,
    required String? note,
  });

  Future<Result<Response, AppError>> getCartTotals({
    required String divisionId,
    required String costCenterId,
  });

  Future<Result<Response, AppError>> getCartProductOtherOffers({
    required String divisionId,
    required String costCenterId,
    required String productMetaId,
  });

  Future<Result<Response, AppError>> mergeCartOrders({
    required String divisionId,
    required String costCenterId,
    required List<String> orders,
  });

  Future<Result<Response, AppError>> deleteCartProduct({
    required String divisionId,
    required String costCenterId,
    required int cartItemId,
  });

  Future<Result<Response, AppError>> deleteCartProductByPositionId({
    required String divisionId,
    required String costCenterId,
    required int positionId,
    required String type,
  });

  Future<Result<Response, AppError>> updateCartProductComment({
    required String divisionId,
    required String costCenterId,
    required int itemId,
    required String? comment,
  });

  Future<Result<Response, AppError>> updateCartOrderDeliveryDate({
    required String divisionId,
    required String costCenterId,
    required String orderTempId,
    required String deliveryDate,
  });

  Future<Result<Response, AppError>> addOrUpdateCartProduct({
    required String divisionId,
    required String costCenterId,
    required int positionId,
    required String itemType,
    required double qty,
  });

  Future<Result<Response, AppError>> addOrUpdateManyCartProducts({
    required String divisionId,
    required String costCenterId,
    required List<Map<String, dynamic>> products,
  });

  Future<Result<Response, AppError>> freeTextOrderSupplierLookup({
    required String divisionId,
    required String costCenterId,
    required String query,
    required int page,
    required int pageSize,
  });

  Future<Result<Response, AppError>> freeTextOrderProductLookup({
    required String divisionId,
    required String costCenterId,
    required String supplierId,
    required String query,
    required int page,
    required int pageSize,
  });

  Future<Result<Response, AppError>> freeTextOrderAddOrUpdateItem({
    required String divisionId,
    required String costCenterId,
    required String supplierId,
    String? productId,
    required String productName,
    required String categoryId,
    required double quantity,
    required double price,
    required String orderUnit,
    required String contentUnit,
    required double contentUnitsPerOrderUnit,
    String? itemDescription,
    String? itemNumber,
    String? inventoryUnit,
    int? vatGstRate,
  });

  Future<Result<Response, AppError>> freeTextOrderGetProductById({
    required String divisionId,
    required String costCenterId,
    required String supplierId,
    required String productId,
  });

  Future<Result<Response, AppError>> getCartAutoConsolidateCostCenters({
    required String divisionId,
    required String costCenterId,
    required String orderByField,
    required String orderType,
  });

  Future<Result<Response, AppError>> cartAutoConsolidate({
    required String divisionId,
    required String costCenterId,
    required List<String> costCenterIds,
  });

  Future<Result<Response, AppError>> cartCostTypeLookup({
    required String divisionId,
    required String costCenterId,
    required String query,
    required String? lookupDivisionId,
    required String? lookupCostCenterId,
  });

  Future<Result<Response, AppError>> updateCartProductCostType({
    required String divisionId,
    required String costCenterId,
    required String productCostCenterId,
    required int itemPositionId,
    required String itemType,
    required String costTypeId,
  });

  Future<Result<Response, AppError>> calculateCartBudget({
    required String divisionId,
    required String costCenterId,
    required String targetCostCenterId,
  });

  Future<Result<Response, AppError>> updateCartProductDiscount({
    required String divisionId,
    required String costCenterId,
    required int cartItemId,
    required double? discountPercentage,
    required double? discountAmount,
  });

  Future<Result<Response, AppError>> updateCartDiscountByOrder({
    required String divisionId,
    required String costCenterId,
    required String supplierId,
    required int leadTime,
    required String? deliveryDate,
    required double? discountPercentage,
    required double? discountAmount,
  });

  Future<Result<Response, AppError>> addCartOrderExternalComment({
    required String divisionId,
    required String costCenterId,
    required String? orderDivisionId,
    required String? orderCostCenterId,
    required String supplierId,
    required int leadTime,
    required String comment,
  });

  Future<Result<Response, AppError>> getCartExternalCommentHistory({
    required String divisionId,
    required String costCenterId,
    required String? orderDivisionId,
    required String? orderCostCenterId,
    required String supplierId,
    required int leadTime,
  });

  Future<Result<Response, AppError>> getCartPurchaseRequestExternalComments({
    required String divisionId,
    required String costCenterId,
    required String? orderDivisionId,
    required String? orderCostCenterId,
    required String purchaseRequestId,
  });

  // -- Orders --
  Future<Result<Response, AppError>> getOrderExternalCommentHistory({
    required String divisionId,
    required String costCenterId,
    required String? orderDivisionId,
    required String? orderCostCenterId,
    required String orderId,
  });

  Future<Result<Response, AppError>> getOrderInvoiceUrl({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required String language,
  });

  Future<Result<Response, AppError>> getOneOrderById({
    required String divisionId,
    required String costCenterId,
    required String orderCostCenterId,
    required String orderId,
    required String language,
    required bool includeMergedComments,
  });

  Future<Result<Response, AppError>> searchOrders({
    required String divisionId,
    required String costCenterKey,
    required String query,
    required String? costCenterId,
    required String? orderer,
    required String? fromDate,
    required String? toDate,
    required String filter,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
  });

  Future<Result<Response, AppError>> searchOrderProducts({
    required String divisionId,
    required String costCenterId,
    required String orderCostCenterId,
    required String orderId,
    required String query,
    required String language,
    required String filter,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
  });

  Future<Result<Response, AppError>> getOrderTargetCostCenters({
    required String divisionId,
    required String costCenterId,
    required String orderId,
  });

  Future<Result<Response, AppError>> sendMessageToOrderSupplier({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required String replyToEmail,
    required String message,
  });

  Future<Result<Response, AppError>> getProductsToOrderAgain({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required String language,
  });

  Future<Result<Response, AppError>> getProductLastOrders({
    required String divisionId,
    required String metaId,
  });

  Future<Result<Response, AppError>> orderersLookup({
    required String divisionId,
    required String costCenterId,
    required String query,
    required int page,
    required int pageSize,
  });

  Future<Result<Response, AppError>> ordersCostCentersLookup({
    required String divisionId,
    required String costCenterId,
    required String query,
    required int page,
    required int pageSize,
  });

  Future<Result<Response, AppError>> searchAstoreOrders({
    required String divisionId,
    required String costCenterKey,
    required String query,
    required String? supplier,
    required String? orderId,
    required String? costCenterName,
    required String? fromOrderDate,
    required String? toOrderDate,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
    required String language,
  });

  Future<Result<Response, AppError>> getAstoreOrdersCountOfNotProcessed({
    required String divisionId,
    required String costCenterKey,
  });

  Future<Result<Response, AppError>> searchAstoreOrderProducts({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required String query,
    required String orderByField,
    required String orderType,
    required String language,
    required int page,
    required int pageSize,
  });

  // -- Transfer Lists --

  Future<Result<Response, AppError>> renameTransferList({
    required String divisionId,
    required String costCenterId,
    required String listId,
    required String newName,
  });

  Future<Result<Response, AppError>> placeTransferListOrder({
    required String divisionId,
    required String listId,
    required String deliveryDate,
    required String? comment,
    required List<Map<String, dynamic>> items,
  });

  Future<Result<Response, AppError>> getTransferListOrderDocumentUrl({
    required String divisionId,
    required String bookingId,
    required String language,
  });

  Future<Result<Response, AppError>> searchTransferLists({
    required String divisionId,
    required String costCenterId,
    required String query,
    required String filter,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
    bool includeItems = false,
  });

  Future<Result<Response, AppError>> searchTransferListItems({
    required String divisionId,
    required String costCenterId,
    required String listId,
    required String query,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
  });

  Future<Result<Response, AppError>> getAllTransferListItems({
    required String divisionId,
    required String costCenterId,
    required String listId,
    required String language,
  });

  Future<Result<Response, AppError>> getOneTransferListItemById({
    required String divisionId,
    required String costCenterId,
    required String listId,
    required int listItemId,
    required String language,
  });

  Future<Result<Response, AppError>> updateTransferListItem({
    required String divisionId,
    required String costCenterId,
    required String listId,
    required int listItemId,
    required Map<String, dynamic> fields,
  });

  Future<Result<Response, AppError>> removeTransferListItem({
    required String divisionId,
    required String costCenterId,
    required String listId,
    required int listItemId,
  });

  Future<Result<Response, AppError>> updateTransferListItemsOrder({
    required String divisionId,
    required String costCenterId,
    required String listId,
    required List<int> listItemIds,
  });

  Future<Result<Response, AppError>> getOneTransferListsById({
    required String divisionId,
    required String listId,
    bool includeItems = false,
  });

  Future<Result<Response, AppError>> searchTransferListsOrders({
    required String divisionId,
    required String costCenterKey,
    required String? costCenterId,
    required String query,
    required String status,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
  });

  Future<Result<Response, AppError>> searchTransferListOrderItems({
    required String divisionId,
    required String costCenterId,
    required String bookingId,
    required String query,
    required String filter,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
    required String language,
  });

  Future<Result<Response, AppError>> lookupTransferListCostCenter({
    required String divisionId,
    required String costCenterId,
    required String query,
    required int page,
    required int pageSize,
  });

  Future<Result<Response, AppError>>
      lookupTransferListInterPropertyTargetDivision({
    required String divisionId,
    required String costCenterId,
    required String query,
    required int page,
    required int pageSize,
  });

  Future<Result<Response, AppError>> createTransferList({
    required String divisionId,
    required String costCenterId,
    required String listName,
    required String costCenterAccountancyId,
    required bool isEditable,
    required String type,
  });

  Future<Result<Response, AppError>> deleteTransferList({
    required String divisionId,
    required String costCenterId,
    required String listId,
  });

  Future<Result<Response, AppError>> lookupInHouseListItemToAdd({
    required String divisionId,
    required String costCenterId,
    required String listId,
    required String query,
    required int page,
    required int pageSize,
    required String language,
  });

  Future<Result<Response, AppError>> addTransferListItem({
    required String divisionId,
    required String costCenterId,
    required String listId,
    required String stockItemId,
    String? sourceStockId,
    String? targetStockId,
    String? incomingCostTypeId,
    String? sourceDivisionId,
  });

  Future<Result<Response, AppError>> lookupInterPropertySourceDivision({
    required String divisionId,
    required String costCenterId,
    required String query,
    required int page,
    required int pageSize,
  });

  Future<Result<Response, AppError>> getOneInterPropertyItemToAddById({
    required String divisionId,
    required String costCenterId,
    required String listId,
    required String sourceDivisionId,
    required String sourceStockItemId,
  });

  Future<Result<Response, AppError>> lookupInterPropertyItemToAdd({
    required String divisionId,
    required String costCenterId,
    required String listId,
    required String sourceDivisionId,
    required String query,
    required int page,
    required int pageSize,
  });

  Future<Result<Response, AppError>> addInterPropertyItemToWarehouse({
    required String divisionId,
    required String costCenterId,
    required String listId,
    required String sourceDivisionId,
    required String sourceStockItemId,
    required String targetStockId,
    required String targetCostTypeId,
  });

  Future<Result<Response, AppError>> lookupInterPropertySyncedItem({
    required String divisionId,
    required String costCenterId,
    required String listId,
    required String stockItemId,
    required String query,
    required int page,
    required int pageSize,
  });

  Future<Result<Response, AppError>> remapInterPropertyItem({
    required String divisionId,
    required String costCenterId,
    required String listId,
    required String stockItemId,
    required String sourceDivisionId,
    required String sourceStockItemId,
  });

  Future<Result<Response, AppError>> lookupInterPropertyItemToMerge({
    required String divisionId,
    required String costCenterId,
    required String listId,
    required String sourceDivisionId,
    required String sourceStockItemId,
    required String query,
    required int page,
    required int pageSize,
  });

  Future<Result<Response, AppError>> mergeInterPropertyItem({
    required String divisionId,
    required String costCenterId,
    required String listId,
    required String stockItemId,
    required String sourceDivisionId,
    required String sourceStockItemId,
  });

  Future<Result<Response, AppError>> getOneTransferListProductById({
    required String divisionId,
    required String costCenterId,
    required String stockItemId,
  });

  Future<Result<Response, AppError>> updateTransferListProduct({
    required String divisionId,
    required String costCenterId,
    required String stockItemId,
    required Map<String, dynamic> fields,
  });

  // -- Inventories --

  Future<Result<Response, AppError>> addInventoryItem({
    required String divisionId,
    required String inventoryId,
    required String stockId,
  });

  Future<Result<Response, AppError>> searchInventoryLists({
    required String divisionId,
    required String query,
    required String filter,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
  });

  Future<Result<Response, AppError>> getInventoryListById({
    required String divisionId,
    required String id,
    required bool includeItems,
  });

  Future<Result<Response, AppError>> lockInventoryControlList({
    required String divisionId,
    required String inventoryId,
  });

  Future<Result<Response, AppError>> unlockInventoryControlList({
    required String divisionId,
    required String inventoryId,
    required bool forceUnlock,
  });

  Future<Result<Response, AppError>> searchInventoryItems({
    required String divisionId,
    required String inventoryId,
    required String query,
    required String filter,
    required String orderByField,
    required String orderType,
    required String? categoryId,
    required int page,
    required int pageSize,
  });

  Future<Result<Response, AppError>> updateInventoryItemQty({
    required String divisionId,
    required String inventoryId,
    required String productStockId,
    required double qty,
  });

  Future<Result<Response, AppError>> overwriteInventoryItemEanCodes({
    required String divisionId,
    required String inventoryId,
    required String productStockId,
    required List<String> eanCodes,
  });

  Future<Result<Response, AppError>> confirmInventoryControlList({
    required String inventoryId,
    required String divisionId,
    required String firstApprover,
    required String firstSignature,
    required String? secondApprover,
    required String? secondSignature,
  });

  Future<Result<Response, AppError>> inventoryCategoryLookup({
    required String divisionId,
    required String inventoryId,
    required String query,
    required int page,
    required int pageSize,
  });

  Future<Result<Response, AppError>> getInventoryBookingVoucherUrl({
    required String divisionId,
    required String costCenterId,
    required String bookingId,
    required String language,
  });

  // --- Supplier ---

  Future<Result<Response, AppError>> searchSupplier({
    required String divisionId,
    required String costCenterId,
    required String query,
    required String filter,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
  });

  Future<Result<Response, AppError>> favoriteSupplier({
    required String divisionId,
    required String costCenterId,
    required String supplierId,
    required bool isFavorite,
  });

  Future<Result<Response, AppError>> getSupplierDeliveryPlan({
    required String divisionId,
    required String costCenterId,
    required String supplierId,
    required int leadTime,
    required String orderDate,
    required String period,
  });

  Future<Result<Response, AppError>> getSupplierById({
    required String divisionId,
    required String costCenterId,
    required String supplierId,
  });

  Future<Result<Response, AppError>> requestCustomerIdFromSupplier({
    required String divisionId,
    required String costCenterId,
    required String supplierId,
    required String? replyToEmail,
  });

  Future<Result<Response, AppError>> enableSendOrderAfterApprovalForSupplier({
    required String divisionId,
    required String costCenterId,
    required String supplierId,
    required bool enabled,
  });

  Future<Result<Response, AppError>> searchSupplierDocuments({
    required String divisionId,
    required String costCenterId,
    required String? supplierId,
    required String? query,
    required String? category,
    required String? documentType,
    required String status,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
  });

  Future<Result<Response, AppError>> getSupplierDocumentById({
    required String divisionId,
    required String costCenterId,
    required int documentId,
    required bool includeFiles,
  });

  Future<Result<Response, AppError>> getAllSupplierDocumentFiles({
    required String divisionId,
    required String costCenterId,
    required int documentId,
  });

  Future<Result<Response, AppError>> getSupplierDocumentFileById({
    required String divisionId,
    required String costCenterId,
    required String documentFileId,
  });

  Future<Result<Response, AppError>> getSupplierDocumentFileUrl({
    required String divisionId,
    required String costCenterId,
    required String documentFileId,
  });

  Future<Result<Response, AppError>> searchSupplierDocumentProducts({
    required String divisionId,
    required String query,
    required String supplierId,
    required int documentId,
    required List<String> categories,
    required String assignmentStatus,
    required String fileStatus,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
    required String costCenterId,
    required String language,
    required bool includeFiles,
  });

  Future<Result<Response, AppError>> getAllSupplierDocumentProductFiles({
    required String divisionId,
    required String costCenterId,
    required int documentId,
    required int positionId,
    required String? itemType,
  });

  Future<Result<Response, AppError>> getAllSupplierDocumentProductDocuments({
    required String divisionId,
    required String costCenterId,
    required int positionId,
    required String? itemType,
    required bool includeFiles,
  });

  // -- Announcements --

  Future<Result<Response, AppError>> getUnreadAnnouncements(String divisionId);

  Future<Result<Response, AppError>> markAnnouncementAsRead({
    required String divisionId,
    required int announcementId,
  });

  // -- Warehouse --

  Future<Result<Response, AppError>> lookupWarehouseItem({
    required String divisionId,
    required String scope,
    required String query,
    required int page,
    required int pageSize,
    required String language,
  });

  Future<Result<Response, AppError>> lookupWarehouseStores({
    required String divisionId,
    String? lookupInDivisionId,
    required String? storeCostCenterId,
    required String query,
    required int page,
    required int pageSize,
    required String language,
  });

  Future<Result<Response, AppError>> lookupWarehouseCostTypes({
    required String divisionId,
    String? lookupInDivisionId,
    required String? storeCostCenterId,
    required String? storeId,
    required String query,
    required int page,
    required int pageSize,
    required String language,
  });

  Future<Result<Response, AppError>> lookupWarehouseCostCenters({
    required String divisionId,
    String? lookupInDivisionId,
    required String query,
    required int page,
    required int pageSize,
    required String language,
  });

  Future<Result<Response, AppError>> lookupWarehouseLocalSuppliers({
    required String divisionId,
    required String query,
    required int page,
    required int pageSize,
    required String language,
  });

  Future<Result<Response, AppError>> lookupWarehouseVat({
    required String divisionId,
    required String query,
    required int page,
    required int pageSize,
    required String language,
  });

  Future<Result<Response, AppError>> lookupWarehouseCategories({
    required String divisionId,
    required String query,
    required int page,
    required int pageSize,
    required String language,
  });

  Future<Result<Response, AppError>> lookupWarehousePackingUnits({
    required String divisionId,
    required String query,
    required int page,
    required int pageSize,
    required String language,
  });

  Future<Result<Response, AppError>> lookupWarehouseInventoryUnit({
    required String divisionId,
    required String query,
    required int page,
    required int pageSize,
    required String language,
  });

  Future<Result<Response, AppError>> lookupWarehouseDepositItem({
    required String divisionId,
    required String query,
    required int page,
    required int pageSize,
    required String language,
  });

  Future<Result<Response, AppError>> lookupWarehouseConfig({
    required String divisionId,
    required String scope,
    required String query,
    required int page,
    required int pageSize,
    required String language,
  });

  Future<Result<Response, AppError>> lookupWarehouseCurrency({
    required String divisionId,
    required String query,
    required int page,
    required int pageSize,
    required String language,
  });

  // -- i18n

  Future<Result<Response, AppError>> pushLabelsIds(
    List<Map<String, String>> labels,
  );

  // -- Bookings --

  Future<Result<Response, AppError>> searchBookings({
    required String divisionId,
    required String costCenterId,
    required String query,
    required String status,
    required String? fromDate,
    required String? toDate,
    required String orderByField,
    required String orderType,
    required List<String> transferType,
    required int page,
    required int pageSize,
    bool onlyThisCostCenter = false,
    bool includeProducts = false,
  });

  Future<Result<Response, AppError>> getOneBookingById({
    required String divisionId,
    required String bookingId,
    bool includeProducts = false,
  });

  Future<Result<Response, AppError>> deleteBooking({
    required String divisionId,
    required String id,
  });

  Future<Result<Response, AppError>> updateBooking({
    required String divisionId,
    required String bookingId,
    required Map<String, dynamic> fields,
  });

  Future<Result<Response, AppError>> confirmBooking({
    required String divisionId,
    required String bookingId,
  });

  Future<Result<Response, AppError>> bookingReasonLookup({
    required String divisionId,
    required String transferType,
    required String query,
    required int page,
    required int pageSize,
  });

  Future<Result<Response, AppError>> createBooking({
    required String divisionId,
    required String constCenterId,
    required String transferType,
    required String bookingDate,
    required String reasonId,
    required String language,
    required String reference,
  });

  Future<Result<Response, AppError>> getBookingVoucherUrl({
    required String divisionId,
    required String bookingId,
    required String language,
  });

  Future<Result<Response, AppError>> searchBookingItems({
    required String divisionId,
    required String costCenterId,
    required String bookingId,
    required String query,
    required String filter,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
  });

  Future<Result<Response, AppError>> getAllBookingItems({
    required String divisionId,
    required String bookingId,
    required String language,
  });

  Future<Result<Response, AppError>> getOneBookingItemById({
    required String divisionId,
    required String bookingId,
    required int id,
    required String language,
  });

  Future<Result<Response, AppError>> deleteBookingItem({
    required String divisionId,
    required int id,
    required String bookingId,
  });

  Future<Result<Response, AppError>> updateBookingItem({
    required String divisionId,
    required String bookingId,
    required int id,
    required Map<String, dynamic> fields,
  });

  Future<Result<Response, AppError>> addBookingItem({
    required String divisionId,
    required String bookingId,
    required String productStockId,
  });

  Future<Result<Response, AppError>> confirmBookingItem({
    required String divisionId,
    required String bookingId,
    required int id,
  });

  // -- Booking approval requests --

  Future<Result<Response, AppError>> requestBookingApproval({
    required String divisionId,
    required String approvalRequestId,
    required String approverId,
  });

  Future<Result<Response, AppError>> searchBookingApprovalRequests({
    required String divisionId,
    required String costCenterKey,
    required String query,
    required String? fromDate,
    required String? toDate,
    required String filter,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
    required String language,
  });

  Future<Result<Response, AppError>> updateBookingApprovalRequest({
    required String divisionId,
    required String approvalRequestId,
    required bool isApproved,
    required String? comment,
    required String? nextApproverId,
  });

  Future<Result<Response, AppError>> getOneBookingApprovalRequestById({
    required String divisionId,
    required String costCenterId,
    required String approvalRequestId,
    required String language,
    bool includeProducts = false,
    bool includeLogRecords = false,
    bool includeMessages = false,
  });

  Future<Result<Response, AppError>> getBookingApprovalRequestProducts({
    required String divisionId,
    required String costCenterId,
    required String approvalRequestId,
    required String language,
  });

  Future<Result<Response, AppError>> searchBookingApprovalRequestProducts({
    required String divisionId,
    required String costCenterId,
    required String approvalRequestId,
    required String language,
    required String query,
    required int page,
    required int pageSize,
  });

  Future<Result<Response, AppError>> getOneBookingApprovalRequestProductById({
    required String divisionId,
    required String costCenterId,
    required String approvalRequestId,
    required int productId,
    required String language,
  });

  Future<Result<Response, AppError>> resetBookingApprovalRequest({
    required String divisionId,
    required String approvalRequestId,
  });

  Future<Result<Response, AppError>> updateBookingApprovalRequestProductQty({
    required String divisionId,
    required String approvalRequestId,
    required int productId,
    required double orderUnitQty,
  });

  Future<Result<Response, AppError>> updateBookingApprovalRequestProduct({
    required String divisionId,
    required String approvalRequestId,
    required int productId,
    required bool isConfirmed,
  });

  Future<Result<Response, AppError>> pullBookingApprovalRequestMessages({
    required String divisionId,
    required String approvalRequestId,
    required int? pullFromMessageId,
    required int size,
    required String order,
  });

  Future<Result<Response, AppError>> sendBookingApprovalRequestMessage({
    required String divisionId,
    required String approvalRequestId,
    required String message,
  });

  Future<Result<Response, AppError>> getBookingApprovalRequestLogRecords({
    required String divisionId,
    required String approvalRequestId,
  });

  Future<Result<Response, AppError>> getBookingApprovalRequestDocuments({
    required String divisionId,
    required String approvalRequestId,
  });

  Future<Result<Response, AppError>> getBookingApprovalRequestApprovalTrail({
    required String divisionId,
    required String approvalRequestId,
  });

  Future<Result<Response, AppError>> deleteBookingApprovalRequestDocument({
    required String divisionId,
    required String approvalRequestId,
    required String documentId,
  });

  // -- Purchase requests --

  Future<Result<Response, AppError>> searchPurchaseRequests({
    required String divisionId,
    required String constCenterId,
    required String? query,
    required String? costCenterName,
    required String? category,
    required String? supplier,
    required String? itemDescription,
    required String status,
    required String? fromDate,
    required String? toDate,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
    bool includeFilesCount = false,
  });

  Future<Result<Response, AppError>> searchPurchaseRequestProducts({
    required String divisionId,
    required String purchaseRequestId,
    required String language,
    required String query,
    required int page,
    required int pageSize,
    required bool includeFilesCount,
    required bool includeFiles,
  });

  Future<Result<Response, AppError>> getOnePurchaseRequestById({
    required String divisionId,
    required String constCenterId,
    required String purchaseRequestId,
    String language = 'de_DE',
    bool includeProducts = false,
    bool includeLogRecords = false,
    bool includeMessages = false,
    bool includeFiles = false,
  });

  Future<Result<Response, AppError>> getOnePurchaseRequestProductById({
    required String divisionId,
    required String purchaseRequestId,
    required int productId,
    required String language,
    required bool includeFiles,
    required bool includeFilesCount,
  });

  Future<Result<Response, AppError>> getPurchaseRequestLogRecords({
    required String divisionId,
    required String purchaseRequestId,
  });

  Future<Result<Response, AppError>> getPurchaseRequestDocuments({
    required String divisionId,
    required String purchaseRequestId,
  });

  Future<Result<Response, AppError>> getPurchaseRequestApprovalTrail({
    required String divisionId,
    required String purchaseRequestId,
  });

  Future<Result<Response, AppError>> deletePurchaseRequestDocument({
    required String divisionId,
    required String purchaseRequestId,
    required String documentId,
  });

  Future<Result<Response, AppError>> getPurchaseRequestMessages({
    required String divisionId,
    required String purchaseRequestId,
    required int lastMessageId,
    String? olderThan,
    int page = 0,
    int pageSize = 20,
  });

  Future<Result<Response, AppError>> pullPurchaseRequestMessages({
    required String divisionId,
    required String purchaseRequestId,
    int? pullFromMessageId,
    int size = 10,
    required String order,
  });

  Future<Result<Response, AppError>> sendPurchaseRequestMessage({
    required String divisionId,
    required String purchaseRequestId,
    required String message,
  });

  Future<Result<Response, AppError>> resetPurchaseRequest({
    required String divisionId,
    required String purchaseRequestId,
  });

  Future<Result<Response, AppError>> deletePurchaseRequest({
    required String divisionId,
    required String purchaseRequestId,
  });

  Future<Result<Response, AppError>> movePurchaseRequestToCart({
    required String divisionId,
    required String purchaseRequestId,
  });

  Future<Result<Response, AppError>> updatePurchaseRequestProduct({
    required String divisionId,
    required String purchaseRequestId,
    required int productId,
    required bool isConfirmed,
  });

  Future<Result<Response, AppError>> updatePurchaseRequestProductQty({
    required String divisionId,
    required String purchaseRequestId,
    required int productId,
    required double orderUnitQty,
  });

  Future<Result<Response, AppError>> updatePurchaseRequest({
    required String divisionId,
    required String purchaseRequestId,
    required bool isApproved,
    String? comment,
    String? nextApproverId,
  });

  Future<Result<Response, AppError>> requestPurchaseRequestApproval({
    required String divisionId,
    required String purchaseRequestId,
    required String type,
    required String approverId,
  });

  Future<Result<Response, AppError>> getPurchaseRequestProductOffers({
    required String divisionId,
    required String purchaseRequestId,
    required String language,
    required String metaId,
  });

  Future<Result<Response, AppError>> changePurchaseRequestProductOffer({
    required String divisionId,
    required String purchaseRequestId,
    required String language,
    required String metaId,
    required String replacementOfferType,
    required int replacementOfferPositionId,
    required String originalType,
    required int originalPositionId,
  });

  Future<Result<Response, AppError>> searchPurchaseRequestProductLog({
    required String divisionId,
    required String purchaseRequestId,
    required int id,
    required int page,
    required int pageSize,
  });

  Future<Result<Response, AppError>> getPurchaseRequestProductOfferById({
    required String divisionId,
    required String purchaseRequestId,
    required int id,
    required String language,
  });

  Future<Result<Response, AppError>> updatePurchaseRequestProductOffer({
    required String divisionId,
    required String purchaseRequestId,
    required int id,
    required Map<String, dynamic> fields,
  });

  Future<Result<Response, AppError>> addPurchaseRequestProductOffer({
    required String divisionId,
    required String purchaseRequestId,
    required int id,
    required String supplierId,
    required String supplierProductId,
    required String orderUnit,
    required String contentUnit,
    required double contentUnitsPerOrderUnit,
    required double inventoryUnitsPerOrderUnit,
    required double orderUnitPrice,
    String? ean,
    int? taxId,
  });

  Future<Result<Response, AppError>> replacePurchaseRequestProductOffer({
    required String divisionId,
    required String purchaseRequestId,
    required int id,
    required int replacementId,
  });

  Future<Result<Response, AppError>> purchaseRequestProductSupplierLookup({
    required String divisionId,
    required String query,
    required int page,
    required int pageSize,
    required String language,
  });

  Future<Result<Response, AppError>>
      purchaseRequestProductOfferReplacementLookup({
    required String divisionId,
    required String purchaseRequestId,
    required int id,
    required String query,
    required int page,
    required int pageSize,
    required String language,
  });

  Future<Result<Response, AppError>> sendPurchaseRequestOrders({
    required String divisionId,
    required String purchaseRequestId,
    required String language,
  });

  Future<Result<Response, AppError>> purchaseRequestCostTypeLookup({
    required String divisionId,
    required String costCenterId,
    required String query,
    required String? lookupDivisionId,
    required String? lookupCostCenterId,
  });

  Future<Result<Response, AppError>> updatePurchaseRequestProductCostType({
    required String divisionId,
    required String costCenterId,
    required String productCostCenterId,
    required int itemPositionId,
    required String itemType,
    required String costTypeId,
    required String purchaseRequestId,
    required int productId,
  });

  Future<Result<Response, AppError>> calculatePurchaseRequestBudget({
    required String divisionId,
    required String costCenterId,
    required String targetCostCenterId,
    required String purchaseRequestId,
  });

  Future<Result<Response, AppError>> getPurchaseRequestExternalCommentHistory({
    required String divisionId,
    required String costCenterId,
    required String? orderDivisionId,
    required String? orderCostCenterId,
    required String supplierId,
    required String purchaseRequestId,
    required int leadTime,
  });

  // -- Capex --

  Future<Result<Response, AppError>> searchCapexApprovalRequests({
    required String orgUnitKey,
    required String costCenterKey,
    required String query,
    required String? originalDivisionId,
    required String filter,
    String? fromDate,
    String? toDate,
    required String orderByField,
    required String orderType,
    int page = 0,
    int pageSize = 10,
    required String language,
  });

  Future<Result<Response, AppError>> getOneCapexApprovalRequestById({
    required String orgUnitKey,
    required String costCenterKey,
    required String approvalRequestId,
    required String language,
    bool includeApprovalTrail = false,
    bool includeMessages = false,
    bool includeLog = false,
    bool includeFiles = false,
    bool includeInvoices = false,
    bool includeAttributes = false,
    bool includeAdditionalSuppliers = false,
  });

  Future<Result<Response, AppError>> getCapexApprovalRequestLogRecords({
    required String orgUnitKey,
    required String approvalRequestId,
    required String language,
  });

  Future<Result<Response, AppError>> getCapexApprovalRequestDocuments({
    required String orgUnitKey,
    required String approvalRequestId,
    required String language,
  });

  Future<Result<Response, AppError>> getCapexApprovalRequestTrail({
    required String orgUnitKey,
    required String approvalRequestId,
    required String language,
  });

  Future<Result<Response, AppError>> deleteCapexApprovalRequestDocument({
    required String orgUnitKey,
    required String approvalRequestId,
    required String documentId,
  });

  Future<Result<Response, AppError>> pullCapexApprovalRequestMessages({
    required String orgUnitKey,
    required String approvalRequestId,
    int? pullFromMessageId,
    int size = 10,
    required String order,
  });

  Future<Result<Response, AppError>> sendCapexApprovalRequestMessage({
    required String orgUnitKey,
    required String approvalRequestId,
    required String message,
  });

  Future<Result<Response, AppError>> resetCapexApprovalRequest({
    required String orgUnitKey,
    required String approvalRequestId,
  });

  Future<Result<Response, AppError>> deleteCapexApprovalRequest({
    required String orgUnitKey,
    required String approvalRequestId,
  });

  Future<Result<Response, AppError>> updateCapexApprovalRequest({
    required String orgUnitKey,
    required String approvalRequestId,
    required Map<String, dynamic> fields,
  });

  Future<Result<Response, AppError>> requestCapexApproval({
    required String orgUnitKey,
    required String approvalRequestId,
    required String approverId,
  });

  Future<Result<Response, AppError>> approveCapexApprovalRequest({
    required String orgUnitKey,
    required String costCenterKey,
    required String approvalRequestId,
    String? nextApproverId,
    String? comment,
  });

  Future<Result<Response, AppError>> declineCapexApprovalRequest({
    required String orgUnitKey,
    required String costCenterKey,
    required String approvalRequestId,
    String? comment,
  });

  Future<Result<Response, AppError>> closeCapexApprovalRequest({
    required String orgUnitKey,
    required String costCenterKey,
    required String approvalRequestId,
  });

  Future<Result<Response, AppError>> createCapexApprovalRequest({
    required String orgUnitKey,
    required String costCenterKey,
    required String costCenterAccountancyId,
    required String supplierId,
    required String name,
    required double totalAmount,
    required String currencyCode,
    required String capexTypeId,
    String? description,
    String? accountingInstruction,
  });

  Future<Result<Response, AppError>> capexApprovalRequestDivisionFilterLookup({
    required String divisionId,
    required int page,
    required int pageSize,
    required String query,
  });

  // -- Invoices --

  Future<Result<Response, AppError>> searchInvoices({
    required String orgUnitKey,
    required String costCenterKey,
    String query = '',
    String invoiceId = '',
    String supplierInvoiceId = '',
    String supplierName = '',
    required String filter,
    required String? fromDate,
    required String? toDate,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
  });

  Future<Result<Response, AppError>> searchDigitalInvoicesArchive({
    required String orgUnitKey,
    required String costCenterKey,
    String query = '',
    String invoiceId = '',
    String supplierInvoiceId = '',
    String supplierName = '',
    required String filter,
    required String? fromDate,
    required String? toDate,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
  });

  Future<Result<Response, AppError>> getOneInvoiceById({
    required String orgUnitKey,
    required String invoiceId,
    bool includeAccountAssignmentRecords = false,
    bool includeLogRecords = false,
    bool includeFiles = false,
  });

  Future<Result<Response, AppError>> getOneDigitalInvoiceByIdFromArchive({
    required String orgUnitKey,
    required String costCenterKey,
    required String invoiceId,
    bool includeAccountAssignmentRecords = false,
    bool includeLogRecords = false,
    bool includeFiles = false,
  });

  Future<Result<Response, AppError>> getInvoiceAccountAssignmentRecords({
    required String orgUnitKey,
    required String costCenterKey,
    required String invoiceId,
  });

  Future<Result<Response, AppError>> getInvoiceLogRecords({
    required String orgUnitKey,
    required String invoiceId,
  });

  Future<Result<Response, AppError>> getArchiveInvoiceLogRecords({
    required String orgUnitKey,
    required String invoiceId,
  });

  Future<Result<Response, AppError>> getDigitalInvoiceFileUrl({
    required String orgUnitKey,
    required String costCenterKey,
    required String scanningId,
    required String language,
  });

  Future<Result<Response, AppError>> requestInvoiceApproval({
    required String orgUnitKey,
    required String invoiceId,
    required String approverId,
  });

  Future<Result<Response, AppError>> resetInvoice({
    required String orgUnitKey,
    required String invoiceId,
    required String comment,
  });

  Future<Result<Response, AppError>> approveInvoice({
    required String orgUnitKey,
    required String invoiceId,
    String? nextApproverId,
  });

  Future<Result<Response, AppError>> getInvoiceApprovalTrail({
    required String divisionId,
    required String invoiceId,
  });

  Future<Result<Response, AppError>> approveDigitalInvoice({
    required String divisionId,
    required String invoiceId,
  });

  Future<Result<Response, AppError>> getDigitalInvoicesProcessingList({
    required String divisionId,
    required String? startDate,
    required String? endDate,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
  });

  Future<Result<Response, AppError>> getOneDigitalInvoiceProcessingById({
    required String divisionId,
    required String scanningId,
  });

  Future<Result<Response, AppError>> getDigitalInvoiceTaxBreakdown({
    required String divisionId,
    required String invoiceId,
  });

  // -- Approvals --

  Future<Result<Response, AppError>> getCountOfNotApprovedApprovals({
    required String orgUnitKey,
    required String costCenterKey,
    bool includePurchaseRequests = false,
    bool includeInvoices = false,
    bool includeBookingApprovalRequests = false,
    bool includeReceivingApprovalRequests = false,
    bool includeCapexApprovalRequests = false,
  });

  Future<Result<Response, AppError>> approversLookup({
    required String divisionId,
    required String costCenterId,
    required String id,
    required String type,
    required int level,
  });

  Future<Result<bool, AppError>> uploadInvoice({
    required String unitId,
    required String filePath,
  });

  // -- Session --

  Future<Result<Response, AppError>> logSession({
    required String orgUnitKey,
    required String costCenterKey,
    required String platform,
    required String appVersion,
    required String platformCode,
    String? deviceId,
  });

  Future<Result<Response, AppError>> getSessionPermissions(String orgUnitKey);

  Future<Result<Response, AppError>> refreshSession();

  // --- Receivings ---

  Future<Result<Response, AppError>> searchAllReceivings({
    required String orgUnitKey,
    required String costCenterKey,
    String query = '',
    String supplierName = '',
    String productName = '',
    String userName = '',
    String costCenterName = '',
    String requestedByCostCenterName = '',
    required String language,
    String? fromDate,
    String? toDate,
    required String type,
    required String filter,
    required String orderByField,
    required String orderType,
    int page = 0,
    int pageSize = 20,
  });

  Future<Result<Response, AppError>> searchReceivingOrders({
    required String orgUnitKey,
    required String costCenterKey,
    String query = '',
    String orderId = '',
    String supplierName = '',
    String productName = '',
    String userName = '',
    String costCenterName = '',
    String requestedByCostCenterName = '',
    required String language,
    String? fromDate,
    String? toDate,
    String? deliveryFromDate,
    String? deliveryToDate,
    required String filter,
    required String orderByField,
    required String orderType,
    int page = 0,
    int pageSize = 20,
  });

  Future<Result<Response, AppError>> getOneReceivingById({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required bool includeFiles,
    required bool includeItemsTotal,
    required bool includeSplitDeliveryReport,
    required bool includeProvisionalBooking,
    required bool includeTaxBreakdown,
    required bool includeCostTypeBreakdown,
    required bool includeFreightCostsOrderTotal,
    required bool includeRelatedOrders,
  });

  Future<Result<Response, AppError>> createFromOrder({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    String? deliveryNoteUid,
    required String language,
  });

  Future<Result<Response, AppError>> createReceiving({
    required String divisionId,
    required String costCenterId,
    required String supplierId,
    String? accountancyCostCenterId,
  });

  Future<Result<Response, AppError>> updateReceiving({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required Map<String, dynamic> fields,
  });

  Future<Result<Response, AppError>> getOneReceivingDepositItemById({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required String depositItemId,
  });

  Future<Result<Response, AppError>> getReceivingItemsTotals({
    required String divisionId,
    required String costCenterId,
    required String orderId,
  });

  Future<Result<Response, AppError>> addReceivingItemAttributeValue({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required int itemId,
    required String type,
    required dynamic value,
  });

  Future<Result<bool, AppError>> addReceivingItemFile({
    required String divisionId,
    required String orderId,
    required int itemId,
    required File file,
    String? comment,
  });

  Future<Result<Response, AppError>> getReceivingItemFiles({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required int itemId,
  });

  Future<Result<Response, AppError>> deleteReceivingItemFile({
    required String divisionId,
    required String costCenterId,
    required String fileId,
  });

  Future<Result<Response, AppError>> lookupReceivingItemInventoryUnits({
    required String divisionId,
    required String costCenterId,
    required String stockItemId,
    required int page,
    required int pageSize,
  });

  Future<Result<Response, AppError>> confirmReceiving({
    required String divisionId,
    required String costCenterId,
    required String orderId,
  });

  Future<Result<Response, AppError>> requestReceivingApproval({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required String approverId,
  });

  Future<Result<Response, AppError>> deleteReceivingByOrderId({
    required String divisionId,
    required String costCenterId,
    required String orderId,
  });

  Future<Result<Response, AppError>> updateReceivingDiscount({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required double discount,
  });

  Future<Result<Response, AppError>> searchReceivingItems({
    required String orgUnitKey,
    required String costCenterKey,
    required String orderId,
    String query = '',
    required String orderByField,
    required String orderType,
    int page = 0,
    int pageSize = 20,
  });

  Future<Result<Response, AppError>> lookupReceivingItems({
    required String orgUnitKey,
    required String costCenterKey,
    required String orderId,
    String query = '',
    required String scope,
    required String language,
    int page = 0,
    int pageSize = 20,
  });

  Future<Result<Response, AppError>> addItemToReceiving({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required String scope,
    required String itemType,
    required String internalId,
    required int positionId,
    required String language,
  });

  Future<Result<List<Result<Response, AppError>>, AppError>>
      addItemsToReceiving({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required String scope,
    required String language,
    required List<Map<String, dynamic>> itemsData,
  });

  Future<Result<Response, AppError>> deleteItemFromReceiving({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required int itemId,
  });

  Future<Result<Response, AppError>> deleteAllReceivingItems({
    required String divisionId,
    required String costCenterId,
    required String orderId,
  });

  Future<Result<Response, AppError>> addAllReceivingItemsFromOrders({
    required String divisionId,
    required String costCenterId,
    required String originalOrderId,
  });

  Future<Result<Response, AppError>> getOneReceivingItemById({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required int itemId,
    required bool includeFreightCostsOrderItemTotal,
  });

  Future<Result<Response, AppError>> updateReceivingItem({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required int itemId,
    required Map<String, dynamic> fields,
  });

  Future<Result<Response, AppError>> updateReceivingItemDiscount({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required int itemId,
    required double discount,
  });

  Future<Result<Response, AppError>> updateReceivingItemTotalAmount({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required int itemId,
    required double totalAmount,
  });

  Future<Result<Response, AppError>> addReceivingItemSplitDelivery({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required int itemId,
  });

  Future<Result<Response, AppError>> deleteReceivingItemSplitDelivery({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required int itemId,
    required int splitDeliveryId,
  });

  Future<Result<Response, AppError>> updateReceivingItemSplitDelivery({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required int itemId,
    required int splitDeliveryId,
    required Map<String, dynamic> fields,
  });

  Future<Result<Response, AppError>> getReceivingItemSplitDelivery({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required int itemId,
  });

  Future<Result<Response, AppError>> updateReceivingItemTotals({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required int itemId,
    required String unitType,
    required double unitQty,
    required double unitPrice,
  });

  Future<Result<Response, AppError>> getRelatedReceivingDepositItems({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    bool includeStockItems = true,
  });

  Future<Result<Response, AppError>> addReceivingDepositItem({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required String depositItemId,
  });

  Future<Result<Response, AppError>> updateReceivingDepositItem({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required String depositItemId,
    required Map<String, dynamic> fields,
  });

  Future<Result<Response, AppError>> deleteReceivingDepositItem({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required String depositItemId,
  });

  Future<Result<Response, AppError>> addReceivingRelatedOrder({
    required String divisionId,
    required String costCenterId,
    required String originalOrderId,
    required String orderId,
  });

  Future<Result<Response, AppError>> deleteReceivingRelatedOrder({
    required String divisionId,
    required String costCenterId,
    required String originalOrderId,
    required String orderId,
  });

  Future<Result<Response, AppError>> getReceivingAvailableOrders({
    required String divisionId,
    required String costCenterId,
    required String originalOrderId,
    required String supplierId,
  });

  Future<Result<Response, AppError>> getReceivingRelatedOrders({
    required String divisionId,
    required String costCenterId,
    required String originalOrderId,
  });

  Future<Result<Response, AppError>> getPartialReceivingHistory({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required String language,
  });

  Future<Result<Response, AppError>> getReceivingSubsequentDeliveryItems({
    required String divisionId,
    required String costCenterId,
    required String orderId,
  });

  Future<Result<Response, AppError>>
      updateReceivingSubsequentDeliveryItemStatus({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required int subsequentDeliveryItemId,
    required bool status,
  });

  Future<Result<Response, AppError>> toggleAllReceivingSubsequentDeliveryItems({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required bool status,
  });

  Future<Result<Response, AppError>> createReceivingProvisionalBooking({
    required String divisionId,
    required String costCenterId,
    required String orderId,
  });

  Future<Result<Response, AppError>> searchReceivingApprovalRequests({
    required String divisionId,
    required String costCenterKey,
    String query = '',
    String? fromDate,
    String? toDate,
    required String status,
    required String orderByField,
    required String orderType,
    int page = 0,
    int pageSize = 20,
    required String language,
    bool includeFilesCount = false,
  });

  Future<Result<Response, AppError>> updateReceivingApprovalRequest({
    required String divisionId,
    required String approvalRequestId,
    required String status,
    String? comment,
    String? nextApproverId,
  });

  Future<Result<Response, AppError>> getOneReceivingApprovalRequestById({
    required String divisionId,
    required String costCenterKey,
    required String approvalRequestId,
    String language = 'de_DE',
    bool includeProducts = false,
    bool includeLogRecords = false,
    bool includeMessages = false,
    bool includeFiles = false,
  });

  Future<Result<Response, AppError>> getReceivingApprovalRequestProducts({
    required String divisionId,
    required String costCenterKey,
    required String approvalRequestId,
    String language = 'de_DE',
  });

  Future<Result<Response, AppError>>
      updateReceivingApprovalRequestProductComment({
    required String divisionId,
    required String costCenterKey,
    required String approvalRequestId,
    required int productId,
    String? comment,
  });

  Future<Result<Response, AppError>> pullReceivingApprovalRequestMessages({
    required String divisionId,
    required String approvalRequestId,
    int? pullFromMessageId,
    int size = 10,
    required String order,
  });

  Future<Result<Response, AppError>> sendReceivingApprovalRequestMessage({
    required String divisionId,
    required String approvalRequestId,
    required String message,
  });

  Future<Result<Response, AppError>> getReceivingApprovalRequestApprovalTrail({
    required String divisionId,
    required String approvalRequestId,
  });

  Future<Result<Response, AppError>> getReceivingApprovalRequestDocuments({
    required String divisionId,
    required String approvalRequestId,
  });

  Future<Result<Response, AppError>> deleteReceivingApprovalRequestDocument({
    required String divisionId,
    required String approvalRequestId,
    required String documentId,
  });

  Future<Result<Response, AppError>> resetReceivingApprovalRequest({
    required String divisionId,
    required String approvalRequestId,
  });

  Future<Result<Response, AppError>> getReceivingApprovalRequestLogRecords({
    required String divisionId,
    required String approvalRequestId,
  });

  Future<Result<Response, AppError>> requestReceivingApprovalRequestApproval({
    required String divisionId,
    required String costCenterId,
    required String approvalRequestId,
    required String approverId,
  });

  Future<Result<Response, AppError>> getReceivingProvisionalBookingTotalInfo({
    required String divisionId,
    required String costCenterId,
  });

  Future<Result<Response, AppError>> searchReceivingProvisionalBookings({
    required String divisionId,
    required String costCenterId,
    required String query,
    required String supplierId,
    required String supplierName,
    required String productName,
    required String userName,
    required String costCenterName,
    required String requestedByCostCenterName,
    required String? fromDate,
    required String? toDate,
    required String status,
    required String filter,
    required String type,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
    required String language,
    required bool includeFilesCount,
    required bool includeFiles,
  });

  Future<Result<Response, AppError>> resetReceivingsProvisionalBooking({
    required String divisionId,
    required String costCenterId,
    required int id,
  });

  Future<Result<Response, AppError>> getReceivingFreightCostsOrderTotals({
    required String divisionId,
    required String costCenterId,
    required String orderId,
  });

  Future<Result<Response, AppError>> updateReceivingFreightCosts({
    required String divisionId,
    required String orderId,
    required Map<String, dynamic> fields,
  });

  Future<Result<Response, AppError>> receivingsSupplierLookup({
    required String divisionId,
    required String costCenterId,
    required String query,
  });

  Future<Result<Response, AppError>>
      receivingsFreightCostsCreditorSupplierLookup({
    required String divisionId,
    required String costCenterId,
    required String query,
  });

  Future<Result<Response, AppError>>
      getReceivingFreightCostsBreakdownByCostType({
    required String divisionId,
    required String costCenterId,
    required String orderId,
  });

  // --- Delivery notes ---

  Future<Result<Response, AppError>> searchDeliveryNotes({
    required String divisionId,
    required String? query,
    required String? supplierName,
    required String? fromDate,
    required String? toDate,
    required String? bookingId,
    required String? deliveryNoteId,
    required double? deliveryNoteAmount,
    required String? productName,
    required String? receivingConfirmedBy,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
    required String language,
  });

  Future<Result<Response, AppError>> getOneDeliveryNoteById({
    required String divisionId,
    required String id,
    required String language,
  });

  Future<Result<Response, AppError>> getDeliveryNoteBookingVoucherUrl({
    required String divisionId,
    required String bookingId,
    required String language,
  });

  Future<Result<Response, AppError>> toggleDeliveryNoteAvailability({
    required String divisionId,
    required String costCenterId,
    required String id,
    required bool enabled,
  });

  // --- Order lists ---

  Future<Result<Response, AppError>> searchOrderLists({
    required String divisionId,
    required String costCenterId,
    required String query,
    required String language,
    String? filterByCostCenter,
    required String filter,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
    bool includeProducts = false,
  });

  Future<Result<Response, AppError>> lookupEditableOrderList({
    required String divisionId,
    required String costCenterId,
    required int positionId,
    required String itemType,
    required String query,
    required int page,
    required int pageSize,
  });

  Future<Result<Response, AppError>> getOneOrderListById({
    required String divisionId,
    required String costCenterId,
    required int listId,
    required String language,
    bool includeProducts = false,
  });

  Future<Result<Response, AppError>> searchOrderListProducts({
    required String divisionId,
    required String costCenterId,
    required String query,
    required int listId,
    required String language,
    required String filter,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
  });

  Future<Result<Response, AppError>> deleteOrderListProduct({
    required String divisionId,
    required String costCenterId,
    required int listId,
    required int positionId,
    required String itemType,
  });

  Future<Result<Response, AppError>> copyOrderListProduct({
    required String divisionId,
    required String costCenterId,
    required int sourceListId,
    required int targetListId,
    required int positionId,
    required String itemType,
  });

  Future<Result<Response, AppError>> moveOrderListProduct({
    required String divisionId,
    required String costCenterId,
    required int sourceListId,
    required int targetListId,
    required int positionId,
    required String itemType,
  });

  Future<Result<Response, AppError>> addProductToOrderList({
    required String divisionId,
    required String costCenterId,
    required int listId,
    required int positionId,
    required String itemType,
  });

  Future<Result<Response, AppError>> replaceProductInOrderList({
    required String divisionId,
    required String costCenterId,
    required int listId,
    required int positionId,
    required String itemType,
    required int replacementPositionId,
    required String replacementItemType,
  });

  Future<Result<Response, AppError>> updateOrderListProductsOrder({
    required String divisionId,
    required String costCenterId,
    required int listId,
    required List<int> order,
  });

  Future<Result<Response, AppError>> updateOrderListCountOfViews({
    required String divisionId,
    required String costCenterId,
    required int listId,
  });

  Future<Result<Response, AppError>> renameOrderList({
    required String divisionId,
    required String costCenterId,
    required int listId,
    required String newName,
  });

  Future<Result<Response, AppError>> createOrderList({
    required String divisionId,
    required String costCenterId,
    required String name,
  });

  Future<Result<Response, AppError>> updateOrderListReadNote({
    required String divisionId,
    required String costCenterId,
    required int listId,
    required String? readNote,
  });

  Future<Result<Response, AppError>> updateOrderListProductComment({
    required String divisionId,
    required String costCenterId,
    required int listId,
    required int listItemId,
    required String? comment,
  });

  Future<Result<Response, AppError>> downloadOrderList({
    required String divisionId,
    required String costCenterId,
    required int listId,
  });

  // -- Reports --

  Future<Result<Response, AppError>> loadReport({
    required String divisionId,
    required String costCenterId,
    required String idOrAlias,
    int urlTTL = 360,
    Map<String, dynamic> parameters = const {},
  });

  // -- Dashboard --

  Future<Result<Response, AppError>> getDashboardItems({
    required String divisionId,
    required String costCenterId,
  });

  // -- CostCenter --

  Future<Result<Response, AppError>> searchForWebshopCostCenter({
    required String query,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
    String? filterByDivisionId,
  });

  Future<Result<Response, AppError>> getWebshopCostCenterById({
    required String divisionId,
    required String costCenterId,
  });

  // -- Ims.Medius --

  Future<Result<Response, AppError>> resetMediusExport({
    required String divisionId,
    required List<String> bookingIds,
  });

  // -- Ims.Config --

  Future<Result<Response, AppError>> getDivisionImsConfig(String divisionId);

  // -- Ims.Product --

  Future<Result<Response, AppError>> getImsProductById({
    required String divisionId,
    required String costCenterId,
    required String stockItemId,
  });

  Future<Result<Response, AppError>> updateImsProduct({
    required String divisionId,
    required String costCenterId,
    required String stockItemId,
    required Map<String, dynamic> fields,
  });

  Future<Result<List<Result<Response, AppError>>, AppError>> updateImsProducts({
    required String divisionId,
    required String costCenterId,
    required List<Map<String, dynamic>> productsData,
  });

  Future<Result<Response, AppError>> searchImsMappingProducts({
    required String divisionId,
    required String costCenterId,
    required String query,
    required String language,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
  });

  Future<Result<Response, AppError>> updateImsMappingProduct({
    required String divisionId,
    required String costCenterId,
    required String stockItemId,
    required Map<String, dynamic> fields,
  });

  // -- Ims.Supplier --

  Future<Result<Response, AppError>> searchSendOrderAfterApprovalCostCenter({
    required String divisionId,
    required String supplierId,
    required String query,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
  });

  Future<Result<Response, AppError>> toggleSendOrderAfterApprovalCostCenter({
    required String divisionId,
    required String costCenterKey,
    required String costCenterId,
    required String supplierId,
    required bool enabled,
  });

  Future<Result<Response, AppError>>
      toggleAllSendOrderAfterApprovalCostCenters({
    required String divisionId,
    required String supplierId,
    required bool enabled,
  });

  Future<Result<Response, AppError>> imsSupplierUpdate({
    required String divisionId,
    required String supplierId,
    required Map<String, dynamic> fields,
  });

  // --- Ims.Stock ---

  Future<Result<Response, AppError>> searchImsStores({
    required String divisionId,
    required String costCenterId,
    required String query,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
  });

  Future<Result<Response, AppError>> getOneImsStoreById({
    required String orgUnitKey,
    required String costCenterId,
    required String stockId,
  });

  Future<Result<Response, AppError>> updateImsStore({
    required String orgUnitKey,
    required String costCenterId,
    required String stockId,
    required Map<String, dynamic> fields,
    required String language,
  });

  // --- Ims.Stock.Item ---

  Future<Result<Response, AppError>> searchImsStoreItems({
    required String orgUnitKey,
    required String costCenterId,
    required String stockId,
    required String orderByField,
    required String orderType,
    required String query,
    required int page,
    required int pageSize,
    required String language,
  });

  Future<Result<Response, AppError>> deleteImsStoreItem({
    required String orgUnitKey,
    required String costCenterId,
    required String stockId,
    required String stockItemId,
    required String language,
  });

  // --- Ims.Stock.ExchangeRate ---

  Future<Result<Response, AppError>> getImsExchangeRate({
    required String orgUnitKey,
    required String currencyCode,
  });

  // -- Reports.PowerBI --

  Future<Result<Response, AppError>> getPowerBIReports({
    required String divisionId,
    required String costCenterId,
    List<String>? categories,
    required String language,
  });

  Future<Result<Response, AppError>> getPowerBIEmbedUrl({
    required String divisionId,
    required String costCenterId,
    required String reportId,
    required String language,
  });

  Future<Result<Response, AppError>> getPowerBICategories({
    required String divisionId,
    required String costCenterId,
    required String language,
  });

  // -- Recipe --

  Future<Result<Response, AppError>> searchRecipes({
    required String divisionId,
    required String costCenterId,
    String query = '',
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
    required String language,
    String? categoryId,
    bool showOnlyFavorites = false,
    required List<String> includes,
  });

  Future<Result<Response, AppError>> getOneRecipeById({
    required String divisionId,
    required String costCenterId,
    required String recipeId,
    required String language,
    required List<String> includes,
  });

  Future<Result<Response, AppError>> addRecipeToCart({
    required String divisionId,
    required String costCenterId,
    required String recipeId,
    required double quantity,
  });

  Future<Result<Response, AppError>> getRecipeCalculation({
    required String divisionId,
    required String costCenterId,
    required String recipeId,
    required String language,
    required List<String> includes,
  });

  Future<Result<Response, AppError>> searchRecipeCategories({
    required String divisionId,
    required String costCenterId,
    String query = '',
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
    required String language,
  });

  Future<Result<Response, AppError>> getAllRecipeIngredients({
    required String divisionId,
    required String costCenterId,
    required String recipeId,
    required String language,
  });

  Future<Result<Response, AppError>> getAllRecipePreparationSteps({
    required String divisionId,
    required String costCenterId,
    required String recipeId,
    required String language,
  });

  Future<Result<Response, AppError>> getAllRecipeNutrients({
    required String divisionId,
    required String costCenterId,
    required String recipeId,
    required String language,
  });

  Future<Result<Response, AppError>> getRecipeShareDivisions({
    required String divisionId,
    required String costCenterId,
    required String recipeId,
    required String language,
  });

  Future<Result<Response, AppError>> updateRecipeShareStatus({
    required String divisionId,
    required String shareWithDivisionId,
    required String costCenterId,
    required String recipeId,
    required bool shared,
  });

  Future<Result<Response, AppError>> addRecipeNote({
    required String divisionId,
    required String costCenterId,
    required String content,
    required String recipeId,
  });

  Future<Result<Response, AppError>> deleteRecipeNote({
    required String divisionId,
    required String costCenterId,
    required int recipeNoteId,
  });

  Future<Result<Response, AppError>> updateRecipeNote({
    required String divisionId,
    required String costCenterId,
    required int recipeNoteId,
    required String content,
  });

  Future<Result<Response, AppError>> searchRecipeNotes({
    required String divisionId,
    required String costCenterId,
    required String recipeId,
    String? addedByUserId,
    String? updatedByUserId,
    String query = '',
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
    required String language,
  });

  Future<Result<Response, AppError>> getOneRecipeNoteById({
    required String divisionId,
    required String costCenterId,
    required int recipeNoteId,
  });

  Future<Result<Response, AppError>> favoritizeRecipe({
    required String divisionId,
    required String costCenterId,
    required String recipeId,
    required bool isFavorite,
  });

  // -- Files --

  Future<Result<Uint8List, AppError>> loadFile({
    required Uri url,
    Duration timeout = const Duration(seconds: 30),
  });

  Future<Result<bool, AppError>> uploadFile({
    required String divisionId,
    required String type,
    required String filePath,
    required Map<String, String> additionalParams,
    bool isPublic = false,
    String? overrideFileName,
  });

  Future<Result<bool, AppError>> renameFile({
    required String divisionId,
    required String type,
    required String fileId,
    required String newName,
    required Map<String, String> additionalParams,
  });

  Future<Result<bool, AppError>> deleteFile({
    required String divisionId,
    required String type,
    required String fileId,
    required Map<String, String> additionalParams,
  });

  // -- Scan to order --

  Future<Result<Response, AppError>> createScanToOrderFromRecipe({
    required String divisionId,
    required String costCenterId,
    required String recipeId,
    required double quantity,
    required String language,
  });

  Future<Result<Response, AppError>> getOneScanToOrderById({
    required String divisionId,
    required String costCenterId,
    required String scanTaskId,
    required String language,
    required List<String> includes,
  });

  Future<Result<Response, AppError>> getScanToOrderItemAvailableSuppliers({
    required String divisionId,
    required String costCenterId,
    required String scanTaskId,
    required String scanItemId,
    required String language,
  });

  Future<Result<Response, AppError>> updateScanToOrderItemSupplier({
    required String divisionId,
    required String costCenterId,
    required String scanTaskId,
    required String scanItemId,
    required Map<String, dynamic> fields,
  });

  Future<Result<Response, AppError>> moveScanToOrderToCart({
    required String divisionId,
    required String costCenterId,
    required String scanTaskId,
  });

  Future<Result<Response, AppError>> loadTestTaskEntities({
    required int page,
    required int pageSize,
    required String orderType,
    required String orderByField,
    required String query,
    required List<String> includes,
    required String costCenterKey,
    required String orgUnitKey,
  });
}
