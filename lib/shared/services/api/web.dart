import 'dart:io';
import 'dart:typed_data';

import 'package:app/shared/services/api/api_.dart';
import 'package:app/shared/services/api/jsonrpc/ims/exchange_rate.dart';
import 'package:app/shared/services/api/jsonrpc/ims/medius.dart';
import 'package:app/shared/types/types.dart';

class WebApiService implements ApiService {
  final ApiTransport apiTransport;

  final JsonRpcApprovals _approvals;

  final JsonRpcBookings _bookings;

  final JsonRpcCart _cart;

  final JsonRpcCatalog _catalog;

  final JsonRpcDivision _division;

  final JsonRpcAuth _auth;

  final JsonRpcI18n _i18n;

  final JsonRpcTransferLists _transferLists;

  final JsonRpcInventory _inventory;

  final JsonRpcInvoices _invoices;

  final JsonRpcAnnouncements _announcements;

  final JsonRpcOrders _orders;

  final JsonRpcPurchaseRequests _purchaseRequests;

  final JsonRpcCapex _capex;

  final JsonRpcSupplier _supplier;

  final JsonRpcTest _test;

  final JsonRpcUser _user;

  final JsonRpcWarehouse _warehouse;

  final JsonRpcSessions _session;

  final JsonRpcReceivings _receivings;

  final JsonRpcOrderLists _orderLists;

  final JsonRpcReports _reports;

  final JsonRpcDashboard _dashboard;

  final JsonRpcDeliveryNotes _deliveryNotes;

  final JsonRpcCostCenter _costCenter;

  final JsonRpcImsMedius _imsMedius;

  final JsonRpcImsConfig _imsConfig;

  final JsonRpcImsProduct _imsProduct;

  final JsonRpcImsSupplier _imsSupplier;

  final JsonRpcImsStore _imsStore;

  final JsonRpcImsExchangeRate _imsExchangeRate;

  final JsonRpcReportsPowerBI _reportsPowerBI;

  final JsonRpcRecipes _recipe;

  final JsonRpcScanToOrder _scanToOrder;

  WebApiService(this.apiTransport)
      : _approvals = JsonRpcApprovals(apiTransport),
        _bookings = JsonRpcBookings(apiTransport),
        _cart = JsonRpcCart(apiTransport),
        _auth = JsonRpcAuth(apiTransport),
        _division = JsonRpcDivision(apiTransport),
        _i18n = JsonRpcI18n(apiTransport),
        _transferLists = JsonRpcTransferLists(apiTransport),
        _inventory = JsonRpcInventory(apiTransport),
        _invoices = JsonRpcInvoices(apiTransport),
        _announcements = JsonRpcAnnouncements(apiTransport),
        _orders = JsonRpcOrders(apiTransport),
        _purchaseRequests = JsonRpcPurchaseRequests(apiTransport),
        _capex = JsonRpcCapex(apiTransport),
        _supplier = JsonRpcSupplier(apiTransport),
        _test = JsonRpcTest(apiTransport),
        _user = JsonRpcUser(apiTransport),
        _warehouse = JsonRpcWarehouse(apiTransport),
        _catalog = JsonRpcCatalog(apiTransport),
        _session = JsonRpcSessions(apiTransport),
        _receivings = JsonRpcReceivings(apiTransport),
        _orderLists = JsonRpcOrderLists(apiTransport),
        _reports = JsonRpcReports(apiTransport),
        _dashboard = JsonRpcDashboard(apiTransport),
        _deliveryNotes = JsonRpcDeliveryNotes(apiTransport),
        _costCenter = JsonRpcCostCenter(apiTransport),
        _imsMedius = JsonRpcImsMedius(apiTransport),
        _imsConfig = JsonRpcImsConfig(apiTransport),
        _imsProduct = JsonRpcImsProduct(apiTransport),
        _imsStore = JsonRpcImsStore(apiTransport),
        _imsSupplier = JsonRpcImsSupplier(apiTransport),
        _imsExchangeRate = JsonRpcImsExchangeRate(apiTransport),
        _reportsPowerBI = JsonRpcReportsPowerBI(apiTransport),
        _recipe = JsonRpcRecipes(apiTransport),
        _scanToOrder = JsonRpcScanToOrder(apiTransport);

  @override
  void setAuthenticator(Authenticator authenticator) {
    apiTransport.setAuthenticator(authenticator);
  }

  @override
  void unsetAuthenticator() {
    apiTransport.setAuthenticator(AccessForbidden());
  }

  @override
  Future<Result<Response, AppError>> ping({
    Duration timeout = const Duration(seconds: 5),
  }) {
    return _test.ping(timeout: timeout);
  }

  // -- User --

  @override
  Future<Result<Response, AppError>> verifyUserBySecurityToken() async {
    return _user.getByToken();
  }

  @override
  Future<Result<Response, AppError>> loginUser({
    required String login,
    required String password,
    required String platform,
    required String platformCode,
    required String appVersion,
    String? deviceId,
    String? otpToken,
  }) async {
    return _user.login(
      login: login,
      password: password,
      platform: platform,
      platformCode: platformCode,
      deviceId: deviceId,
      appVersion: appVersion,
      otpToken: otpToken,
    );
  }

  @override
  Future<Result<Response, AppError>> resetPassword(String login) async {
    return _user.resetPassword(login);
  }

  @override
  Future<Result<Response, AppError>> setNewPassword({
    required String login,
    required String oldPassword,
    required String newPassword,
  }) async {
    return _user.setNewPassword(
      login: login,
      oldPassword: oldPassword,
      newPassword: newPassword,
    );
  }

  @override
  Future<Result<Response, AppError>> logoutUser() async {
    return _user.logout();
  }

  @override
  Future<Result<Response, AppError>> getUserThemeConfig(
    String divisionId,
  ) async {
    return _user.getThemeConfig(divisionId);
  }

  @override
  Future<Result<Response, AppError>> getUserFeatureFlags({
    required String divisionId,
    required String costCenterId,
  }) async {
    return _user.getFeatureFlags(
      divisionId: divisionId,
      costCenterId: costCenterId,
    );
  }

  // -- Division --

  @override
  Future<Result<Response, AppError>> getOneDivisionById(String unitId) {
    return _division.getOneById(unitId);
  }

  // -- Catalog --

  @override
  Future<Result<Response, AppError>> rebuildCatalog({
    required String divisionId,
    required String costCenterId,
  }) async {
    return _catalog.rebuild(
      divisionId: divisionId,
      costCenterId: costCenterId,
    );
  }

  @override
  Future<Result<Response, AppError>> getProductOffers({
    required String divisionId,
    required String costCenterId,
    required String metaId,
    required String language,
  }) async {
    return _catalog.getProductOffers(
      divisionId: divisionId,
      costCenterId: costCenterId,
      metaId: metaId,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> getOfferById({
    required String divisionId,
    required String costCenterId,
    required int positionId,
    required String itemType,
    required String language,
  }) async {
    return _catalog.getOfferById(
      divisionId: divisionId,
      costCenterId: costCenterId,
      positionId: positionId,
      itemType: itemType,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> getCurrencies({
    required String divisionId,
  }) async {
    return _catalog.getCurrencies(divisionId: divisionId);
  }

  // -- Cart --

  @override
  Future<Result<Response, AppError>> getProductTotals({
    required String divisionId,
    required String costCenterId,
    required int positionId,
    required String type,
  }) async {
    return _cart.getProductTotals(
      divisionId: divisionId,
      costCenterId: costCenterId,
      positionId: positionId,
      type: type,
    );
  }

  @override
  Future<Result<Response, AppError>> getCartProducts({
    required String divisionId,
    required String costCenterId,
    required String language,
    required String filter,
  }) {
    return _cart.getProducts(
      divisionId: divisionId,
      costCenterId: costCenterId,
      language: language,
      filter: filter,
    );
  }

  @override
  Future<Result<Response, AppError>> getCartProductsOverview({
    required String divisionId,
    required String costCenterId,
  }) {
    return _cart.getProductsOverview(
      divisionId: divisionId,
      costCenterId: costCenterId,
    );
  }

  @override
  Future<Result<Response, AppError>> getCartOrdersOverview({
    required String divisionId,
    required String costCenterId,
    required String filter,
  }) {
    return _cart.getOrdersOverview(
      divisionId: divisionId,
      costCenterId: costCenterId,
      filter: filter,
    );
  }

  @override
  Future<Result<Response, AppError>> getCartOrdersToApprove({
    required String divisionId,
    required String costCenterId,
    required String language,
  }) {
    return _cart.getOrdersToApprove(
      divisionId: divisionId,
      costCenterId: costCenterId,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> requestCartOrderApproval({
    required String divisionId,
    required String costCenterId,
    required String purchaseRequestId,
    required String type,
    required String approverId,
    String? comment,
  }) {
    return _cart.requestOrderApproval(
      divisionId: divisionId,
      costCenterId: costCenterId,
      purchaseRequestId: purchaseRequestId,
      type: type,
      approverId: approverId,
      comment: comment,
    );
  }

  @override
  Future<Result<Response, AppError>> getCartOrdersToSend({
    required String divisionId,
    required String costCenterId,
  }) {
    return _cart.getOrdersToSend(
      divisionId: divisionId,
      costCenterId: costCenterId,
    );
  }

  @override
  Future<Result<Response, AppError>> sendCartOrders({
    required String divisionId,
    required String costCenterId,
    required List<Map<String, dynamic>> orders,
  }) {
    return _cart.sendOrders(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orders: orders,
    );
  }

  @override
  Future<Result<Response, AppError>> getCartNote({
    required String divisionId,
    required String costCenterId,
  }) {
    return _cart.getNote(
      divisionId: divisionId,
      costCenterId: costCenterId,
    );
  }

  @override
  Future<Result<Response, AppError>> updateCartNote({
    required String divisionId,
    required String costCenterId,
    required String? note,
  }) {
    return _cart.updateNote(
      divisionId: divisionId,
      costCenterId: costCenterId,
      note: note,
    );
  }

  @override
  Future<Result<Response, AppError>> getCartTotals({
    required String divisionId,
    required String costCenterId,
  }) {
    return _cart.getTotals(
      divisionId: divisionId,
      costCenterId: costCenterId,
    );
  }

  @override
  Future<Result<Response, AppError>> getCartProductOtherOffers({
    required String divisionId,
    required String costCenterId,
    required String productMetaId,
  }) {
    return _cart.getOtherOffers(
      divisionId: divisionId,
      costCenterId: costCenterId,
      productMetaId: productMetaId,
    );
  }

  @override
  Future<Result<Response, AppError>> mergeCartOrders({
    required String divisionId,
    required String costCenterId,
    required List<String> orders,
  }) {
    return _cart.mergeOrders(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orders: orders,
    );
  }

  @override
  Future<Result<Response, AppError>> deleteCartProduct({
    required String divisionId,
    required String costCenterId,
    required int cartItemId,
  }) {
    return _cart.deleteItem(
      divisionId: divisionId,
      costCenterId: costCenterId,
      cartItemId: cartItemId,
    );
  }

  @override
  Future<Result<Response, AppError>> deleteCartProductByPositionId({
    required String divisionId,
    required String costCenterId,
    required int positionId,
    required String type,
  }) {
    return _cart.deleteItemByPositionId(
      divisionId: divisionId,
      costCenterId: costCenterId,
      positionId: positionId,
      type: type,
    );
  }

  @override
  Future<Result<Response, AppError>> updateCartProductComment({
    required String divisionId,
    required String costCenterId,
    required int itemId,
    required String? comment,
  }) {
    return _cart.updateItemComment(
      divisionId: divisionId,
      costCenterId: costCenterId,
      itemId: itemId,
      comment: comment,
    );
  }

  @override
  Future<Result<Response, AppError>> updateCartOrderDeliveryDate({
    required String divisionId,
    required String costCenterId,
    required String orderTempId,
    required String deliveryDate,
  }) {
    return _cart.updateOrderDeliveryDate(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderTempId: orderTempId,
      deliveryDate: deliveryDate,
    );
  }

  @override
  Future<Result<Response, AppError>> addOrUpdateCartProduct({
    required String divisionId,
    required String costCenterId,
    required int positionId,
    required String itemType,
    required double qty,
  }) {
    return _cart.addOrUpdateItem(
      divisionId: divisionId,
      costCenterId: costCenterId,
      positionId: positionId,
      itemType: itemType,
      qty: qty,
    );
  }

  @override
  Future<Result<Response, AppError>> addOrUpdateManyCartProducts({
    required String divisionId,
    required String costCenterId,
    required List<Map<String, dynamic>> products,
  }) {
    return _cart.addOrUpdateManyItems(
      divisionId: divisionId,
      costCenterId: costCenterId,
      products: products,
    );
  }

  @override
  Future<Result<Response, AppError>> freeTextOrderSupplierLookup({
    required String divisionId,
    required String costCenterId,
    required String query,
    required int page,
    required int pageSize,
  }) {
    return _cart.freeTextOrderSupplierLookup(
      divisionId: divisionId,
      costCenterId: costCenterId,
      query: query,
      page: page,
      pageSize: pageSize,
    );
  }

  @override
  Future<Result<Response, AppError>> freeTextOrderProductLookup({
    required String divisionId,
    required String costCenterId,
    required String supplierId,
    required String query,
    required int page,
    required int pageSize,
  }) {
    return _cart.freeTextOrderProductLookup(
      divisionId: divisionId,
      costCenterId: costCenterId,
      supplierId: supplierId,
      query: query,
      page: page,
      pageSize: pageSize,
    );
  }

  @override
  Future<Result<Response, AppError>> freeTextOrderAddOrUpdateItem({
    required String divisionId,
    required String costCenterId,
    required String supplierId,
    String? productId,
    required String productName,
    required String categoryId,
    required double quantity,
    required double price,
    required String orderUnit,
    required String contentUnit,
    required double contentUnitsPerOrderUnit,
    String? itemDescription,
    String? itemNumber,
    String? inventoryUnit,
    int? vatGstRate,
  }) {
    return _cart.freeTextOrderAddOrUpdateItem(
      divisionId: divisionId,
      costCenterId: costCenterId,
      supplierId: supplierId,
      productId: productId,
      productName: productName,
      categoryId: categoryId,
      quantity: quantity,
      price: price,
      orderUnit: orderUnit,
      contentUnit: contentUnit,
      contentUnitsPerOrderUnit: contentUnitsPerOrderUnit,
      itemDescription: itemDescription,
      itemNumber: itemNumber,
      inventoryUnit: inventoryUnit,
      vatGstRate: vatGstRate,
    );
  }

  @override
  Future<Result<Response, AppError>> freeTextOrderGetProductById({
    required String divisionId,
    required String costCenterId,
    required String supplierId,
    required String productId,
  }) {
    return _cart.freeTextOrderGetProductById(
      divisionId: divisionId,
      costCenterId: costCenterId,
      supplierId: supplierId,
      productId: productId,
    );
  }

  @override
  Future<Result<Response, AppError>> getCartAutoConsolidateCostCenters({
    required String divisionId,
    required String costCenterId,
    required String orderByField,
    required String orderType,
  }) {
    return _cart.getAutoConsolidateCostCenters(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderByField: orderByField,
      orderType: orderType,
    );
  }

  @override
  Future<Result<Response, AppError>> cartAutoConsolidate({
    required String divisionId,
    required String costCenterId,
    required List<String> costCenterIds,
  }) {
    return _cart.autoConsolidate(
      divisionId: divisionId,
      costCenterId: costCenterId,
      costCenterIds: costCenterIds,
    );
  }

  @override
  Future<Result<Response, AppError>> cartCostTypeLookup({
    required String divisionId,
    required String costCenterId,
    required String query,
    required String? lookupDivisionId,
    required String? lookupCostCenterId,
  }) =>
      _cart.costTypeLookup(
        divisionId: divisionId,
        costCenterId: costCenterId,
        query: query,
        lookupDivisionId: lookupDivisionId,
        lookupCostCenterId: lookupCostCenterId,
      );

  @override
  Future<Result<Response, AppError>> updateCartProductCostType({
    required String divisionId,
    required String costCenterId,
    required String productCostCenterId,
    required int itemPositionId,
    required String itemType,
    required String costTypeId,
  }) =>
      _cart.updateProductCostType(
        divisionId: divisionId,
        costCenterId: costCenterId,
        productCostCenterId: productCostCenterId,
        itemPositionId: itemPositionId,
        itemType: itemType,
        costTypeId: costTypeId,
      );

  @override
  Future<Result<Response, AppError>> calculateCartBudget({
    required String divisionId,
    required String costCenterId,
    required String targetCostCenterId,
  }) =>
      _cart.calculateBudget(
        divisionId: divisionId,
        costCenterId: costCenterId,
        targetCostCenterId: targetCostCenterId,
      );
  @override
  Future<Result<Response, AppError>> updateCartProductDiscount({
    required String divisionId,
    required String costCenterId,
    required int cartItemId,
    required double? discountPercentage,
    required double? discountAmount,
  }) =>
      _cart.updateProductDiscount(
        divisionId: divisionId,
        costCenterId: costCenterId,
        cartItemId: cartItemId,
        discountPercentage: discountPercentage,
        discountAmount: discountAmount,
      );

  @override
  Future<Result<Response, AppError>> updateCartDiscountByOrder({
    required String divisionId,
    required String costCenterId,
    required String supplierId,
    required int leadTime,
    required String? deliveryDate,
    required double? discountPercentage,
    required double? discountAmount,
  }) =>
      _cart.updateDiscountByOrder(
        divisionId: divisionId,
        costCenterId: costCenterId,
        supplierId: supplierId,
        leadTime: leadTime,
        deliveryDate: deliveryDate,
        discountPercentage: discountPercentage,
        discountAmount: discountAmount,
      );

  @override
  Future<Result<Response, AppError>> addCartOrderExternalComment({
    required String divisionId,
    required String costCenterId,
    required String? orderDivisionId,
    required String? orderCostCenterId,
    required String supplierId,
    required int leadTime,
    required String comment,
  }) =>
      _cart.addExternalComment(
        divisionId: divisionId,
        costCenterId: costCenterId,
        orderDivisionId: orderDivisionId,
        orderCostCenterId: orderCostCenterId,
        supplierId: supplierId,
        leadTime: leadTime,
        comment: comment,
      );

  @override
  Future<Result<Response, AppError>> getCartExternalCommentHistory({
    required String divisionId,
    required String costCenterId,
    required String? orderDivisionId,
    required String? orderCostCenterId,
    required String supplierId,
    required int leadTime,
  }) =>
      _cart.getExternalCommentHistory(
        divisionId: divisionId,
        costCenterId: costCenterId,
        orderDivisionId: orderDivisionId,
        orderCostCenterId: orderCostCenterId,
        supplierId: supplierId,
        leadTime: leadTime,
      );

  @override
  Future<Result<Response, AppError>> getCartPurchaseRequestExternalComments({
    required String divisionId,
    required String costCenterId,
    required String? orderDivisionId,
    required String? orderCostCenterId,
    required String purchaseRequestId,
  }) =>
      _cart.getPurchaseRequestExternalComments(
        divisionId: divisionId,
        costCenterId: costCenterId,
        orderDivisionId: orderDivisionId,
        orderCostCenterId: orderCostCenterId,
        purchaseRequestId: purchaseRequestId,
      );

  // -- Orders --
  @override
  Future<Result<Response, AppError>> getOrderExternalCommentHistory({
    required String divisionId,
    required String costCenterId,
    required String? orderDivisionId,
    required String? orderCostCenterId,
    required String orderId,
  }) =>
      _orders.getExternalCommentHistory(
        divisionId: divisionId,
        costCenterId: costCenterId,
        orderDivisionId: orderDivisionId,
        orderCostCenterId: orderCostCenterId,
        orderId: orderId,
      );

  @override
  Future<Result<Response, AppError>> getOrderInvoiceUrl({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required String language,
  }) async {
    return _orders.getInvoiceUrl(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> getOneOrderById({
    required String divisionId,
    required String costCenterId,
    required String orderCostCenterId,
    required String orderId,
    required String language,
    required bool includeMergedComments,
  }) {
    return _orders.getOneById(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderCostCenterId: orderCostCenterId,
      orderId: orderId,
      language: language,
      includeMergedComments: includeMergedComments,
    );
  }

  @override
  Future<Result<Response, AppError>> searchOrders({
    required String divisionId,
    required String costCenterKey,
    required String query,
    required String? costCenterId,
    required String? orderer,
    required String? fromDate,
    required String? toDate,
    required String filter,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
  }) async {
    return _orders.search(
      divisionId: divisionId,
      costCenterKey: costCenterKey,
      query: query,
      costCenterId: costCenterId,
      orderer: orderer,
      fromDate: fromDate,
      toDate: toDate,
      filter: filter,
      orderByField: orderByField,
      orderType: orderType,
      page: page,
      pageSize: pageSize,
    );
  }

  @override
  Future<Result<Response, AppError>> searchOrderProducts({
    required String divisionId,
    required String costCenterId,
    required String orderCostCenterId,
    required String orderId,
    required String query,
    required String language,
    required String filter,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
  }) async {
    return _orders.searchProducts(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderCostCenterId: orderCostCenterId,
      orderId: orderId,
      query: query,
      language: language,
      filter: filter,
      orderByField: orderByField,
      orderType: orderType,
      page: page,
      pageSize: pageSize,
    );
  }

  @override
  Future<Result<Response, AppError>> getOrderTargetCostCenters({
    required String divisionId,
    required String costCenterId,
    required String orderId,
  }) async {
    return _orders.getTargetCostCenters(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
    );
  }

  @override
  Future<Result<Response, AppError>> sendMessageToOrderSupplier({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required String replyToEmail,
    required String message,
  }) async {
    return _orders.sendMessageToSupplier(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
      replyToEmail: replyToEmail,
      message: message,
    );
  }

  @override
  Future<Result<Response, AppError>> orderersLookup({
    required String divisionId,
    required String costCenterId,
    required String query,
    required int page,
    required int pageSize,
  }) async {
    return _orders.ordererLookup(
      divisionId: divisionId,
      costCenterId: costCenterId,
      query: query,
      page: page,
      pageSize: pageSize,
    );
  }

  @override
  Future<Result<Response, AppError>> ordersCostCentersLookup({
    required String divisionId,
    required String costCenterId,
    required String query,
    required int page,
    required int pageSize,
  }) async {
    return _orders.costCenterLookup(
      divisionId: divisionId,
      costCenterId: costCenterId,
      query: query,
      page: page,
      pageSize: pageSize,
    );
  }

  @override
  Future<Result<Response, AppError>> getProductsToOrderAgain({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required String language,
  }) async {
    return _orders.getProductsToOrderAgain(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> getProductLastOrders({
    required String divisionId,
    required String metaId,
  }) =>
      _orders.getProductLastOrders(
        divisionId: divisionId,
        metaId: metaId,
      );

  @override
  Future<Result<Response, AppError>> searchAstoreOrders({
    required String divisionId,
    required String costCenterKey,
    required String query,
    required String? supplier,
    required String? orderId,
    required String? costCenterName,
    required String? fromOrderDate,
    required String? toOrderDate,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
    required String language,
  }) =>
      _orders.searchAstoreOrders(
        divisionId: divisionId,
        costCenterKey: costCenterKey,
        query: query,
        supplier: supplier,
        orderId: orderId,
        costCenterName: costCenterName,
        fromOrderDate: fromOrderDate,
        toOrderDate: toOrderDate,
        orderByField: orderByField,
        orderType: orderType,
        page: page,
        pageSize: pageSize,
        language: language,
      );

  @override
  Future<Result<Response, AppError>> getAstoreOrdersCountOfNotProcessed({
    required String divisionId,
    required String costCenterKey,
  }) =>
      _orders.getAstoreOrdersCountOfNotProcessed(
        divisionId: divisionId,
        costCenterKey: costCenterKey,
      );

  @override
  Future<Result<Response, AppError>> searchAstoreOrderProducts({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required String query,
    required String orderByField,
    required String orderType,
    required String language,
    required int page,
    required int pageSize,
  }) =>
      _orders.searchAstoreOrderProducts(
        divisionId: divisionId,
        costCenterId: costCenterId,
        orderId: orderId,
        query: query,
        orderByField: orderByField,
        orderType: orderType,
        language: language,
        page: page,
        pageSize: pageSize,
      );

  // -- Transfer Lists --

  @override
  Future<Result<Response, AppError>> renameTransferList({
    required String divisionId,
    required String costCenterId,
    required String listId,
    required String newName,
  }) {
    return _transferLists.rename(
      divisionId: divisionId,
      costCenterId: costCenterId,
      listId: listId,
      newName: newName,
    );
  }

  @override
  Future<Result<Response, AppError>> placeTransferListOrder({
    required String divisionId,
    required String listId,
    required String deliveryDate,
    required String? comment,
    required List<Map<String, dynamic>> items,
  }) {
    return _transferLists.placeOrder(
      divisionId: divisionId,
      listId: listId,
      deliveryDate: deliveryDate,
      comment: comment,
      items: items,
    );
  }

  @override
  Future<Result<Response, AppError>> getTransferListOrderDocumentUrl({
    required String divisionId,
    required String bookingId,
    required String language,
  }) {
    return _transferLists.getTransferListOrderDocumentUrl(
      divisionId: divisionId,
      bookingId: bookingId,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> searchTransferLists({
    required String divisionId,
    required String costCenterId,
    required String query,
    required String filter,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
    bool includeItems = false,
  }) {
    return _transferLists.search(
      divisionId: divisionId,
      costCenterId: costCenterId,
      query: query,
      filter: filter,
      orderByField: orderByField,
      orderType: orderType,
      page: page,
      pageSize: pageSize,
      includeItems: includeItems,
    );
  }

  @override
  Future<Result<Response, AppError>> searchTransferListItems({
    required String divisionId,
    required String costCenterId,
    required String listId,
    required String query,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
  }) {
    return _transferLists.searchItems(
      divisionId: divisionId,
      costCenterId: costCenterId,
      listId: listId,
      query: query,
      orderByField: orderByField,
      orderType: orderType,
      page: page,
      pageSize: pageSize,
    );
  }

  @override
  Future<Result<Response, AppError>> getAllTransferListItems({
    required String divisionId,
    required String costCenterId,
    required String listId,
    required String language,
  }) {
    return _transferLists.getAllItems(
      divisionId: divisionId,
      costCenterId: costCenterId,
      listId: listId,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> getOneTransferListItemById({
    required String divisionId,
    required String costCenterId,
    required String listId,
    required int listItemId,
    required String language,
  }) {
    return _transferLists.getOneItemById(
      divisionId: divisionId,
      costCenterId: costCenterId,
      listId: listId,
      listItemId: listItemId,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> updateTransferListItem({
    required String divisionId,
    required String costCenterId,
    required String listId,
    required int listItemId,
    required Map<String, dynamic> fields,
  }) {
    return _transferLists.updateItem(
      divisionId: divisionId,
      costCenterId: costCenterId,
      listId: listId,
      listItemId: listItemId,
      fields: fields,
    );
  }

  @override
  Future<Result<Response, AppError>> removeTransferListItem({
    required String divisionId,
    required String costCenterId,
    required String listId,
    required int listItemId,
  }) {
    return _transferLists.removeItem(
      divisionId: divisionId,
      costCenterId: costCenterId,
      listId: listId,
      listItemId: listItemId,
    );
  }

  @override
  Future<Result<Response, AppError>> updateTransferListItemsOrder({
    required String divisionId,
    required String costCenterId,
    required String listId,
    required List<int> listItemIds,
  }) {
    return _transferLists.updateItemsOrder(
      divisionId: divisionId,
      costCenterId: costCenterId,
      listId: listId,
      listItemIds: listItemIds,
    );
  }

  @override
  Future<Result<Response, AppError>> getOneTransferListsById({
    required String divisionId,
    required String listId,
    bool includeItems = false,
  }) async {
    return _transferLists.getOneById(
      divisionId: divisionId,
      listId: listId,
      includeItems: includeItems,
    );
  }

  @override
  Future<Result<Response, AppError>> searchTransferListsOrders({
    required String divisionId,
    required String costCenterKey,
    required String? costCenterId,
    required String query,
    required String status,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
    bool onlyThisCostCenter = false,
  }) {
    return _transferLists.searchOrders(
      divisionId: divisionId,
      costCenterKey: costCenterKey,
      costCenterId: costCenterId,
      query: query,
      status: status,
      orderByField: orderByField,
      orderType: orderType,
      page: page,
      pageSize: pageSize,
    );
  }

  @override
  Future<Result<Response, AppError>> searchTransferListOrderItems({
    required String divisionId,
    required String costCenterId,
    required String bookingId,
    required String query,
    required String filter,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
    required String language,
  }) {
    return _transferLists.searchOrderItems(
      divisionId: divisionId,
      costCenterId: costCenterId,
      bookingId: bookingId,
      query: query,
      filter: filter,
      orderByField: orderByField,
      orderType: orderType,
      page: page,
      pageSize: pageSize,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> lookupTransferListCostCenter({
    required String divisionId,
    required String costCenterId,
    required String query,
    required int page,
    required int pageSize,
  }) {
    return _transferLists.lookupCostCenter(
      divisionId: divisionId,
      costCenterId: costCenterId,
      query: query,
      page: page,
      pageSize: pageSize,
    );
  }

  @override
  Future<Result<Response, AppError>>
      lookupTransferListInterPropertyTargetDivision({
    required String divisionId,
    required String costCenterId,
    required String query,
    required int page,
    required int pageSize,
  }) {
    return _transferLists.lookupInterPropertyTargetDivision(
      divisionId: divisionId,
      costCenterId: costCenterId,
      query: query,
      page: page,
      pageSize: pageSize,
    );
  }

  @override
  Future<Result<Response, AppError>> createTransferList({
    required String divisionId,
    required String costCenterId,
    required String listName,
    required String costCenterAccountancyId,
    required bool isEditable,
    required String type,
  }) {
    return _transferLists.create(
      divisionId: divisionId,
      costCenterId: costCenterId,
      listName: listName,
      costCenterAccountancyId: costCenterAccountancyId,
      isEditable: isEditable,
      type: type,
    );
  }

  @override
  Future<Result<Response, AppError>> deleteTransferList({
    required String divisionId,
    required String costCenterId,
    required String listId,
  }) {
    return _transferLists.delete(
      divisionId: divisionId,
      costCenterId: costCenterId,
      listId: listId,
    );
  }

  @override
  Future<Result<Response, AppError>> lookupInHouseListItemToAdd({
    required String divisionId,
    required String costCenterId,
    required String listId,
    required String query,
    required int page,
    required int pageSize,
    required String language,
  }) {
    return _transferLists.lookupInHouseListItemToAdd(
      divisionId: divisionId,
      costCenterId: costCenterId,
      listId: listId,
      query: query,
      page: page,
      pageSize: pageSize,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> addTransferListItem({
    required String divisionId,
    required String costCenterId,
    required String listId,
    required String stockItemId,
    String? sourceStockId,
    String? targetStockId,
    String? incomingCostTypeId,
    String? sourceDivisionId,
  }) {
    return _transferLists.addItem(
      divisionId: divisionId,
      costCenterId: costCenterId,
      listId: listId,
      stockItemId: stockItemId,
      sourceStockId: sourceStockId,
      targetStockId: targetStockId,
      incomingCostTypeId: incomingCostTypeId,
      sourceDivisionId: sourceDivisionId,
    );
  }

  @override
  Future<Result<Response, AppError>> lookupInterPropertySourceDivision({
    required String divisionId,
    required String costCenterId,
    required String query,
    required int page,
    required int pageSize,
  }) {
    return _transferLists.lookupSourceDivision(
      divisionId: divisionId,
      costCenterId: costCenterId,
      query: query,
      page: page,
      pageSize: pageSize,
    );
  }

  @override
  Future<Result<Response, AppError>> getOneInterPropertyItemToAddById({
    required String divisionId,
    required String costCenterId,
    required String listId,
    required String sourceDivisionId,
    required String sourceStockItemId,
  }) {
    return _transferLists.getOneInterPropertyItemToAddById(
      divisionId: divisionId,
      costCenterId: costCenterId,
      listId: listId,
      sourceDivisionId: sourceDivisionId,
      sourceStockItemId: sourceStockItemId,
    );
  }

  @override
  Future<Result<Response, AppError>> lookupInterPropertyItemToAdd({
    required String divisionId,
    required String costCenterId,
    required String listId,
    required String sourceDivisionId,
    required String query,
    required int page,
    required int pageSize,
  }) {
    return _transferLists.lookupInterPropertyItemToAdd(
      divisionId: divisionId,
      costCenterId: costCenterId,
      listId: listId,
      sourceDivisionId: sourceDivisionId,
      query: query,
      page: page,
      pageSize: pageSize,
    );
  }

  @override
  Future<Result<Response, AppError>> addInterPropertyItemToWarehouse({
    required String divisionId,
    required String costCenterId,
    required String listId,
    required String sourceDivisionId,
    required String sourceStockItemId,
    required String targetStockId,
    required String targetCostTypeId,
  }) {
    return _transferLists.addInterPropertyItemToWarehouse(
      divisionId: divisionId,
      costCenterId: costCenterId,
      listId: listId,
      sourceDivisionId: sourceDivisionId,
      sourceStockItemId: sourceStockItemId,
      targetStockId: targetStockId,
      targetCostTypeId: targetCostTypeId,
    );
  }

  @override
  Future<Result<Response, AppError>> lookupInterPropertySyncedItem({
    required String divisionId,
    required String costCenterId,
    required String listId,
    required String stockItemId,
    required String query,
    required int page,
    required int pageSize,
  }) {
    return _transferLists.lookupInterPropertySyncedItem(
      divisionId: divisionId,
      costCenterId: costCenterId,
      listId: listId,
      stockItemId: stockItemId,
      query: query,
      page: page,
      pageSize: pageSize,
    );
  }

  @override
  Future<Result<Response, AppError>> remapInterPropertyItem({
    required String divisionId,
    required String costCenterId,
    required String listId,
    required String stockItemId,
    required String sourceDivisionId,
    required String sourceStockItemId,
  }) {
    return _transferLists.remapInterPropertyItem(
      divisionId: divisionId,
      costCenterId: costCenterId,
      listId: listId,
      stockItemId: stockItemId,
      sourceDivisionId: sourceDivisionId,
      sourceStockItemId: sourceStockItemId,
    );
  }

  @override
  Future<Result<Response, AppError>> lookupInterPropertyItemToMerge({
    required String divisionId,
    required String costCenterId,
    required String listId,
    required String sourceDivisionId,
    required String sourceStockItemId,
    required String query,
    required int page,
    required int pageSize,
  }) {
    return _transferLists.lookupInterPropertyItemToMerge(
      divisionId: divisionId,
      costCenterId: costCenterId,
      listId: listId,
      sourceDivisionId: sourceDivisionId,
      sourceStockItemId: sourceStockItemId,
      query: query,
      page: page,
      pageSize: pageSize,
    );
  }

  @override
  Future<Result<Response, AppError>> mergeInterPropertyItem({
    required String divisionId,
    required String costCenterId,
    required String listId,
    required String stockItemId,
    required String sourceDivisionId,
    required String sourceStockItemId,
  }) {
    return _transferLists.mergeInterPropertyItem(
      divisionId: divisionId,
      costCenterId: costCenterId,
      listId: listId,
      stockItemId: stockItemId,
      sourceDivisionId: sourceDivisionId,
      sourceStockItemId: sourceStockItemId,
    );
  }

  @override
  Future<Result<Response, AppError>> getOneTransferListProductById({
    required String divisionId,
    required String costCenterId,
    required String stockItemId,
  }) {
    return _transferLists.getOneProductById(
      divisionId: divisionId,
      costCenterId: costCenterId,
      stockItemId: stockItemId,
    );
  }

  @override
  Future<Result<Response, AppError>> updateTransferListProduct({
    required String divisionId,
    required String costCenterId,
    required String stockItemId,
    required Map<String, dynamic> fields,
  }) {
    return _transferLists.updateProduct(
      divisionId: divisionId,
      costCenterId: costCenterId,
      stockItemId: stockItemId,
      fields: fields,
    );
  }

  // -- Inventories --

  @override
  Future<Result<Response, AppError>> addInventoryItem({
    required String divisionId,
    required String inventoryId,
    required String stockId,
  }) async {
    return _inventory.addItem(
      divisionId: divisionId,
      inventoryId: inventoryId,
      stockId: stockId,
    );
  }

  @override
  Future<Result<Response, AppError>> searchInventoryLists({
    required String divisionId,
    required String query,
    required String filter,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
  }) async {
    return _inventory.search(
      divisionId: divisionId,
      query: query,
      filter: filter,
      orderByField: orderByField,
      orderType: orderType,
      page: page,
      pageSize: pageSize,
    );
  }

  @override
  Future<Result<Response, AppError>> getInventoryListById({
    required String divisionId,
    required String id,
    required bool includeItems,
  }) async {
    return _inventory.getListById(
      divisionId: divisionId,
      id: id,
      includeItems: includeItems,
    );
  }

  @override
  Future<Result<Response, AppError>> lockInventoryControlList({
    required String divisionId,
    required String inventoryId,
  }) async {
    return _inventory.lockList(
      divisionId: divisionId,
      inventoryId: inventoryId,
    );
  }

  @override
  Future<Result<Response, AppError>> unlockInventoryControlList({
    required String divisionId,
    required String inventoryId,
    required bool forceUnlock,
  }) async {
    return _inventory.unlockList(
      divisionId: divisionId,
      inventoryId: inventoryId,
      forceUnlock: forceUnlock,
    );
  }

  @override
  Future<Result<Response, AppError>> searchInventoryItems({
    required String divisionId,
    required String inventoryId,
    required String query,
    required String filter,
    required String orderByField,
    required String orderType,
    required String? categoryId,
    required int page,
    required int pageSize,
  }) async {
    return _inventory.searchStockItems(
      divisionId: divisionId,
      inventoryId: inventoryId,
      query: query,
      filter: filter,
      orderByField: orderByField,
      orderType: orderType,
      categoryId: categoryId,
      page: page,
      pageSize: pageSize,
    );
  }

  @override
  Future<Result<Response, AppError>> updateInventoryItemQty({
    required String divisionId,
    required String inventoryId,
    required String productStockId,
    required double qty,
  }) async {
    return _inventory.updateItemQty(
      divisionId: divisionId,
      inventoryId: inventoryId,
      productStockId: productStockId,
      qty: qty,
    );
  }

  @override
  Future<Result<Response, AppError>> overwriteInventoryItemEanCodes({
    required String divisionId,
    required String inventoryId,
    required String productStockId,
    required List<String> eanCodes,
  }) async {
    return _inventory.overwriteItemEanCodes(
      divisionId: divisionId,
      inventoryId: inventoryId,
      productStockId: productStockId,
      eanCodes: eanCodes,
    );
  }

  @override
  Future<Result<Response, AppError>> confirmInventoryControlList({
    required String divisionId,
    required String inventoryId,
    required String firstApprover,
    required String firstSignature,
    required String? secondApprover,
    required String? secondSignature,
  }) async {
    return _inventory.confirmList(
      divisionId: divisionId,
      inventoryId: inventoryId,
      firstApprover: firstApprover,
      firstSignature: firstSignature,
      secondApprover: secondApprover,
      secondSignature: secondSignature,
    );
  }

  @override
  Future<Result<Response, AppError>> inventoryCategoryLookup({
    required String divisionId,
    required String inventoryId,
    required String query,
    required int page,
    required int pageSize,
  }) {
    return _inventory.categoryLookup(
      divisionId: divisionId,
      inventoryId: inventoryId,
      query: query,
      page: page,
      pageSize: pageSize,
    );
  }

  @override
  Future<Result<Response, AppError>> getInventoryBookingVoucherUrl({
    required String divisionId,
    required String costCenterId,
    required String bookingId,
    required String language,
  }) {
    return _inventory.getBookingVoucherUrl(
      divisionId: divisionId,
      costCenterId: costCenterId,
      bookingId: bookingId,
      language: language,
    );
  }

  // --- Supplier ---

  @override
  Future<Result<Response, AppError>> searchSupplier({
    required String divisionId,
    required String costCenterId,
    required String query,
    required String filter,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
  }) async {
    return _supplier.search(
      divisionId: divisionId,
      costCenterId: costCenterId,
      query: query,
      filter: filter,
      orderByField: orderByField,
      orderType: orderType,
      page: page,
      pageSize: pageSize,
    );
  }

  @override
  Future<Result<Response, AppError>> favoriteSupplier({
    required String divisionId,
    required String costCenterId,
    required String supplierId,
    required bool isFavorite,
  }) async {
    return _supplier.favorite(
      divisionId: divisionId,
      costCenterId: costCenterId,
      supplierId: supplierId,
      isFavorite: isFavorite,
    );
  }

  @override
  Future<Result<Response, AppError>> getSupplierDeliveryPlan({
    required String divisionId,
    required String costCenterId,
    required String supplierId,
    required int leadTime,
    required String orderDate,
    required String period,
  }) async {
    return _supplier.getDeliveryPlan(
      divisionId: divisionId,
      costCenterId: costCenterId,
      supplierId: supplierId,
      leadTime: leadTime,
      orderDate: orderDate,
      period: period,
    );
  }

  @override
  Future<Result<Response, AppError>> requestCustomerIdFromSupplier({
    required String divisionId,
    required String costCenterId,
    required String supplierId,
    required String? replyToEmail,
  }) async {
    return _supplier.requestCustomerId(
      divisionId: divisionId,
      costCenterId: costCenterId,
      supplierId: supplierId,
      replyToEmail: replyToEmail,
    );
  }

  @override
  Future<Result<Response, AppError>> getSupplierById({
    required String divisionId,
    required String costCenterId,
    required String supplierId,
  }) async {
    return _supplier.getSupplierById(
      divisionId: divisionId,
      costCenterId: costCenterId,
      supplierId: supplierId,
    );
  }

  @override
  Future<Result<Response, AppError>> enableSendOrderAfterApprovalForSupplier({
    required String divisionId,
    required String costCenterId,
    required String supplierId,
    required bool enabled,
  }) {
    return _supplier.enableSendOrderAfterApproval(
      divisionId: divisionId,
      costCenterId: costCenterId,
      supplierId: supplierId,
      enabled: enabled,
    );
  }

  @override
  Future<Result<Response, AppError>> searchSupplierDocuments({
    required String divisionId,
    required String costCenterId,
    required String? supplierId,
    required String? query,
    required String? category,
    required String? documentType,
    required String status,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
  }) =>
      _supplier.searchDocuments(
        divisionId: divisionId,
        costCenterId: costCenterId,
        supplierId: supplierId,
        query: query,
        documentType: documentType,
        category: category,
        status: status,
        orderByField: orderByField,
        orderType: orderType,
        page: page,
        pageSize: pageSize,
      );

  @override
  Future<Result<Response, AppError>> getSupplierDocumentById({
    required String divisionId,
    required String costCenterId,
    required int documentId,
    required bool includeFiles,
  }) =>
      _supplier.getDocumentById(
        divisionId: divisionId,
        costCenterId: costCenterId,
        documentId: documentId,
        includeFiles: includeFiles,
      );

  @override
  Future<Result<Response, AppError>> getAllSupplierDocumentFiles({
    required String divisionId,
    required String costCenterId,
    required int documentId,
  }) =>
      _supplier.getAllDocumentFiles(
        divisionId: divisionId,
        costCenterId: costCenterId,
        documentId: documentId,
      );

  @override
  Future<Result<Response, AppError>> getSupplierDocumentFileById({
    required String divisionId,
    required String costCenterId,
    required String documentFileId,
  }) =>
      _supplier.getDocumentFileById(
        divisionId: divisionId,
        costCenterId: costCenterId,
        documentFileId: documentFileId,
      );

  @override
  Future<Result<Response, AppError>> getSupplierDocumentFileUrl({
    required String divisionId,
    required String costCenterId,
    required String documentFileId,
  }) =>
      _supplier.getDocumentFileUrl(
        divisionId: divisionId,
        costCenterId: costCenterId,
        documentFileId: documentFileId,
      );

  @override
  Future<Result<Response, AppError>> searchSupplierDocumentProducts({
    required String divisionId,
    required String query,
    required String supplierId,
    required int documentId,
    required List<String> categories,
    required String assignmentStatus,
    required String fileStatus,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
    required String costCenterId,
    required String language,
    required bool includeFiles,
  }) =>
      _supplier.searchDocumentProducts(
        divisionId: divisionId,
        query: query,
        costCenterId: costCenterId,
        supplierId: supplierId,
        documentId: documentId,
        categories: categories,
        assignmentStatus: assignmentStatus,
        fileStatus: fileStatus,
        orderByField: orderByField,
        orderType: orderType,
        page: page,
        pageSize: pageSize,
        language: language,
        includeFiles: includeFiles,
      );

  @override
  Future<Result<Response, AppError>> getAllSupplierDocumentProductFiles({
    required String divisionId,
    required String costCenterId,
    required int documentId,
    required int positionId,
    required String? itemType,
  }) =>
      _supplier.getAllDocumentProductFiles(
        divisionId: divisionId,
        costCenterId: costCenterId,
        documentId: documentId,
        positionId: positionId,
        itemType: itemType,
      );

  @override
  Future<Result<Response, AppError>> getAllSupplierDocumentProductDocuments({
    required String divisionId,
    required String costCenterId,
    required int positionId,
    required String? itemType,
    required bool includeFiles,
  }) =>
      _supplier.getAllDocumentProductDocuments(
        divisionId: divisionId,
        costCenterId: costCenterId,
        positionId: positionId,
        itemType: itemType,
        includeFiles: includeFiles,
      );

  // -- Announcements --

  @override
  Future<Result<Response, AppError>> getUnreadAnnouncements(
    String divisionId,
  ) {
    return _announcements.getUnread(divisionId);
  }

  @override
  Future<Result<Response, AppError>> markAnnouncementAsRead({
    required String divisionId,
    required int announcementId,
  }) {
    return _announcements.markAsRead(
      divisionId: divisionId,
      announcementId: announcementId,
    );
  }

  // -- Warehouse --

  @override
  Future<Result<Response, AppError>> lookupWarehouseItem({
    required String divisionId,
    required String scope,
    required String query,
    required int page,
    required int pageSize,
    required String language,
  }) async {
    return _warehouse.itemLookup(
      divisionId: divisionId,
      scope: scope,
      query: query,
      page: page,
      pageSize: pageSize,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> lookupWarehouseStores({
    required String divisionId,
    String? lookupInDivisionId,
    required String? storeCostCenterId,
    required String query,
    required int page,
    required int pageSize,
    required String language,
  }) async {
    return _warehouse.storeLookup(
      divisionId: divisionId,
      storeCostCenterId: storeCostCenterId,
      query: query,
      page: page,
      pageSize: pageSize,
      language: language,
      lookupInDivisionId: lookupInDivisionId,
    );
  }

  @override
  Future<Result<Response, AppError>> lookupWarehouseCostTypes({
    required String divisionId,
    String? lookupInDivisionId,
    required String? storeCostCenterId,
    required String? storeId,
    required String query,
    required int page,
    required int pageSize,
    required String language,
  }) async {
    return _warehouse.costTypeLookup(
      divisionId: divisionId,
      storeCostCenterId: storeCostCenterId,
      storeId: storeId,
      query: query,
      page: page,
      pageSize: pageSize,
      language: language,
      lookupInDivisionId: lookupInDivisionId,
    );
  }

  @override
  Future<Result<Response, AppError>> lookupWarehouseCostCenters({
    required String divisionId,
    String? lookupInDivisionId,
    required String query,
    required int page,
    required int pageSize,
    required String language,
  }) async {
    return _warehouse.costCenterLookup(
      divisionId: divisionId,
      query: query,
      page: page,
      pageSize: pageSize,
      language: language,
      lookupInDivisionId: lookupInDivisionId,
    );
  }

  @override
  Future<Result<Response, AppError>> lookupWarehouseLocalSuppliers({
    required String divisionId,
    required String query,
    required int page,
    required int pageSize,
    required String language,
  }) async {
    return _warehouse.localSupplierLookup(
      divisionId: divisionId,
      query: query,
      page: page,
      pageSize: pageSize,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> lookupWarehouseVat({
    required String divisionId,
    required String query,
    required int page,
    required int pageSize,
    required String language,
  }) async {
    return _warehouse.valLookup(
      divisionId: divisionId,
      query: query,
      page: page,
      pageSize: pageSize,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> lookupWarehouseCategories({
    required String divisionId,
    required String query,
    required int page,
    required int pageSize,
    required String language,
  }) async {
    return _warehouse.categoryLookup(
      divisionId: divisionId,
      query: query,
      page: page,
      pageSize: pageSize,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> lookupWarehousePackingUnits({
    required String divisionId,
    required String query,
    required int page,
    required int pageSize,
    required String language,
  }) async {
    return _warehouse.packingUnitLookup(
      divisionId: divisionId,
      query: query,
      page: page,
      pageSize: pageSize,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> lookupWarehouseInventoryUnit({
    required String divisionId,
    required String query,
    required int page,
    required int pageSize,
    required String language,
  }) async {
    return _warehouse.inventoryUnitLookup(
      divisionId: divisionId,
      query: query,
      page: page,
      pageSize: pageSize,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> lookupWarehouseDepositItem({
    required String divisionId,
    required String query,
    required int page,
    required int pageSize,
    required String language,
  }) async {
    return _warehouse.depositItemLookup(
      divisionId: divisionId,
      query: query,
      page: page,
      pageSize: pageSize,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> lookupWarehouseConfig({
    required String divisionId,
    required String scope,
    required String query,
    required int page,
    required int pageSize,
    required String language,
  }) async {
    return _warehouse.configLookup(
      divisionId: divisionId,
      scope: scope,
      query: query,
      page: page,
      pageSize: pageSize,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> lookupWarehouseCurrency({
    required String divisionId,
    required String query,
    required int page,
    required int pageSize,
    required String language,
  }) async {
    return _warehouse.currencyLookup(
      divisionId: divisionId,
      query: query,
      page: page,
      pageSize: pageSize,
      language: language,
    );
  }

  // -- i18n --

  @override
  Future<Result<Response, AppError>> pushLabelsIds(
    List<Map<String, String>> labels,
  ) async {
    return _i18n.pushLabelsIds(labels);
  }

  // -- Bookings --

  @override
  Future<Result<Response, AppError>> searchBookings({
    required String divisionId,
    required String costCenterId,
    required String query,
    required String status,
    required String? fromDate,
    required String? toDate,
    required String orderByField,
    required String orderType,
    required List<String> transferType,
    required int page,
    required int pageSize,
    bool onlyThisCostCenter = false,
    bool includeProducts = false,
  }) async {
    return _bookings.search(
      divisionId: divisionId,
      costCenterId: costCenterId,
      query: query,
      status: status,
      fromDate: fromDate,
      toDate: toDate,
      orderByField: orderByField,
      orderType: orderType,
      transferType: transferType,
      onlyThisCostCenter: onlyThisCostCenter,
      page: page,
      pageSize: pageSize,
      includeProducts: includeProducts,
    );
  }

  @override
  Future<Result<Response, AppError>> getOneBookingById({
    required String divisionId,
    required String bookingId,
    bool includeProducts = false,
  }) async {
    return _bookings.getOneById(
      divisionId: divisionId,
      bookingId: bookingId,
      includeProducts: includeProducts,
    );
  }

  @override
  Future<Result<Response, AppError>> deleteBooking({
    required String divisionId,
    required String id,
  }) async {
    return _bookings.delete(
      divisionId: divisionId,
      id: id,
    );
  }

  @override
  Future<Result<Response, AppError>> updateBooking({
    required String divisionId,
    required String bookingId,
    required Map<String, dynamic> fields,
  }) async {
    return _bookings.update(
      divisionId: divisionId,
      bookingId: bookingId,
      fields: fields,
    );
  }

  @override
  Future<Result<Response, AppError>> confirmBooking({
    required String divisionId,
    required String bookingId,
  }) async {
    return _bookings.confirm(
      divisionId: divisionId,
      bookingId: bookingId,
    );
  }

  @override
  Future<Result<Response, AppError>> createBooking({
    required String divisionId,
    required String constCenterId,
    required String transferType,
    required String bookingDate,
    required String reasonId,
    required String language,
    required String reference,
  }) async {
    return _bookings.create(
      divisionId: divisionId,
      constCenterId: constCenterId,
      transferType: transferType,
      bookingDate: bookingDate,
      reasonId: reasonId,
      language: language,
      reference: reference,
    );
  }

  @override
  Future<Result<Response, AppError>> bookingReasonLookup({
    required String divisionId,
    required String transferType,
    String query = '',
    required int page,
    required int pageSize,
  }) async {
    return _bookings.reasonLookup(
      divisionId: divisionId,
      transferType: transferType,
      query: query,
      page: page,
      pageSize: pageSize,
    );
  }

  @override
  Future<Result<Response, AppError>> getBookingVoucherUrl({
    required String divisionId,
    required String bookingId,
    required String language,
  }) async {
    return _bookings.getVoucherUrl(
      divisionId: divisionId,
      bookingId: bookingId,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> searchBookingItems({
    required String divisionId,
    required String costCenterId,
    required String bookingId,
    required String query,
    required String filter,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
  }) async {
    return _bookings.searchItems(
      divisionId: divisionId,
      costCenterId: costCenterId,
      bookingId: bookingId,
      query: query,
      filter: filter,
      orderByField: orderByField,
      orderType: orderType,
      page: page,
      pageSize: pageSize,
    );
  }

  @override
  Future<Result<Response, AppError>> getAllBookingItems({
    required String divisionId,
    required String bookingId,
    required String language,
  }) {
    return _bookings.getAllItems(
      divisionId: divisionId,
      bookingId: bookingId,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> getOneBookingItemById({
    required String divisionId,
    required String bookingId,
    required int id,
    required String language,
  }) {
    return _bookings.getOneItemById(
      divisionId: divisionId,
      bookingId: bookingId,
      id: id,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> deleteBookingItem({
    required String divisionId,
    required int id,
    required String bookingId,
  }) async {
    return _bookings.deleteItem(
      divisionId: divisionId,
      id: id,
      bookingId: bookingId,
    );
  }

  @override
  Future<Result<Response, AppError>> updateBookingItem({
    required String divisionId,
    required String bookingId,
    required int id,
    required Map<String, dynamic> fields,
  }) async {
    return _bookings.updateItem(
      divisionId: divisionId,
      bookingId: bookingId,
      id: id,
      fields: fields,
    );
  }

  @override
  Future<Result<Response, AppError>> addBookingItem({
    required String divisionId,
    required String bookingId,
    required String productStockId,
  }) async {
    return _bookings.addItem(
      divisionId: divisionId,
      bookingId: bookingId,
      productStockId: productStockId,
    );
  }

  @override
  Future<Result<Response, AppError>> confirmBookingItem({
    required String divisionId,
    required String bookingId,
    required int id,
  }) async {
    return _bookings.confirmItem(
      divisionId: divisionId,
      bookingId: bookingId,
      id: id,
    );
  }

  // -- Booking approval requests --

  @override
  Future<Result<Response, AppError>> requestBookingApproval({
    required String divisionId,
    required String approvalRequestId,
    required String approverId,
  }) async {
    return _bookings.requestApproval(
      divisionId: divisionId,
      approvalRequestId: approvalRequestId,
      approverId: approverId,
    );
  }

  @override
  Future<Result<Response, AppError>> searchBookingApprovalRequests({
    required String divisionId,
    required String costCenterKey,
    required String query,
    required String? fromDate,
    required String? toDate,
    required String filter,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
    required String language,
  }) async {
    return _bookings.searchApprovalRequests(
      divisionId: divisionId,
      costCenterId: costCenterKey,
      query: query,
      fromDate: fromDate,
      toDate: toDate,
      filter: filter,
      orderByField: orderByField,
      orderType: orderType,
      page: page,
      pageSize: pageSize,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> updateBookingApprovalRequest({
    required String divisionId,
    required String approvalRequestId,
    required bool isApproved,
    required String? comment,
    required String? nextApproverId,
  }) async {
    return _bookings.updateApprovalRequest(
      divisionId: divisionId,
      approvalRequestId: approvalRequestId,
      isApproved: isApproved,
      comment: comment,
      nextApproverId: nextApproverId,
    );
  }

  @override
  Future<Result<Response, AppError>> getOneBookingApprovalRequestById({
    required String divisionId,
    required String costCenterId,
    required String approvalRequestId,
    required String language,
    bool includeProducts = false,
    bool includeLogRecords = false,
    bool includeMessages = false,
  }) async {
    return _bookings.getOneApprovalRequestById(
      divisionId: divisionId,
      costCenterId: costCenterId,
      approvalRequestId: approvalRequestId,
      language: language,
      includeProducts: includeProducts,
      includeLogRecords: includeLogRecords,
      includeMessages: includeMessages,
    );
  }

  @override
  Future<Result<Response, AppError>> getBookingApprovalRequestProducts({
    required String divisionId,
    required String costCenterId,
    required String approvalRequestId,
    required String language,
  }) async {
    return _bookings.getApprovalRequestProducts(
      divisionId: divisionId,
      costCenterId: costCenterId,
      approvalRequestId: approvalRequestId,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> searchBookingApprovalRequestProducts({
    required String divisionId,
    required String costCenterId,
    required String approvalRequestId,
    required String language,
    required String query,
    required int page,
    required int pageSize,
  }) {
    return _bookings.searchApprovalRequestProducts(
      divisionId: divisionId,
      costCenterId: costCenterId,
      approvalRequestId: approvalRequestId,
      language: language,
      query: query,
      page: page,
      pageSize: pageSize,
    );
  }

  @override
  Future<Result<Response, AppError>> getOneBookingApprovalRequestProductById({
    required String divisionId,
    required String costCenterId,
    required String approvalRequestId,
    required int productId,
    required String language,
  }) {
    return _bookings.getOneApprovalRequestProductById(
      divisionId: divisionId,
      costCenterId: costCenterId,
      approvalRequestId: approvalRequestId,
      productId: productId,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> resetBookingApprovalRequest({
    required String divisionId,
    required String approvalRequestId,
  }) async {
    return _bookings.resetApprovalRequest(
      divisionId: divisionId,
      approvalRequestId: approvalRequestId,
    );
  }

  @override
  Future<Result<Response, AppError>> updateBookingApprovalRequestProductQty({
    required String divisionId,
    required String approvalRequestId,
    required int productId,
    required double orderUnitQty,
  }) async {
    return _bookings.updateApprovalRequestProductQty(
      divisionId: divisionId,
      approvalRequestId: approvalRequestId,
      productId: productId,
      orderUnitQty: orderUnitQty,
    );
  }

  @override
  Future<Result<Response, AppError>> updateBookingApprovalRequestProduct({
    required String divisionId,
    required String approvalRequestId,
    required int productId,
    required bool isConfirmed,
  }) async {
    return _bookings.updateApprovalRequestProduct(
      divisionId: divisionId,
      approvalRequestId: approvalRequestId,
      productId: productId,
      isConfirmed: isConfirmed,
    );
  }

  @override
  Future<Result<Response, AppError>> pullBookingApprovalRequestMessages({
    required String divisionId,
    required String approvalRequestId,
    required int? pullFromMessageId,
    required int size,
    required String order,
  }) async {
    return _bookings.pullApprovalRequestMessages(
      divisionId: divisionId,
      approvalRequestId: approvalRequestId,
      pullFromMessageId: pullFromMessageId,
      size: size,
      order: order,
    );
  }

  @override
  Future<Result<Response, AppError>> sendBookingApprovalRequestMessage({
    required String divisionId,
    required String approvalRequestId,
    required String message,
  }) async {
    return _bookings.sendApprovalRequestMessage(
      divisionId: divisionId,
      approvalRequestId: approvalRequestId,
      message: message,
    );
  }

  @override
  Future<Result<Response, AppError>> getBookingApprovalRequestLogRecords({
    required String divisionId,
    required String approvalRequestId,
  }) async {
    return _bookings.getApprovalRequestLogRecords(
      divisionId: divisionId,
      approvalRequestId: approvalRequestId,
    );
  }

  @override
  Future<Result<Response, AppError>> getBookingApprovalRequestDocuments({
    required String divisionId,
    required String approvalRequestId,
  }) async {
    return _bookings.getApprovalRequestDocuments(
      divisionId: divisionId,
      approvalRequestId: approvalRequestId,
    );
  }

  @override
  Future<Result<Response, AppError>> getBookingApprovalRequestApprovalTrail({
    required String divisionId,
    required String approvalRequestId,
  }) async {
    return _bookings.getApprovalTrail(
      divisionId: divisionId,
      approvalRequestId: approvalRequestId,
    );
  }

  @override
  Future<Result<Response, AppError>> deleteBookingApprovalRequestDocument({
    required String divisionId,
    required String approvalRequestId,
    required String documentId,
  }) async {
    return _bookings.deleteApprovalRequestDocument(
      divisionId: divisionId,
      approvalRequestId: approvalRequestId,
      documentId: documentId,
    );
  }

  // -- Purchase requests --

  @override
  Future<Result<Response, AppError>> searchPurchaseRequests({
    required String divisionId,
    required String constCenterId,
    required String? query,
    required String? costCenterName,
    required String? category,
    required String? supplier,
    required String? itemDescription,
    required String status,
    required String? fromDate,
    required String? toDate,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
    bool includeFilesCount = false,
  }) async {
    return _purchaseRequests.search(
      divisionId: divisionId,
      costCenterId: constCenterId,
      query: query,
      costCenterName: costCenterName,
      category: category,
      supplier: supplier,
      itemDescription: itemDescription,
      status: status,
      fromDate: fromDate,
      toDate: toDate,
      orderByField: orderByField,
      orderType: orderType,
      page: page,
      pageSize: pageSize,
      includeFilesCount: includeFilesCount,
    );
  }

  @override
  Future<Result<Response, AppError>> searchPurchaseRequestProducts({
    required String divisionId,
    required String purchaseRequestId,
    required String language,
    required String query,
    required int page,
    required int pageSize,
    required bool includeFilesCount,
    required bool includeFiles,
  }) async {
    return _purchaseRequests.searchProducts(
      divisionId: divisionId,
      purchaseRequestId: purchaseRequestId,
      language: language,
      query: query,
      page: page,
      pageSize: pageSize,
      includeFiles: includeFiles,
      includeFilesCount: includeFilesCount,
    );
  }

  @override
  Future<Result<Response, AppError>> getOnePurchaseRequestById({
    required String divisionId,
    required String constCenterId,
    required String purchaseRequestId,
    String language = 'de_DE',
    bool includeProducts = false,
    bool includeLogRecords = false,
    bool includeMessages = false,
    bool includeFiles = false,
  }) async {
    return _purchaseRequests.getOneById(
      divisionId: divisionId,
      costCenterId: constCenterId,
      purchaseRequestId: purchaseRequestId,
      language: language,
      includeProducts: includeProducts,
      includeLogRecords: includeLogRecords,
      includeMessages: includeMessages,
      includeFiles: includeFiles,
    );
  }

  @override
  Future<Result<Response, AppError>> getOnePurchaseRequestProductById({
    required String divisionId,
    required String purchaseRequestId,
    required int productId,
    required String language,
    required bool includeFiles,
    required bool includeFilesCount,
  }) async {
    return _purchaseRequests.getOneProductById(
      divisionId: divisionId,
      purchaseRequestId: purchaseRequestId,
      productId: productId,
      language: language,
      includeFiles: includeFiles,
      includeFilesCount: includeFilesCount,
    );
  }

  @override
  Future<Result<Response, AppError>> getPurchaseRequestLogRecords({
    required String divisionId,
    required String purchaseRequestId,
  }) async {
    return _purchaseRequests.getLogRecords(
      divisionId: divisionId,
      purchaseRequestId: purchaseRequestId,
    );
  }

  @override
  Future<Result<Response, AppError>> getPurchaseRequestDocuments({
    required String divisionId,
    required String purchaseRequestId,
  }) async {
    return _purchaseRequests.getDocuments(
      divisionId: divisionId,
      purchaseRequestId: purchaseRequestId,
    );
  }

  @override
  Future<Result<Response, AppError>> getPurchaseRequestApprovalTrail({
    required String divisionId,
    required String purchaseRequestId,
  }) async {
    return _purchaseRequests.getApprovalTrail(
      divisionId: divisionId,
      purchaseRequestId: purchaseRequestId,
    );
  }

  @override
  Future<Result<Response, AppError>> deletePurchaseRequestDocument({
    required String divisionId,
    required String purchaseRequestId,
    required String documentId,
  }) async {
    return _purchaseRequests.deleteDocument(
      divisionId: divisionId,
      purchaseRequestId: purchaseRequestId,
      documentId: documentId,
    );
  }

  @override
  Future<Result<Response, AppError>> getPurchaseRequestMessages({
    required String divisionId,
    required String purchaseRequestId,
    required int lastMessageId,
    String? olderThan,
    int page = 0,
    int pageSize = 20,
  }) async {
    return _purchaseRequests.getMessages(
      divisionId: divisionId,
      purchaseRequestId: purchaseRequestId,
      olderThan: olderThan,
      lastMessageId: lastMessageId,
      page: page,
      pageSize: pageSize,
    );
  }

  @override
  Future<Result<Response, AppError>> pullPurchaseRequestMessages({
    required String divisionId,
    required String purchaseRequestId,
    int? pullFromMessageId,
    int size = 10,
    required String order,
  }) async {
    return _purchaseRequests.pullMessages(
      divisionId: divisionId,
      purchaseRequestId: purchaseRequestId,
      pullFromMessageId: pullFromMessageId,
      size: size,
      order: order,
    );
  }

  @override
  Future<Result<Response, AppError>> sendPurchaseRequestMessage({
    required String divisionId,
    required String purchaseRequestId,
    required String message,
  }) async {
    return _purchaseRequests.sendMessage(
      divisionId: divisionId,
      purchaseRequestId: purchaseRequestId,
      message: message,
    );
  }

  @override
  Future<Result<Response, AppError>> resetPurchaseRequest({
    required String divisionId,
    required String purchaseRequestId,
  }) async {
    return _purchaseRequests.reset(
      divisionId: divisionId,
      purchaseRequestId: purchaseRequestId,
    );
  }

  @override
  Future<Result<Response, AppError>> deletePurchaseRequest({
    required String divisionId,
    required String purchaseRequestId,
  }) async {
    return _purchaseRequests.delete(
      divisionId: divisionId,
      purchaseRequestId: purchaseRequestId,
    );
  }

  @override
  Future<Result<Response, AppError>> movePurchaseRequestToCart({
    required String divisionId,
    required String purchaseRequestId,
  }) async {
    return _purchaseRequests.moveToCart(
      divisionId: divisionId,
      purchaseRequestId: purchaseRequestId,
    );
  }

  @override
  Future<Result<Response, AppError>> updatePurchaseRequestProduct({
    required String divisionId,
    required String purchaseRequestId,
    required int productId,
    double? orderUnitQty,
    required bool isConfirmed,
  }) async {
    return _purchaseRequests.updateProduct(
      divisionId: divisionId,
      purchaseRequestId: purchaseRequestId,
      productId: productId,
      isConfirmed: isConfirmed,
    );
  }

  @override
  Future<Result<Response, AppError>> updatePurchaseRequestProductQty({
    required String divisionId,
    required String purchaseRequestId,
    required int productId,
    required double orderUnitQty,
  }) async {
    return _purchaseRequests.updateProductQty(
      divisionId: divisionId,
      purchaseRequestId: purchaseRequestId,
      productId: productId,
      orderUnitQty: orderUnitQty,
    );
  }

  @override
  Future<Result<Response, AppError>> updatePurchaseRequest({
    required String divisionId,
    required String purchaseRequestId,
    required bool isApproved,
    String? comment,
    String? nextApproverId,
  }) async {
    return _purchaseRequests.update(
      divisionId: divisionId,
      purchaseRequestId: purchaseRequestId,
      isApproved: isApproved,
      comment: comment,
      nextApproverId: nextApproverId,
    );
  }

  @override
  Future<Result<Response, AppError>> requestPurchaseRequestApproval({
    required String divisionId,
    required String purchaseRequestId,
    required String type,
    required String approverId,
  }) async {
    return _purchaseRequests.requestApproval(
      divisionId: divisionId,
      purchaseRequestId: purchaseRequestId,
      type: type,
      approverId: approverId,
    );
  }

  @override
  Future<Result<Response, AppError>> getPurchaseRequestProductOffers({
    required String divisionId,
    required String purchaseRequestId,
    required String language,
    required String metaId,
  }) {
    return _purchaseRequests.getProductOffers(
      divisionId: divisionId,
      purchaseRequestId: purchaseRequestId,
      language: language,
      metaId: metaId,
    );
  }

  @override
  Future<Result<Response, AppError>> changePurchaseRequestProductOffer({
    required String divisionId,
    required String purchaseRequestId,
    required String language,
    required String metaId,
    required String replacementOfferType,
    required int replacementOfferPositionId,
    required String originalType,
    required int originalPositionId,
  }) {
    return _purchaseRequests.changeProductOffer(
      divisionId: divisionId,
      purchaseRequestId: purchaseRequestId,
      language: language,
      metaId: metaId,
      replacementOfferType: replacementOfferType,
      replacementOfferPositionId: replacementOfferPositionId,
      originalType: originalType,
      originalPositionId: originalPositionId,
    );
  }

  @override
  Future<Result<Response, AppError>> searchPurchaseRequestProductLog({
    required String divisionId,
    required String purchaseRequestId,
    required int id,
    required int page,
    required int pageSize,
  }) {
    return _purchaseRequests.searchProductLog(
      divisionId: divisionId,
      purchaseRequestId: purchaseRequestId,
      id: id,
      page: page,
      pageSize: pageSize,
    );
  }

  @override
  Future<Result<Response, AppError>> getPurchaseRequestProductOfferById({
    required String divisionId,
    required String purchaseRequestId,
    required int id,
    required String language,
  }) {
    return _purchaseRequests.getProductOfferById(
      divisionId: divisionId,
      purchaseRequestId: purchaseRequestId,
      id: id,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> updatePurchaseRequestProductOffer({
    required String divisionId,
    required String purchaseRequestId,
    required int id,
    required Map<String, dynamic> fields,
  }) =>
      _purchaseRequests.updateProductOffer(
        divisionId: divisionId,
        purchaseRequestId: purchaseRequestId,
        id: id,
        fields: fields,
      );

  @override
  Future<Result<Response, AppError>> addPurchaseRequestProductOffer({
    required String divisionId,
    required String purchaseRequestId,
    required int id,
    required String supplierId,
    required String supplierProductId,
    required String orderUnit,
    required String contentUnit,
    required double contentUnitsPerOrderUnit,
    required double inventoryUnitsPerOrderUnit,
    required double orderUnitPrice,
    String? ean,
    int? taxId,
  }) =>
      _purchaseRequests.addProductOffer(
        divisionId: divisionId,
        purchaseRequestId: purchaseRequestId,
        id: id,
        supplierId: supplierId,
        supplierProductId: supplierProductId,
        orderUnit: orderUnit,
        contentUnit: contentUnit,
        contentUnitsPerOrderUnit: contentUnitsPerOrderUnit,
        inventoryUnitsPerOrderUnit: inventoryUnitsPerOrderUnit,
        orderUnitPrice: orderUnitPrice,
        ean: ean,
        taxId: taxId,
      );

  @override
  Future<Result<Response, AppError>> replacePurchaseRequestProductOffer({
    required String divisionId,
    required String purchaseRequestId,
    required int id,
    required int replacementId,
  }) =>
      _purchaseRequests.replaceProductOffer(
        divisionId: divisionId,
        purchaseRequestId: purchaseRequestId,
        id: id,
        replacementId: replacementId,
      );

  @override
  Future<Result<Response, AppError>> purchaseRequestProductSupplierLookup({
    required String divisionId,
    required String query,
    required int page,
    required int pageSize,
    required String language,
  }) =>
      _purchaseRequests.productSupplierLookup(
        divisionId: divisionId,
        query: query,
        page: page,
        pageSize: pageSize,
        language: language,
      );

  @override
  Future<Result<Response, AppError>>
      purchaseRequestProductOfferReplacementLookup({
    required String divisionId,
    required String purchaseRequestId,
    required int id,
    required String query,
    required int page,
    required int pageSize,
    required String language,
  }) =>
          _purchaseRequests.productOfferReplacementLookup(
            divisionId: divisionId,
            purchaseRequestId: purchaseRequestId,
            id: id,
            query: query,
            page: page,
            pageSize: pageSize,
            language: language,
          );

  @override
  Future<Result<Response, AppError>> sendPurchaseRequestOrders({
    required String divisionId,
    required String purchaseRequestId,
    required String language,
  }) {
    return _purchaseRequests.sendOrders(
      divisionId: divisionId,
      purchaseRequestId: purchaseRequestId,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> purchaseRequestCostTypeLookup({
    required String divisionId,
    required String costCenterId,
    required String query,
    required String? lookupDivisionId,
    required String? lookupCostCenterId,
  }) =>
      _purchaseRequests.costTypeLookup(
        divisionId: divisionId,
        costCenterId: costCenterId,
        query: query,
        lookupDivisionId: lookupDivisionId,
        lookupCostCenterId: lookupCostCenterId,
      );

  @override
  Future<Result<Response, AppError>> updatePurchaseRequestProductCostType({
    required String divisionId,
    required String costCenterId,
    required String productCostCenterId,
    required int itemPositionId,
    required String itemType,
    required String costTypeId,
    required String purchaseRequestId,
    required int productId,
  }) =>
      _purchaseRequests.updateProductCostType(
        divisionId: divisionId,
        costCenterId: costCenterId,
        productCostCenterId: productCostCenterId,
        itemPositionId: itemPositionId,
        itemType: itemType,
        costTypeId: costTypeId,
        purchaseRequestId: purchaseRequestId,
        productId: productId,
      );

  @override
  Future<Result<Response, AppError>> calculatePurchaseRequestBudget({
    required String divisionId,
    required String costCenterId,
    required String targetCostCenterId,
    required String purchaseRequestId,
  }) =>
      _purchaseRequests.calculateBudget(
        divisionId: divisionId,
        costCenterId: costCenterId,
        targetCostCenterId: targetCostCenterId,
        purchaseRequestId: purchaseRequestId,
      );

  @override
  Future<Result<Response, AppError>> getPurchaseRequestExternalCommentHistory({
    required String divisionId,
    required String costCenterId,
    required String? orderDivisionId,
    required String? orderCostCenterId,
    required String supplierId,
    required String purchaseRequestId,
    required int leadTime,
  }) =>
      _purchaseRequests.getExternalCommentHistory(
        divisionId: divisionId,
        costCenterId: costCenterId,
        orderDivisionId: orderDivisionId,
        orderCostCenterId: orderCostCenterId,
        supplierId: supplierId,
        purchaseRequestId: purchaseRequestId,
        leadTime: leadTime,
      );

  // -- Capex --

  @override
  Future<Result<Response, AppError>> searchCapexApprovalRequests({
    required String orgUnitKey,
    required String costCenterKey,
    required String query,
    required String? originalDivisionId,
    required String filter,
    String? fromDate,
    String? toDate,
    required String orderByField,
    required String orderType,
    int page = 0,
    int pageSize = 10,
    required String language,
  }) {
    return _capex.search(
      orgUnitKey: orgUnitKey,
      costCenterKey: costCenterKey,
      query: query,
      originalDivisionId: originalDivisionId,
      filter: filter,
      fromDate: fromDate,
      toDate: toDate,
      orderByField: orderByField,
      orderType: orderType,
      page: page,
      pageSize: pageSize,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> getOneCapexApprovalRequestById({
    required String orgUnitKey,
    required String costCenterKey,
    required String approvalRequestId,
    required String language,
    bool includeApprovalTrail = false,
    bool includeMessages = false,
    bool includeLog = false,
    bool includeFiles = false,
    bool includeInvoices = false,
    bool includeAttributes = false,
    bool includeAdditionalSuppliers = false,
  }) {
    return _capex.getOneById(
      orgUnitKey: orgUnitKey,
      costCenterKey: costCenterKey,
      approvalRequestId: approvalRequestId,
      language: language,
      includeApprovalTrail: includeApprovalTrail,
      includeMessages: includeMessages,
      includeLog: includeLog,
      includeFiles: includeFiles,
      includeInvoices: includeInvoices,
      includeAttributes: includeAttributes,
      includeAdditionalSuppliers: includeAdditionalSuppliers,
    );
  }

  @override
  Future<Result<Response, AppError>> getCapexApprovalRequestLogRecords({
    required String orgUnitKey,
    required String approvalRequestId,
    required String language,
  }) {
    return _capex.getLogRecords(
      orgUnitKey: orgUnitKey,
      approvalRequestId: approvalRequestId,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> getCapexApprovalRequestDocuments({
    required String orgUnitKey,
    required String approvalRequestId,
    required String language,
  }) {
    return _capex.getDocuments(
      orgUnitKey: orgUnitKey,
      approvalRequestId: approvalRequestId,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> getCapexApprovalRequestTrail({
    required String orgUnitKey,
    required String approvalRequestId,
    required String language,
  }) {
    return _capex.getApprovalTrail(
      orgUnitKey: orgUnitKey,
      approvalRequestId: approvalRequestId,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> deleteCapexApprovalRequestDocument({
    required String orgUnitKey,
    required String approvalRequestId,
    required String documentId,
  }) {
    return _capex.deleteDocument(
      orgUnitKey: orgUnitKey,
      approvalRequestId: approvalRequestId,
      documentId: documentId,
    );
  }

  @override
  Future<Result<Response, AppError>> pullCapexApprovalRequestMessages({
    required String orgUnitKey,
    required String approvalRequestId,
    int? pullFromMessageId,
    int size = 10,
    required String order,
  }) {
    return _capex.pullMessages(
      orgUnitKey: orgUnitKey,
      approvalRequestId: approvalRequestId,
      order: order,
      pullFromMessageId: pullFromMessageId,
      size: size,
    );
  }

  @override
  Future<Result<Response, AppError>> sendCapexApprovalRequestMessage({
    required String orgUnitKey,
    required String approvalRequestId,
    required String message,
  }) {
    return _capex.sendMessage(
      orgUnitKey: orgUnitKey,
      approvalRequestId: approvalRequestId,
      message: message,
    );
  }

  @override
  Future<Result<Response, AppError>> resetCapexApprovalRequest({
    required String orgUnitKey,
    required String approvalRequestId,
  }) {
    return _capex.reset(
      orgUnitKey: orgUnitKey,
      approvalRequestId: approvalRequestId,
    );
  }

  @override
  Future<Result<Response, AppError>> deleteCapexApprovalRequest({
    required String orgUnitKey,
    required String approvalRequestId,
  }) {
    return _capex.delete(
      orgUnitKey: orgUnitKey,
      approvalRequestId: approvalRequestId,
    );
  }

  @override
  Future<Result<Response, AppError>> updateCapexApprovalRequest({
    required String orgUnitKey,
    required String approvalRequestId,
    required Map<String, dynamic> fields,
  }) {
    return _capex.update(
      orgUnitKey: orgUnitKey,
      approvalRequestId: approvalRequestId,
      fields: fields,
    );
  }

  @override
  Future<Result<Response, AppError>> requestCapexApproval({
    required String orgUnitKey,
    required String approvalRequestId,
    required String approverId,
  }) {
    return _capex.requestApproval(
      orgUnitKey: orgUnitKey,
      approvalRequestId: approvalRequestId,
      approverId: approverId,
    );
  }

  @override
  Future<Result<Response, AppError>> approveCapexApprovalRequest({
    required String orgUnitKey,
    required String costCenterKey,
    required String approvalRequestId,
    String? nextApproverId,
    String? comment,
  }) {
    return _capex.approve(
      orgUnitKey: orgUnitKey,
      costCenterKey: costCenterKey,
      approvalRequestId: approvalRequestId,
      nextApproverId: nextApproverId,
      comment: comment,
    );
  }

  @override
  Future<Result<Response, AppError>> declineCapexApprovalRequest({
    required String orgUnitKey,
    required String costCenterKey,
    required String approvalRequestId,
    String? comment,
  }) {
    return _capex.decline(
      orgUnitKey: orgUnitKey,
      costCenterKey: costCenterKey,
      approvalRequestId: approvalRequestId,
      comment: comment,
    );
  }

  @override
  Future<Result<Response, AppError>> closeCapexApprovalRequest({
    required String orgUnitKey,
    required String costCenterKey,
    required String approvalRequestId,
  }) {
    return _capex.close(
      orgUnitKey: orgUnitKey,
      costCenterKey: costCenterKey,
      approvalRequestId: approvalRequestId,
    );
  }

  @override
  Future<Result<Response, AppError>> createCapexApprovalRequest({
    required String orgUnitKey,
    required String costCenterKey,
    required String costCenterAccountancyId,
    required String supplierId,
    required String name,
    required double totalAmount,
    required String currencyCode,
    required String capexTypeId,
    String? description,
    String? accountingInstruction,
  }) {
    return _capex.createApproval(
      orgUnitKey: orgUnitKey,
      costCenterKey: costCenterKey,
      costCenterAccountancyId: costCenterAccountancyId,
      supplierId: supplierId,
      name: name,
      totalAmount: totalAmount,
      currencyCode: currencyCode,
      capexTypeId: capexTypeId,
      description: description,
      accountingInstruction: accountingInstruction,
    );
  }

  @override
  Future<Result<Response, AppError>> capexApprovalRequestDivisionFilterLookup({
    required String divisionId,
    required int page,
    required int pageSize,
    required String query,
  }) {
    return _capex.approvalDivisionFilterLookup(
      divisionId: divisionId,
      page: page,
      pageSize: pageSize,
      query: query,
    );
  }

  // -- Invoices --

  @override
  Future<Result<Response, AppError>> searchInvoices({
    required String orgUnitKey,
    required String costCenterKey,
    String query = '',
    String invoiceId = '',
    String supplierInvoiceId = '',
    String supplierName = '',
    required String filter,
    required String? fromDate,
    required String? toDate,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
  }) async {
    return _invoices.search(
      orgUnitKey: orgUnitKey,
      costCenterKey: costCenterKey,
      query: query,
      invoiceId: invoiceId,
      supplierInvoiceId: supplierInvoiceId,
      supplierName: supplierName,
      filter: filter,
      fromDate: fromDate,
      toDate: toDate,
      orderByField: orderByField,
      orderType: orderType,
      page: page,
      pageSize: pageSize,
    );
  }

  @override
  Future<Result<Response, AppError>> searchDigitalInvoicesArchive({
    required String orgUnitKey,
    required String costCenterKey,
    String query = '',
    String invoiceId = '',
    String supplierInvoiceId = '',
    String supplierName = '',
    required String filter,
    required String? fromDate,
    required String? toDate,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
  }) async {
    return _invoices.searchDigitalInvoicesArchive(
      orgUnitKey: orgUnitKey,
      costCenterKey: costCenterKey,
      query: query,
      invoiceId: invoiceId,
      supplierInvoiceId: supplierInvoiceId,
      supplierName: supplierName,
      filter: filter,
      fromDate: fromDate,
      toDate: toDate,
      orderByField: orderByField,
      orderType: orderType,
      page: page,
      pageSize: pageSize,
    );
  }

  @override
  Future<Result<Response, AppError>> getOneInvoiceById({
    required String orgUnitKey,
    required String invoiceId,
    bool includeAccountAssignmentRecords = false,
    bool includeLogRecords = false,
    bool includeFiles = false,
  }) async {
    return _invoices.getOneById(
      orgUnitKey: orgUnitKey,
      invoiceId: invoiceId,
      includeAccountAssignmentRecords: includeAccountAssignmentRecords,
      includeLogRecords: includeLogRecords,
      includeFiles: includeFiles,
    );
  }

  @override
  Future<Result<Response, AppError>> getOneDigitalInvoiceByIdFromArchive({
    required String orgUnitKey,
    required String costCenterKey,
    required String invoiceId,
    bool includeAccountAssignmentRecords = false,
    bool includeLogRecords = false,
    bool includeFiles = false,
  }) async {
    return _invoices.getOneDigitalByIdFromArchive(
      orgUnitKey: orgUnitKey,
      costCenterKey: costCenterKey,
      invoiceId: invoiceId,
      includeAccountAssignmentRecords: includeAccountAssignmentRecords,
      includeLogRecords: includeLogRecords,
      includeFiles: includeFiles,
    );
  }

  @override
  Future<Result<Response, AppError>> getInvoiceAccountAssignmentRecords({
    required String orgUnitKey,
    required String costCenterKey,
    required String invoiceId,
  }) async {
    return _invoices.getAccountAssignmentRecords(
      orgUnitKey: orgUnitKey,
      costCenterKey: costCenterKey,
      invoiceId: invoiceId,
    );
  }

  @override
  Future<Result<Response, AppError>> getInvoiceLogRecords({
    required String orgUnitKey,
    required String invoiceId,
  }) async {
    return _invoices.getLogRecords(
      orgUnitKey: orgUnitKey,
      invoiceId: invoiceId,
    );
  }

  @override
  Future<Result<Response, AppError>> getArchiveInvoiceLogRecords({
    required String orgUnitKey,
    required String invoiceId,
  }) async {
    return _invoices.getArchiveLogRecords(
      orgUnitKey: orgUnitKey,
      invoiceId: invoiceId,
    );
  }

  @override
  Future<Result<Response, AppError>> getDigitalInvoiceFileUrl({
    required String orgUnitKey,
    required String costCenterKey,
    required String scanningId,
    required String language,
  }) async {
    return _invoices.getDigitalFileUrl(
      orgUnitKey: orgUnitKey,
      costCenterKey: costCenterKey,
      scanningId: scanningId,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> requestInvoiceApproval({
    required String orgUnitKey,
    required String invoiceId,
    required String approverId,
  }) async {
    return _invoices.requestApproval(
      orgUnitKey: orgUnitKey,
      invoiceId: invoiceId,
      approverId: approverId,
    );
  }

  @override
  Future<Result<Response, AppError>> resetInvoice({
    required String orgUnitKey,
    required String invoiceId,
    required String comment,
  }) async {
    return _invoices.reset(
      orgUnitKey: orgUnitKey,
      invoiceId: invoiceId,
      comment: comment,
    );
  }

  @override
  Future<Result<Response, AppError>> approveInvoice({
    required String orgUnitKey,
    required String invoiceId,
    String? nextApproverId,
  }) async {
    return _invoices.approve(
      orgUnitKey: orgUnitKey,
      invoiceId: invoiceId,
      nextApproverId: nextApproverId,
    );
  }

  @override
  Future<Result<Response, AppError>> getInvoiceApprovalTrail({
    required String divisionId,
    required String invoiceId,
  }) {
    return _invoices.getApprovalTrail(
      divisionId: divisionId,
      invoiceId: invoiceId,
    );
  }

  @override
  Future<Result<Response, AppError>> approveDigitalInvoice({
    required String divisionId,
    required String invoiceId,
  }) {
    return _invoices.approveDigital(
      divisionId: divisionId,
      invoiceId: invoiceId,
    );
  }

  @override
  Future<Result<Response, AppError>> getDigitalInvoicesProcessingList({
    required String divisionId,
    required String? startDate,
    required String? endDate,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
  }) {
    return _invoices.getDigitalProcessingList(
      divisionId: divisionId,
      startDate: startDate,
      endDate: endDate,
      orderByField: orderByField,
      orderType: orderType,
      page: page,
      pageSize: pageSize,
    );
  }

  @override
  Future<Result<Response, AppError>> getOneDigitalInvoiceProcessingById({
    required String divisionId,
    required String scanningId,
  }) {
    return _invoices.getOneDigitalProcessingById(
      divisionId: divisionId,
      scanningId: scanningId,
    );
  }

  @override
  Future<Result<Response, AppError>> getDigitalInvoiceTaxBreakdown({
    required String divisionId,
    required String invoiceId,
  }) =>
      _invoices.getDigitalInvoiceTaxBreakdown(
        divisionId: divisionId,
        invoiceId: invoiceId,
      );

  // -- Approvals --

  @override
  Future<Result<Response, AppError>> getCountOfNotApprovedApprovals({
    required String orgUnitKey,
    required String costCenterKey,
    bool includePurchaseRequests = false,
    bool includeInvoices = false,
    bool includeBookingApprovalRequests = false,
    bool includeReceivingApprovalRequests = false,
    bool includeCapexApprovalRequests = false,
  }) async {
    return _approvals.getCountOfNotApproved(
      orgUnitKey: orgUnitKey,
      costCenterKey: costCenterKey,
      includePurchaseRequests: includePurchaseRequests,
      includeInvoices: includeInvoices,
      includeBookingApprovalRequests: includeBookingApprovalRequests,
      includeReceivingApprovalRequests: includeReceivingApprovalRequests,
      includeCapexApprovalRequests: includeCapexApprovalRequests,
    );
  }

  @override
  Future<Result<Response, AppError>> approversLookup({
    required String divisionId,
    required String costCenterId,
    required String id,
    required String type,
    required int level,
  }) async {
    return _approvals.approversLookup(
      divisionId: divisionId,
      costCenterId: costCenterId,
      id: id,
      type: type,
      level: level,
    );
  }

  @override
  Future<Result<bool, AppError>> uploadInvoice({
    required String unitId,
    required String filePath,
  }) async {
    return apiTransport.uploadScannedFile(
      unitId: unitId,
      filePath: filePath,
    );
  }

  // -- Session --

  @override
  Future<Result<Response, AppError>> logSession({
    required String orgUnitKey,
    required String costCenterKey,
    required String platform,
    required String appVersion,
    required String platformCode,
    String? deviceId,
  }) async {
    return _session.log(
      orgUnitKey: orgUnitKey,
      costCenterKey: costCenterKey,
      platform: platform,
      appVersion: appVersion,
      platformCode: platformCode,
      deviceId: deviceId,
    );
  }

  @override
  Future<Result<Response, AppError>> getSessionPermissions(
    String orgUnitKey,
  ) async {
    return _session.getPermissions(orgUnitKey);
  }

  @override
  Future<Result<Response, AppError>> refreshSession() async {
    return _session.refreshSession();
  }

  // --- Receivings ---

  @override
  Future<Result<Response, AppError>> searchAllReceivings({
    required String orgUnitKey,
    required String costCenterKey,
    String query = '',
    String supplierName = '',
    String productName = '',
    String userName = '',
    String costCenterName = '',
    String requestedByCostCenterName = '',
    required String language,
    String? fromDate,
    String? toDate,
    required String type,
    required String filter,
    required String orderByField,
    required String orderType,
    int page = 0,
    int pageSize = 20,
  }) {
    return _receivings.searchAll(
      orgUnitKey: orgUnitKey,
      costCenterKey: costCenterKey,
      query: query,
      supplierName: supplierName,
      productName: productName,
      userName: userName,
      costCenterName: costCenterName,
      requestedByCostCenterName: requestedByCostCenterName,
      language: language,
      fromDate: fromDate,
      toDate: toDate,
      type: type,
      filter: filter,
      orderByField: orderByField,
      orderType: orderType,
      page: page,
      pageSize: pageSize,
    );
  }

  @override
  Future<Result<Response, AppError>> searchReceivingOrders({
    required String orgUnitKey,
    required String costCenterKey,
    String query = '',
    String orderId = '',
    String supplierName = '',
    String productName = '',
    String userName = '',
    String costCenterName = '',
    String requestedByCostCenterName = '',
    required String language,
    String? fromDate,
    String? toDate,
    String? deliveryFromDate,
    String? deliveryToDate,
    required String filter,
    required String orderByField,
    required String orderType,
    int page = 0,
    int pageSize = 20,
  }) {
    return _receivings.searchOrders(
      orgUnitKey: orgUnitKey,
      costCenterKey: costCenterKey,
      query: query,
      orderId: orderId,
      supplierName: supplierName,
      productName: productName,
      userName: userName,
      costCenterName: costCenterName,
      requestedByCostCenterName: requestedByCostCenterName,
      language: language,
      fromDate: fromDate,
      toDate: toDate,
      deliveryFromDate: deliveryFromDate,
      deliveryToDate: deliveryToDate,
      filter: filter,
      orderByField: orderByField,
      orderType: orderType,
      page: page,
      pageSize: pageSize,
    );
  }

  @override
  Future<Result<Response, AppError>> getOneReceivingById({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required bool includeFiles,
    required bool includeItemsTotal,
    required bool includeSplitDeliveryReport,
    required bool includeProvisionalBooking,
    required bool includeTaxBreakdown,
    required bool includeCostTypeBreakdown,
    required bool includeFreightCostsOrderTotal,
    required bool includeRelatedOrders,
  }) async {
    return _receivings.getOneByOrderId(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
      includeFiles: includeFiles,
      includeItemsTotal: includeItemsTotal,
      includeSplitDeliveryReport: includeSplitDeliveryReport,
      includeProvisionalBooking: includeProvisionalBooking,
      includeTaxBreakdown: includeTaxBreakdown,
      includeCostTypeBreakdown: includeCostTypeBreakdown,
      includeFreightCostsOrderTotal: includeFreightCostsOrderTotal,
      includeRelatedOrders: includeRelatedOrders,
    );
  }

  @override
  Future<Result<Response, AppError>> createFromOrder({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    String? deliveryNoteUid,
    required String language,
  }) async {
    return _receivings.createFromOrder(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
      deliveryNoteUid: deliveryNoteUid,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> createReceiving({
    required String divisionId,
    required String costCenterId,
    required String supplierId,
    String? accountancyCostCenterId,
  }) async {
    return _receivings.create(
      divisionId: divisionId,
      costCenterId: costCenterId,
      supplierId: supplierId,
      accountancyCostCenterId: accountancyCostCenterId,
    );
  }

  @override
  Future<Result<Response, AppError>> updateReceiving({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required Map<String, dynamic> fields,
  }) async {
    return _receivings.update(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
      fields: fields,
    );
  }

  @override
  Future<Result<Response, AppError>> getReceivingItemsTotals({
    required String divisionId,
    required String costCenterId,
    required String orderId,
  }) async {
    return _receivings.getItemsTotals(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
    );
  }

  @override
  Future<Result<Response, AppError>> confirmReceiving({
    required String divisionId,
    required String costCenterId,
    required String orderId,
  }) async {
    return _receivings.confirm(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
    );
  }

  @override
  Future<Result<Response, AppError>> requestReceivingApproval({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required String approverId,
  }) async {
    return _receivings.requestApproval(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
      approverId: approverId,
    );
  }

  @override
  Future<Result<Response, AppError>> deleteReceivingByOrderId({
    required String divisionId,
    required String costCenterId,
    required String orderId,
  }) async {
    return _receivings.deleteOneByOrderId(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
    );
  }

  @override
  Future<Result<Response, AppError>> updateReceivingDiscount({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required double discount,
  }) async {
    return _receivings.updateDiscount(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
      discount: discount,
    );
  }

  @override
  Future<Result<Response, AppError>> searchReceivingItems({
    required String orgUnitKey,
    required String costCenterKey,
    required String orderId,
    String query = '',
    required String orderByField,
    required String orderType,
    int page = 0,
    int pageSize = 20,
  }) {
    return _receivings.searchItems(
      orgUnitKey: orgUnitKey,
      costCenterKey: costCenterKey,
      orderId: orderId,
      query: query,
      orderByField: orderByField,
      orderType: orderType,
      page: page,
      pageSize: pageSize,
    );
  }

  @override
  Future<Result<Response, AppError>> lookupReceivingItems({
    required String orgUnitKey,
    required String costCenterKey,
    required String orderId,
    String query = '',
    required String scope,
    required String language,
    int page = 0,
    int pageSize = 20,
  }) {
    return _receivings.lookupItems(
      orgUnitKey: orgUnitKey,
      costCenterKey: costCenterKey,
      orderId: orderId,
      query: query,
      scope: scope,
      language: language,
      page: page,
      pageSize: pageSize,
    );
  }

  @override
  Future<Result<Response, AppError>> addItemToReceiving({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required String scope,
    required String itemType,
    required String internalId,
    required int positionId,
    required String language,
  }) {
    return _receivings.addItem(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
      scope: scope,
      itemType: itemType,
      internalId: internalId,
      positionId: positionId,
      language: language,
    );
  }

  @override
  Future<Result<List<Result<Response, AppError>>, AppError>>
      addItemsToReceiving({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required String scope,
    required String language,
    required List<Map<String, dynamic>> itemsData,
  }) {
    return _receivings.addItems(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
      scope: scope,
      language: language,
      itemsData: itemsData,
    );
  }

  @override
  Future<Result<Response, AppError>> deleteItemFromReceiving({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required int itemId,
  }) {
    return _receivings.deleteItem(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
      itemId: itemId,
    );
  }

  @override
  Future<Result<Response, AppError>> deleteAllReceivingItems({
    required String divisionId,
    required String costCenterId,
    required String orderId,
  }) {
    return _receivings.deleteAllItems(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
    );
  }

  @override
  Future<Result<Response, AppError>> addAllReceivingItemsFromOrders({
    required String divisionId,
    required String costCenterId,
    required String originalOrderId,
  }) =>
      _receivings.addAllItemsFromOrders(
        divisionId: divisionId,
        costCenterId: costCenterId,
        originalOrderId: originalOrderId,
      );

  @override
  Future<Result<Response, AppError>> getOneReceivingItemById({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required int itemId,
    required bool includeFreightCostsOrderItemTotal,
  }) {
    return _receivings.getOneItemById(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
      itemId: itemId,
      includeFreightCostsOrderItemTotal: includeFreightCostsOrderItemTotal,
    );
  }

  @override
  Future<Result<Response, AppError>> updateReceivingItem({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required int itemId,
    required Map<String, dynamic> fields,
  }) {
    return _receivings.updateItem(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
      itemId: itemId,
      fields: fields,
    );
  }

  @override
  Future<Result<Response, AppError>> updateReceivingItemDiscount({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required int itemId,
    required double discount,
  }) {
    return _receivings.updateItemDiscount(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
      itemId: itemId,
      discount: discount,
    );
  }

  @override
  Future<Result<Response, AppError>> updateReceivingItemTotalAmount({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required int itemId,
    required double totalAmount,
  }) {
    return _receivings.updateItemTotalAmount(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
      itemId: itemId,
      totalAmount: totalAmount,
    );
  }

  @override
  Future<Result<Response, AppError>> addReceivingItemSplitDelivery({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required int itemId,
  }) {
    return _receivings.addItemSplitDelivery(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
      itemId: itemId,
    );
  }

  @override
  Future<Result<Response, AppError>> deleteReceivingItemSplitDelivery({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required int itemId,
    required int splitDeliveryId,
  }) {
    return _receivings.deleteItemSplitDelivery(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
      itemId: itemId,
      splitDeliveryId: splitDeliveryId,
    );
  }

  @override
  Future<Result<Response, AppError>> updateReceivingItemSplitDelivery({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required int itemId,
    required int splitDeliveryId,
    required Map<String, dynamic> fields,
  }) {
    return _receivings.updateItemSplitDelivery(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
      itemId: itemId,
      splitDeliveryId: splitDeliveryId,
      fields: fields,
    );
  }

  @override
  Future<Result<Response, AppError>> getReceivingItemSplitDelivery({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required int itemId,
  }) {
    return _receivings.getItemSplitDelivery(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
      itemId: itemId,
    );
  }

  @override
  Future<Result<Response, AppError>> updateReceivingItemTotals({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required int itemId,
    required String unitType,
    required double unitQty,
    required double unitPrice,
  }) {
    return _receivings.updateItemTotals(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
      itemId: itemId,
      unitType: unitType,
      unitQty: unitQty,
      unitPrice: unitPrice,
    );
  }

  @override
  Future<Result<Response, AppError>> addReceivingItemAttributeValue({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required int itemId,
    required String type,
    required dynamic value,
  }) {
    return _receivings.addAttributeValue(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
      itemId: itemId,
      type: type,
      value: value,
    );
  }

  @override
  Future<Result<bool, AppError>> addReceivingItemFile({
    required String divisionId,
    required String orderId,
    required int itemId,
    required File file,
    String? comment,
  }) {
    return _receivings.addItemFile(
      divisionId: divisionId,
      orderId: orderId,
      itemId: itemId,
      file: file,
      comment: comment,
    );
  }

  @override
  Future<Result<Response, AppError>> getReceivingItemFiles({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required int itemId,
  }) {
    return _receivings.getItemFiles(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
      itemId: itemId,
    );
  }

  @override
  Future<Result<Response, AppError>> deleteReceivingItemFile({
    required String divisionId,
    required String costCenterId,
    required String fileId,
  }) {
    return _receivings.deleteItemFile(
      divisionId: divisionId,
      costCenterId: costCenterId,
      fileId: fileId,
    );
  }

  @override
  Future<Result<Response, AppError>> lookupReceivingItemInventoryUnits({
    required String divisionId,
    required String costCenterId,
    required String stockItemId,
    required int page,
    required int pageSize,
  }) {
    return _receivings.lookupItemInventoryUnits(
      divisionId: divisionId,
      costCenterId: costCenterId,
      stockItemId: stockItemId,
      page: page,
      pageSize: pageSize,
    );
  }

  @override
  Future<Result<Response, AppError>> getRelatedReceivingDepositItems({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    bool includeStockItems = true,
  }) {
    return _receivings.getRelatedDepositItems(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
      includeStockItems: includeStockItems,
    );
  }

  @override
  Future<Result<Response, AppError>> addReceivingDepositItem({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required String depositItemId,
  }) {
    return _receivings.addDepositItem(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
      depositItemId: depositItemId,
    );
  }

  @override
  Future<Result<Response, AppError>> updateReceivingDepositItem({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required String depositItemId,
    required Map<String, dynamic> fields,
  }) {
    return _receivings.updateDepositItem(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
      depositItemId: depositItemId,
      fields: fields,
    );
  }

  @override
  Future<Result<Response, AppError>> deleteReceivingDepositItem({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required String depositItemId,
  }) {
    return _receivings.deleteDepositItem(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
      depositItemId: depositItemId,
    );
  }

  @override
  Future<Result<Response, AppError>> addReceivingRelatedOrder({
    required String divisionId,
    required String costCenterId,
    required String originalOrderId,
    required String orderId,
  }) {
    return _receivings.addRelatedOrder(
      divisionId: divisionId,
      costCenterId: costCenterId,
      originalOrderId: originalOrderId,
      orderId: orderId,
    );
  }

  @override
  Future<Result<Response, AppError>> deleteReceivingRelatedOrder({
    required String divisionId,
    required String costCenterId,
    required String originalOrderId,
    required String orderId,
  }) {
    return _receivings.deleteRelatedOrder(
      divisionId: divisionId,
      costCenterId: costCenterId,
      originalOrderId: originalOrderId,
      orderId: orderId,
    );
  }

  @override
  Future<Result<Response, AppError>> getReceivingAvailableOrders({
    required String divisionId,
    required String costCenterId,
    required String originalOrderId,
    required String supplierId,
  }) {
    return _receivings.getAvailableOrders(
      divisionId: divisionId,
      costCenterId: costCenterId,
      originalOrderId: originalOrderId,
      supplierId: supplierId,
    );
  }

  @override
  Future<Result<Response, AppError>> getReceivingRelatedOrders({
    required String divisionId,
    required String costCenterId,
    required String originalOrderId,
  }) {
    return _receivings.getRelatedOrders(
      divisionId: divisionId,
      costCenterId: costCenterId,
      originalOrderId: originalOrderId,
    );
  }

  @override
  Future<Result<Response, AppError>> getPartialReceivingHistory({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required String language,
  }) {
    return _receivings.getPartialHistory(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> getReceivingSubsequentDeliveryItems({
    required String divisionId,
    required String costCenterId,
    required String orderId,
  }) {
    return _receivings.getSubsequentDeliveryItems(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
    );
  }

  @override
  Future<Result<Response, AppError>>
      updateReceivingSubsequentDeliveryItemStatus({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required int subsequentDeliveryItemId,
    required bool status,
  }) {
    return _receivings.updateSubsequentDeliveryItemStatus(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
      subsequentDeliveryItemId: subsequentDeliveryItemId,
      status: status,
    );
  }

  @override
  Future<Result<Response, AppError>> toggleAllReceivingSubsequentDeliveryItems({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required bool status,
  }) {
    return _receivings.toggleAllSubsequentDeliveryItems(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
      status: status,
    );
  }

  @override
  Future<Result<Response, AppError>> createReceivingProvisionalBooking({
    required String divisionId,
    required String costCenterId,
    required String orderId,
  }) {
    return _receivings.createProvisionalBooking(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
    );
  }

  @override
  Future<Result<Response, AppError>> searchReceivingApprovalRequests({
    required String divisionId,
    required String costCenterKey,
    String query = '',
    String? fromDate,
    String? toDate,
    required String status,
    required String orderByField,
    required String orderType,
    int page = 0,
    int pageSize = 20,
    required String language,
    bool includeFilesCount = false,
  }) {
    return _receivings.searchApprovalRequests(
      orgUnitKey: divisionId,
      costCenterKey: costCenterKey,
      query: query,
      fromDate: fromDate,
      toDate: toDate,
      status: status,
      orderByField: orderByField,
      orderType: orderType,
      page: page,
      pageSize: pageSize,
      language: language,
      includeFilesCount: includeFilesCount,
    );
  }

  @override
  Future<Result<Response, AppError>> updateReceivingApprovalRequest({
    required String divisionId,
    required String approvalRequestId,
    required String status,
    String? comment,
    String? nextApproverId,
  }) async {
    return _receivings.updateApprovalRequest(
      divisionId: divisionId,
      approvalRequestId: approvalRequestId,
      status: status,
      comment: comment,
      nextApproverId: nextApproverId,
    );
  }

  @override
  Future<Result<Response, AppError>> getOneReceivingApprovalRequestById({
    required String divisionId,
    required String costCenterKey,
    required String approvalRequestId,
    String language = 'de_DE',
    bool includeProducts = false,
    bool includeLogRecords = false,
    bool includeMessages = false,
    bool includeFiles = false,
  }) async {
    return _receivings.getOneApprovalRequestById(
      divisionId: divisionId,
      costCenterKey: costCenterKey,
      approvalRequestId: approvalRequestId,
      language: language,
      includeProducts: includeProducts,
      includeLogRecords: includeLogRecords,
      includeMessages: includeMessages,
      includeFiles: includeFiles,
    );
  }

  @override
  Future<Result<Response, AppError>> getReceivingApprovalRequestProducts({
    required String divisionId,
    required String costCenterKey,
    required String approvalRequestId,
    String language = 'de_DE',
  }) async {
    return _receivings.getApprovalRequestProducts(
      divisionId: divisionId,
      costCenterKey: costCenterKey,
      approvalRequestId: approvalRequestId,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>>
      updateReceivingApprovalRequestProductComment({
    required String divisionId,
    required String costCenterKey,
    required String approvalRequestId,
    required int productId,
    String? comment,
  }) async {
    return _receivings.updateApprovalRequestProductComment(
      divisionId: divisionId,
      costCenterKey: costCenterKey,
      approvalRequestId: approvalRequestId,
      productId: productId,
      comment: comment,
    );
  }

  @override
  Future<Result<Response, AppError>> pullReceivingApprovalRequestMessages({
    required String divisionId,
    required String approvalRequestId,
    int? pullFromMessageId,
    int size = 10,
    required String order,
  }) async {
    return _receivings.pullApprovalRequestMessages(
      divisionId: divisionId,
      approvalRequestId: approvalRequestId,
      pullFromMessageId: pullFromMessageId,
      size: size,
      order: order,
    );
  }

  @override
  Future<Result<Response, AppError>> sendReceivingApprovalRequestMessage({
    required String divisionId,
    required String approvalRequestId,
    required String message,
  }) async {
    return _receivings.sendApprovalRequestMessage(
      divisionId: divisionId,
      approvalRequestId: approvalRequestId,
      message: message,
    );
  }

  @override
  Future<Result<Response, AppError>> getReceivingApprovalRequestApprovalTrail({
    required String divisionId,
    required String approvalRequestId,
  }) async {
    return _receivings.getApprovalTrail(
      divisionId: divisionId,
      approvalRequestId: approvalRequestId,
    );
  }

  @override
  Future<Result<Response, AppError>> getReceivingApprovalRequestDocuments({
    required String divisionId,
    required String approvalRequestId,
  }) async {
    return _receivings.getApprovalRequestDocuments(
      divisionId: divisionId,
      approvalRequestId: approvalRequestId,
    );
  }

  @override
  Future<Result<Response, AppError>> deleteReceivingApprovalRequestDocument({
    required String divisionId,
    required String approvalRequestId,
    required String documentId,
  }) async {
    return _receivings.deleteApprovalRequestDocument(
      divisionId: divisionId,
      approvalRequestId: approvalRequestId,
      documentId: divisionId,
    );
  }

  @override
  Future<Result<Response, AppError>> resetReceivingApprovalRequest({
    required String divisionId,
    required String approvalRequestId,
  }) async {
    return _receivings.resetApprovalRequest(
      divisionId: divisionId,
      approvalRequestId: approvalRequestId,
    );
  }

  @override
  Future<Result<Response, AppError>> getReceivingApprovalRequestLogRecords({
    required String divisionId,
    required String approvalRequestId,
  }) async {
    return _receivings.getApprovalRequestLogRecords(
      divisionId: divisionId,
      approvalRequestId: approvalRequestId,
    );
  }

  @override
  Future<Result<Response, AppError>> requestReceivingApprovalRequestApproval({
    required String divisionId,
    required String costCenterId,
    required String approvalRequestId,
    required String approverId,
  }) async {
    return _receivings.requestApprovalRequestApproval(
      divisionId: divisionId,
      costCenterId: costCenterId,
      approvalRequestId: approvalRequestId,
      approverId: approverId,
    );
  }

  @override
  Future<Result<Response, AppError>> getOneReceivingDepositItemById({
    required String divisionId,
    required String costCenterId,
    required String orderId,
    required String depositItemId,
  }) {
    return _receivings.getOneDepositItemById(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
      depositItemId: depositItemId,
    );
  }

  @override
  Future<Result<Response, AppError>> getReceivingProvisionalBookingTotalInfo({
    required String divisionId,
    required String costCenterId,
  }) {
    return _receivings.getProvisionalBookingTotalInfo(
      divisionId: divisionId,
      costCenterId: costCenterId,
    );
  }

  @override
  Future<Result<Response, AppError>> searchReceivingProvisionalBookings({
    required String divisionId,
    required String costCenterId,
    required String query,
    required String supplierId,
    required String supplierName,
    required String productName,
    required String userName,
    required String costCenterName,
    required String requestedByCostCenterName,
    required String? fromDate,
    required String? toDate,
    required String status,
    required String filter,
    required String type,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
    required String language,
    required bool includeFilesCount,
    required bool includeFiles,
  }) {
    return _receivings.searchProvisionalBookings(
      divisionId: divisionId,
      costCenterId: costCenterId,
      query: query,
      supplierId: supplierId,
      supplierName: supplierName,
      productName: productName,
      userName: userName,
      costCenterName: costCenterName,
      requestedByCostCenterName: requestedByCostCenterName,
      fromDate: fromDate,
      toDate: toDate,
      status: status,
      filter: filter,
      type: type,
      orderByField: orderByField,
      orderType: orderType,
      page: page,
      pageSize: pageSize,
      language: language,
      includeFilesCount: includeFilesCount,
      includeFiles: includeFiles,
    );
  }

  @override
  Future<Result<Response, AppError>> resetReceivingsProvisionalBooking({
    required String divisionId,
    required String costCenterId,
    required int id,
  }) {
    return _receivings.resetProvisionalBooking(
      divisionId: divisionId,
      costCenterId: costCenterId,
      id: id,
    );
  }

  @override
  Future<Result<Response, AppError>> getReceivingFreightCostsOrderTotals({
    required String divisionId,
    required String costCenterId,
    required String orderId,
  }) {
    return _receivings.getFreightCostsOrderTotals(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
    );
  }

  @override
  Future<Result<Response, AppError>> updateReceivingFreightCosts({
    required String divisionId,
    required String orderId,
    required Map<String, dynamic> fields,
  }) {
    return _receivings.updateFreightCosts(
      divisionId: divisionId,
      orderId: orderId,
      fields: fields,
    );
  }

  @override
  Future<Result<Response, AppError>> receivingsSupplierLookup({
    required String divisionId,
    required String costCenterId,
    required String query,
  }) =>
      _receivings.supplierLookup(
        divisionId: divisionId,
        costCenterId: costCenterId,
        query: query,
      );

  @override
  Future<Result<Response, AppError>>
      receivingsFreightCostsCreditorSupplierLookup({
    required String divisionId,
    required String costCenterId,
    required String query,
  }) =>
          _receivings.freightCostsCreditorSupplierLookup(
            divisionId: divisionId,
            costCenterId: costCenterId,
            query: query,
          );

  @override
  Future<Result<Response, AppError>>
      getReceivingFreightCostsBreakdownByCostType({
    required String divisionId,
    required String costCenterId,
    required String orderId,
  }) {
    return _receivings.getFreightCostsBreakdownByCostType(
      divisionId: divisionId,
      costCenterId: costCenterId,
      orderId: orderId,
    );
  }

  // --- Delivery notes ---

  @override
  Future<Result<Response, AppError>> searchDeliveryNotes({
    required String divisionId,
    required String? query,
    required String? supplierName,
    required String? fromDate,
    required String? toDate,
    required String? bookingId,
    required String? deliveryNoteId,
    required double? deliveryNoteAmount,
    required String? productName,
    required String? receivingConfirmedBy,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
    required String language,
  }) {
    return _deliveryNotes.search(
      divisionId: divisionId,
      query: query,
      supplierName: supplierName,
      fromDate: fromDate,
      toDate: toDate,
      bookingId: bookingId,
      deliveryNoteId: deliveryNoteId,
      deliveryNoteAmount: deliveryNoteAmount,
      productName: productName,
      receivingConfirmedBy: receivingConfirmedBy,
      orderByField: orderByField,
      orderType: orderType,
      page: page,
      pageSize: pageSize,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> getOneDeliveryNoteById({
    required String divisionId,
    required String id,
    required String language,
  }) {
    return _deliveryNotes.getOneById(
      divisionId: divisionId,
      id: id,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> getDeliveryNoteBookingVoucherUrl({
    required String divisionId,
    required String bookingId,
    required String language,
  }) {
    return _deliveryNotes.getBookingVoucherUrl(
      divisionId: divisionId,
      bookingId: bookingId,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> toggleDeliveryNoteAvailability({
    required String divisionId,
    required String costCenterId,
    required String id,
    required bool enabled,
  }) {
    return _deliveryNotes.toggle(
      divisionId: divisionId,
      costCenterId: costCenterId,
      id: id,
      enabled: enabled,
    );
  }

  // --- Order lists ---

  @override
  Future<Result<Response, AppError>> searchOrderLists({
    required String divisionId,
    required String costCenterId,
    required String query,
    required String language,
    String? filterByCostCenter,
    required String filter,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
    bool includeProducts = false,
  }) {
    return _orderLists.search(
      divisionId: divisionId,
      costCenterId: costCenterId,
      language: language,
      query: query,
      filterByCostCenter: filterByCostCenter,
      filter: filter,
      orderByField: orderByField,
      orderType: orderType,
      page: page,
      pageSize: pageSize,
      includeProducts: includeProducts,
    );
  }

  @override
  Future<Result<Response, AppError>> lookupEditableOrderList({
    required String divisionId,
    required String costCenterId,
    required int positionId,
    required String itemType,
    required String query,
    required int page,
    required int pageSize,
  }) {
    return _orderLists.lookupEditable(
      divisionId: divisionId,
      costCenterId: costCenterId,
      positionId: positionId,
      itemType: itemType,
      query: query,
      page: page,
      pageSize: pageSize,
    );
  }

  @override
  Future<Result<Response, AppError>> getOneOrderListById({
    required String divisionId,
    required String costCenterId,
    required int listId,
    required String language,
    bool includeProducts = false,
  }) {
    return _orderLists.getOneById(
      divisionId: divisionId,
      costCenterId: costCenterId,
      listId: listId,
      language: language,
      includeProducts: includeProducts,
    );
  }

  @override
  Future<Result<Response, AppError>> searchOrderListProducts({
    required String divisionId,
    required String costCenterId,
    required String query,
    required int listId,
    required String language,
    required String filter,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
  }) {
    return _orderLists.searchProducts(
      divisionId: divisionId,
      costCenterId: costCenterId,
      query: query,
      listId: listId,
      language: language,
      filter: filter,
      orderByField: orderByField,
      orderType: orderType,
      page: page,
      pageSize: pageSize,
    );
  }

  @override
  Future<Result<Response, AppError>> deleteOrderListProduct({
    required String divisionId,
    required String costCenterId,
    required int listId,
    required int positionId,
    required String itemType,
  }) {
    return _orderLists.deleteProduct(
      divisionId: divisionId,
      costCenterId: costCenterId,
      listId: listId,
      positionId: positionId,
      itemType: itemType,
    );
  }

  @override
  Future<Result<Response, AppError>> copyOrderListProduct({
    required String divisionId,
    required String costCenterId,
    required int sourceListId,
    required int targetListId,
    required int positionId,
    required String itemType,
  }) {
    return _orderLists.copyProduct(
      divisionId: divisionId,
      costCenterId: costCenterId,
      sourceListId: sourceListId,
      targetListId: targetListId,
      positionId: positionId,
      itemType: itemType,
    );
  }

  @override
  Future<Result<Response, AppError>> moveOrderListProduct({
    required String divisionId,
    required String costCenterId,
    required int sourceListId,
    required int targetListId,
    required int positionId,
    required String itemType,
  }) {
    return _orderLists.moveProduct(
      divisionId: divisionId,
      costCenterId: costCenterId,
      sourceListId: sourceListId,
      targetListId: targetListId,
      positionId: positionId,
      itemType: itemType,
    );
  }

  @override
  Future<Result<Response, AppError>> addProductToOrderList({
    required String divisionId,
    required String costCenterId,
    required int listId,
    required int positionId,
    required String itemType,
  }) {
    return _orderLists.addProduct(
      divisionId: divisionId,
      costCenterId: costCenterId,
      listId: listId,
      positionId: positionId,
      itemType: itemType,
    );
  }

  @override
  Future<Result<Response, AppError>> replaceProductInOrderList({
    required String divisionId,
    required String costCenterId,
    required int listId,
    required int positionId,
    required String itemType,
    required int replacementPositionId,
    required String replacementItemType,
  }) async {
    return _orderLists.replaceProduct(
      divisionId: divisionId,
      costCenterId: costCenterId,
      listId: listId,
      positionId: positionId,
      itemType: itemType,
      replacementPositionId: replacementPositionId,
      replacementItemType: replacementItemType,
    );
  }

  @override
  Future<Result<Response, AppError>> updateOrderListProductsOrder({
    required String divisionId,
    required String costCenterId,
    required int listId,
    required List<int> order,
  }) {
    return _orderLists.updateProductsOrder(
      divisionId: divisionId,
      costCenterId: costCenterId,
      listId: listId,
      order: order,
    );
  }

  @override
  Future<Result<Response, AppError>> updateOrderListCountOfViews({
    required String divisionId,
    required String costCenterId,
    required int listId,
  }) {
    return _orderLists.updateCountOfViews(
      divisionId: divisionId,
      costCenterId: costCenterId,
      listId: listId,
    );
  }

  @override
  Future<Result<Response, AppError>> renameOrderList({
    required String divisionId,
    required String costCenterId,
    required int listId,
    required String newName,
  }) {
    return _orderLists.rename(
      divisionId: divisionId,
      costCenterId: costCenterId,
      listId: listId,
      newName: newName,
    );
  }

  @override
  Future<Result<Response, AppError>> createOrderList({
    required String divisionId,
    required String costCenterId,
    required String name,
  }) {
    return _orderLists.create(
      divisionId: divisionId,
      costCenterId: costCenterId,
      name: name,
    );
  }

  @override
  Future<Result<Response, AppError>> updateOrderListReadNote({
    required String divisionId,
    required String costCenterId,
    required int listId,
    required String? readNote,
  }) {
    return _orderLists.updateReadNote(
      divisionId: divisionId,
      costCenterId: costCenterId,
      listId: listId,
      readNote: readNote,
    );
  }

  @override
  Future<Result<Response, AppError>> updateOrderListProductComment({
    required String divisionId,
    required String costCenterId,
    required int listId,
    required int listItemId,
    required String? comment,
  }) {
    return _orderLists.updateProductComment(
      divisionId: divisionId,
      costCenterId: costCenterId,
      listId: listId,
      listItemId: listItemId,
      comment: comment,
    );
  }

  @override
  Future<Result<Response, AppError>> downloadOrderList({
    required String divisionId,
    required String costCenterId,
    required int listId,
  }) =>
      _orderLists.download(
        divisionId: divisionId,
        costCenterId: costCenterId,
        listId: listId,
      );

  // -- Auth --

  @override
  Future<Result<Response, AppError>> refreshJWTToken({
    required String refreshToken,
  }) {
    return _auth.refreshJWTToken(refreshToken);
  }

  @override
  Future<Result<Response, AppError>> getSsoIdentityProvidersList() =>
      _auth.getSsoIdentityProviders();

  // -- Reports --

  @override
  Future<Result<Response, AppError>> loadReport({
    required String divisionId,
    required String costCenterId,
    required String idOrAlias,
    int urlTTL = 360,
    Map<String, dynamic> parameters = const {},
  }) {
    return _reports.load(
      divisionId: divisionId,
      costCenterId: costCenterId,
      idOrAlias: idOrAlias,
      urlTTL: urlTTL,
      parameters: parameters,
    );
  }

  // -- Dashboard --

  @override
  Future<Result<Response, AppError>> getDashboardItems({
    required String divisionId,
    required String costCenterId,
  }) {
    return _dashboard.getItems(
      divisionId: divisionId,
      costCenterId: costCenterId,
    );
  }

  // -- CostCenter --

  @override
  Future<Result<Response, AppError>> searchForWebshopCostCenter({
    required String query,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
    String? filterByDivisionId,
  }) {
    return _costCenter.searchForWebshopCostCenter(
      query: query,
      orderByField: orderByField,
      orderType: orderType,
      page: page,
      pageSize: pageSize,
      filterByDivisionId: filterByDivisionId,
    );
  }

  @override
  Future<Result<Response, AppError>> getWebshopCostCenterById({
    required String divisionId,
    required String costCenterId,
  }) {
    return _costCenter.getWebshopCostCenterById(
      divisionId: divisionId,
      costCenterId: costCenterId,
    );
  }

  // -- Ims.Medius --

  @override
  Future<Result<Response, AppError>> resetMediusExport({
    required String divisionId,
    required List<String> bookingIds,
  }) =>
      _imsMedius.resetExport(
        divisionId: divisionId,
        bookingIds: bookingIds,
      );

  // -- Ims.Config --

  @override
  Future<Result<Response, AppError>> getDivisionImsConfig(String divisionId) {
    return _imsConfig.getDivisionConfig(divisionId);
  }

  // -- Ims.Product --
  @override
  Future<Result<Response, AppError>> getImsProductById({
    required String divisionId,
    required String costCenterId,
    required String stockItemId,
  }) =>
      _imsProduct.getOneById(
        divisionId: divisionId,
        costCenterId: costCenterId,
        stockItemId: stockItemId,
      );

  @override
  Future<Result<Response, AppError>> updateImsProduct({
    required String divisionId,
    required String costCenterId,
    required String stockItemId,
    required Map<String, dynamic> fields,
  }) {
    return _imsProduct.updateProduct(
      divisionId: divisionId,
      costCenterId: costCenterId,
      stockItemId: stockItemId,
      fields: fields,
    );
  }

  @override
  Future<Result<List<Result<Response, AppError>>, AppError>> updateImsProducts({
    required String divisionId,
    required String costCenterId,
    required List<Map<String, dynamic>> productsData,
  }) {
    return _imsProduct.updateProducts(
      divisionId: divisionId,
      costCenterId: costCenterId,
      productsData: productsData,
    );
  }

  @override
  Future<Result<Response, AppError>> searchImsMappingProducts({
    required String divisionId,
    required String costCenterId,
    required String query,
    required String language,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
  }) {
    return _imsProduct.searchMappingProducts(
      divisionId: divisionId,
      costCenterId: costCenterId,
      query: query,
      language: language,
      orderByField: orderByField,
      orderType: orderType,
      page: page,
      pageSize: pageSize,
    );
  }

  @override
  Future<Result<Response, AppError>> updateImsMappingProduct({
    required String divisionId,
    required String costCenterId,
    required String stockItemId,
    required Map<String, dynamic> fields,
  }) {
    return _imsProduct.updateMappingProduct(
      divisionId: divisionId,
      costCenterId: costCenterId,
      stockItemId: stockItemId,
      fields: fields,
    );
  }

  // -- Ims.Supplier --

  @override
  Future<Result<Response, AppError>> searchSendOrderAfterApprovalCostCenter({
    required String divisionId,
    required String supplierId,
    required String query,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
  }) {
    return _imsSupplier.searchCostCenter(
      divisionId: divisionId,
      supplierId: supplierId,
      query: query,
      orderByField: orderByField,
      orderType: orderType,
      page: page,
      pageSize: pageSize,
    );
  }

  @override
  Future<Result<Response, AppError>> toggleSendOrderAfterApprovalCostCenter({
    required String divisionId,
    required String costCenterKey,
    required String costCenterId,
    required String supplierId,
    required bool enabled,
  }) {
    return _imsSupplier.toggle(
      divisionId: divisionId,
      costCenterKey: costCenterKey,
      costCenterId: costCenterId,
      supplierId: supplierId,
      enabled: enabled,
    );
  }

  @override
  Future<Result<Response, AppError>>
      toggleAllSendOrderAfterApprovalCostCenters({
    required String divisionId,
    required String supplierId,
    required bool enabled,
  }) {
    return _imsSupplier.toggleAllCostCenters(
      divisionId: divisionId,
      supplierId: supplierId,
      enabled: enabled,
    );
  }

  @override
  Future<Result<Response, AppError>> imsSupplierUpdate({
    required String divisionId,
    required String supplierId,
    required Map<String, dynamic> fields,
  }) {
    return _imsSupplier.update(
      divisionId: divisionId,
      supplierId: supplierId,
      fields: fields,
    );
  }

  // --- Ims.Stock ---

  @override
  Future<Result<Response, AppError>> searchImsStores({
    required String divisionId,
    required String costCenterId,
    required String query,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
  }) {
    return _imsStore.search(
      divisionId: divisionId,
      costCenterId: costCenterId,
      query: query,
      orderByField: orderByField,
      orderType: orderType,
      page: page,
      pageSize: pageSize,
    );
  }

  @override
  Future<Result<Response, AppError>> getOneImsStoreById({
    required String orgUnitKey,
    required String costCenterId,
    required String stockId,
  }) {
    return _imsStore.getOneById(
      orgUnitKey: orgUnitKey,
      costCenterId: costCenterId,
      stockId: stockId,
    );
  }

  @override
  Future<Result<Response, AppError>> updateImsStore({
    required String orgUnitKey,
    required String costCenterId,
    required String stockId,
    required Map<String, dynamic> fields,
    required String language,
  }) {
    return _imsStore.updateStore(
      orgUnitKey: orgUnitKey,
      costCenterId: costCenterId,
      stockId: stockId,
      fields: fields,
      language: language,
    );
  }

  // --- Ims.Stock.Item ---

  @override
  Future<Result<Response, AppError>> searchImsStoreItems({
    required String orgUnitKey,
    required String costCenterId,
    required String stockId,
    required String orderByField,
    required String orderType,
    required String query,
    required int page,
    required int pageSize,
    required String language,
  }) {
    return _imsStore.searchStoreItems(
      orgUnitKey: orgUnitKey,
      costCenterId: costCenterId,
      stockId: stockId,
      orderByField: orderByField,
      orderType: orderType,
      query: query,
      page: page,
      pageSize: pageSize,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> deleteImsStoreItem({
    required String orgUnitKey,
    required String costCenterId,
    required String stockId,
    required String stockItemId,
    required String language,
  }) {
    return _imsStore.deleteStoreItem(
      orgUnitKey: orgUnitKey,
      costCenterId: costCenterId,
      stockId: stockId,
      stockItemId: stockItemId,
      language: language,
    );
  }

  // --- Ims.Stock.ExchangeRate ---

  @override
  Future<Result<Response, AppError>> getImsExchangeRate({
    required String orgUnitKey,
    required String currencyCode,
  }) {
    return _imsExchangeRate.getOneById(
      orgUnitKey: orgUnitKey,
      currencyCode: currencyCode,
    );
  }

  // -- Reports.PowerBI --

  @override
  Future<Result<Response, AppError>> getPowerBIReports({
    required String divisionId,
    required String costCenterId,
    List<String>? categories,
    required String language,
  }) {
    return _reportsPowerBI.getReports(
      divisionId: divisionId,
      costCenterId: costCenterId,
      categories: categories,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> getPowerBIEmbedUrl({
    required String divisionId,
    required String costCenterId,
    required String reportId,
    required String language,
  }) {
    return _reportsPowerBI.getEmbedUrl(
      divisionId: divisionId,
      costCenterId: costCenterId,
      reportId: reportId,
      language: language,
    );
  }

  @override
  Future<Result<Response, AppError>> getPowerBICategories({
    required String divisionId,
    required String costCenterId,
    required String language,
  }) {
    return _reportsPowerBI.getCategories(
      divisionId: divisionId,
      costCenterId: costCenterId,
      language: language,
    );
  }

  // -- Recipe --

  @override
  Future<Result<Response, AppError>> searchRecipes({
    required String divisionId,
    required String costCenterId,
    String query = '',
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
    required String language,
    String? categoryId,
    bool showOnlyFavorites = false,
    required List<String> includes,
  }) =>
      _recipe.searchRecipes(
        divisionId: divisionId,
        costCenterId: costCenterId,
        query: query,
        orderByField: orderByField,
        orderType: orderType,
        language: language,
        page: page,
        pageSize: pageSize,
        categoryId: categoryId,
        includes: includes,
        showOnlyFavorites: showOnlyFavorites,
      );

  @override
  Future<Result<Response, AppError>> getOneRecipeById({
    required String divisionId,
    required String costCenterId,
    required String recipeId,
    required String language,
    required List<String> includes,
  }) =>
      _recipe.getOneRecipeById(
        divisionId: divisionId,
        costCenterId: costCenterId,
        recipeId: recipeId,
        language: language,
        includes: includes,
      );

  @override
  Future<Result<Response, AppError>> addRecipeToCart({
    required String divisionId,
    required String costCenterId,
    required String recipeId,
    required double quantity,
  }) =>
      _recipe.addToCart(
        divisionId: divisionId,
        costCenterId: costCenterId,
        recipeId: recipeId,
        quantity: quantity,
      );

  @override
  Future<Result<Response, AppError>> getRecipeCalculation({
    required String divisionId,
    required String costCenterId,
    required String recipeId,
    required String language,
    required List<String> includes,
  }) =>
      _recipe.getCalculation(
        divisionId: divisionId,
        costCenterId: costCenterId,
        recipeId: recipeId,
        language: language,
        includes: includes,
      );

  @override
  Future<Result<Response, AppError>> searchRecipeCategories({
    required String divisionId,
    required String costCenterId,
    String query = '',
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
    required String language,
  }) =>
      _recipe.searchCategories(
        divisionId: divisionId,
        costCenterId: costCenterId,
        query: query,
        orderByField: orderByField,
        orderType: orderType,
        language: language,
        page: page,
        pageSize: pageSize,
      );

  @override
  Future<Result<Response, AppError>> getAllRecipeIngredients({
    required String divisionId,
    required String costCenterId,
    required String recipeId,
    required String language,
  }) =>
      _recipe.getAllIngredients(
        divisionId: divisionId,
        costCenterId: costCenterId,
        language: language,
        recipeId: recipeId,
      );

  @override
  Future<Result<Response, AppError>> getAllRecipePreparationSteps({
    required String divisionId,
    required String costCenterId,
    required String recipeId,
    required String language,
  }) =>
      _recipe.getAllPreparationSteps(
        divisionId: divisionId,
        costCenterId: costCenterId,
        language: language,
        recipeId: recipeId,
      );

  @override
  Future<Result<Response, AppError>> getAllRecipeNutrients({
    required String divisionId,
    required String costCenterId,
    required String recipeId,
    required String language,
  }) =>
      _recipe.getAllNutrients(
        divisionId: divisionId,
        costCenterId: costCenterId,
        language: language,
        recipeId: recipeId,
      );

  @override
  Future<Result<Response, AppError>> getRecipeShareDivisions({
    required String divisionId,
    required String costCenterId,
    required String recipeId,
    required String language,
  }) =>
      _recipe.getShareDivisions(
        divisionId: divisionId,
        costCenterId: costCenterId,
        recipeId: recipeId,
        language: language,
      );

  @override
  Future<Result<Response, AppError>> updateRecipeShareStatus({
    required String divisionId,
    required String shareWithDivisionId,
    required String costCenterId,
    required String recipeId,
    required bool shared,
  }) =>
      _recipe.updateShareStatus(
        divisionId: divisionId,
        costCenterId: costCenterId,
        shareWithDivisionId: shareWithDivisionId,
        recipeId: recipeId,
        shared: shared,
      );

  @override
  Future<Result<Response, AppError>> addRecipeNote({
    required String divisionId,
    required String costCenterId,
    required String content,
    required String recipeId,
  }) =>
      _recipe.addNote(
        divisionId: divisionId,
        costCenterId: costCenterId,
        content: content,
        recipeId: recipeId,
      );

  @override
  Future<Result<Response, AppError>> deleteRecipeNote({
    required String divisionId,
    required String costCenterId,
    required int recipeNoteId,
  }) =>
      _recipe.deleteNote(
        divisionId: divisionId,
        costCenterId: costCenterId,
        recipeNoteId: recipeNoteId,
      );

  @override
  Future<Result<Response, AppError>> updateRecipeNote({
    required String divisionId,
    required String costCenterId,
    required int recipeNoteId,
    required String content,
  }) =>
      _recipe.updateNote(
        divisionId: divisionId,
        costCenterId: costCenterId,
        recipeNoteId: recipeNoteId,
        content: content,
      );

  @override
  Future<Result<Response, AppError>> searchRecipeNotes({
    required String divisionId,
    required String costCenterId,
    required String recipeId,
    String? addedByUserId,
    String? updatedByUserId,
    String query = '',
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
    required String language,
  }) =>
      _recipe.searchNotes(
        divisionId: divisionId,
        costCenterId: costCenterId,
        recipeId: recipeId,
        orderByField: orderByField,
        orderType: orderType,
        language: language,
        addedByUserId: addedByUserId,
        updatedByUserId: updatedByUserId,
        query: query,
        page: page,
        pageSize: pageSize,
      );

  @override
  Future<Result<Response, AppError>> getOneRecipeNoteById({
    required String divisionId,
    required String costCenterId,
    required int recipeNoteId,
  }) =>
      _recipe.getOneNoteById(
        divisionId: divisionId,
        costCenterId: costCenterId,
        recipeNoteId: recipeNoteId,
      );

  @override
  Future<Result<Response, AppError>> favoritizeRecipe({
    required String divisionId,
    required String costCenterId,
    required String recipeId,
    required bool isFavorite,
  }) =>
      _recipe.favoritize(
        divisionId: divisionId,
        costCenterId: costCenterId,
        recipeId: recipeId,
        isFavorite: isFavorite,
      );

  // -- Files --

  @override
  Future<Result<Uint8List, AppError>> loadFile({
    required Uri url,
    Duration timeout = const Duration(seconds: 30),
  }) {
    return apiTransport.loadFile(
      url: url,
      timeout: timeout,
    );
  }

  @override
  Future<Result<bool, AppError>> uploadFile({
    required String divisionId,
    required String type,
    required String filePath,
    bool isPublic = false,
    String? overrideFileName,
    required Map<String, String> additionalParams,
  }) {
    return apiTransport.uploadFile(
      divisionId: divisionId,
      type: type,
      filePath: filePath,
      additionalParams: additionalParams,
      isPublic: isPublic,
      overrideFileName: overrideFileName,
    );
  }

  @override
  Future<Result<bool, AppError>> renameFile({
    required String divisionId,
    required String type,
    required String fileId,
    required String newName,
    required Map<String, String> additionalParams,
  }) {
    return apiTransport.renameFile(
      divisionId: divisionId,
      type: type,
      fileId: fileId,
      newName: newName,
      additionalParams: additionalParams,
    );
  }

  @override
  Future<Result<bool, AppError>> deleteFile({
    required String divisionId,
    required String type,
    required String fileId,
    required Map<String, String> additionalParams,
  }) {
    return apiTransport.deleteFile(
      divisionId: divisionId,
      type: type,
      fileId: fileId,
      additionalParams: additionalParams,
    );
  }

  // -- Scan to order --

  @override
  Future<Result<Response, AppError>> createScanToOrderFromRecipe({
    required String divisionId,
    required String costCenterId,
    required String recipeId,
    required double quantity,
    required String language,
  }) =>
      _scanToOrder.createFromRecipe(
        divisionId: divisionId,
        costCenterId: costCenterId,
        recipeId: recipeId,
        quantity: quantity,
        language: language,
      );

  @override
  Future<Result<Response, AppError>> getOneScanToOrderById({
    required String divisionId,
    required String costCenterId,
    required String scanTaskId,
    required String language,
    required List<String> includes,
  }) =>
      _scanToOrder.getOneById(
        divisionId: divisionId,
        costCenterId: costCenterId,
        scanTaskId: scanTaskId,
        language: language,
        includes: includes,
      );

  @override
  Future<Result<Response, AppError>> getScanToOrderItemAvailableSuppliers({
    required String divisionId,
    required String costCenterId,
    required String scanTaskId,
    required String scanItemId,
    required String language,
  }) =>
      _scanToOrder.getItemAvailableSuppliers(
        divisionId: divisionId,
        costCenterId: costCenterId,
        scanTaskId: scanTaskId,
        scanItemId: scanItemId,
        language: language,
      );

  @override
  Future<Result<Response, AppError>> updateScanToOrderItemSupplier({
    required String divisionId,
    required String costCenterId,
    required String scanTaskId,
    required String scanItemId,
    required Map<String, dynamic> fields,
  }) =>
      _scanToOrder.updateItemSupplier(
        divisionId: divisionId,
        costCenterId: costCenterId,
        scanTaskId: scanTaskId,
        scanItemId: scanItemId,
        fields: fields,
      );

  @override
  Future<Result<Response, AppError>> moveScanToOrderToCart({
    required String divisionId,
    required String costCenterId,
    required String scanTaskId,
  }) =>
      _scanToOrder.moveToCart(
        divisionId: divisionId,
        costCenterId: costCenterId,
        scanTaskId: scanTaskId,
      );
      
  @override
  Future<Result<Response, AppError>> loadTestTaskEntities({
    required int page,
    required int pageSize,
    required String orderType,
    required String orderByField,
    required String query,
    required List<String> includes,
    required String costCenterKey,
    required String orgUnitKey,
  }) {
    // TODO: implement loadTestTaskEntities for web
    throw UnimplementedError();
  }
}
