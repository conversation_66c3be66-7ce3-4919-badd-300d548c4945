import 'package:tr_labels_annotation/tr_labels_annotation.dart';

part 'labels_config.g.dart';

@TrLabels()
class LabelsConfig {
  static String placeholderOf(String label) {
    return _labels[label] ?? label;
  }

  static List<Map<String, String>> toJson() {
    return _labels.entries
        .map<Map<String, String>>((e) => {'id': e.key, 'placeholder': e.value})
        .toList();
  }

  @LabelsMap()
  static const Map<String, String> _labels = {
    // Common labels/words
    'supplier_label': 'Supplier',
    'price': 'Unit price',
    'sum': 'Sum',
    'monday': 'Monday',
    'tuesday': 'Tuesday',
    'wednesday': 'Wednesday',
    'thursday': 'Thursday',
    'friday': 'Friday',
    'saturday': 'Saturday',
    'sunday': 'Sunday',
    'costcenter': 'Cost center',
    'no_translation': 'No translation',
    'quantity': 'Quantity',
    'no_access': 'No access',
    'january': 'January',
    'february': 'February',
    'march': 'March',
    'april': 'April',
    'may': 'May',
    'june': 'June',
    'july': 'July',
    'august': 'August',
    'september': 'September',
    'october': 'October',
    'november': 'November',
    'december': 'December',

    'common.dispatching_division_label': 'Dispatching division',
    'common.dispatching_cost_center_label': 'Dispatching cost center',
    'common.dispatching_store_label': 'Dispatching store',
    'common.outgoing_cost_type_label': 'Outgoing cost type',
    'common.receiving_division_label': 'Receiving division',
    'common.receiving_cost_center_label': 'Receiving cost center',
    'common.receiving_store_label': 'Receiving store',
    'common.incoming_cost_type_label': 'Incoming cost type',

    // Init
    'init.restart_without_data_saving': 'Restart without data saving',

    // Sync
    'sync.rebuild_catalog': 'Rebuilding catalog',
    'sync.division': 'Loading division info',
    'sync.announcements': 'Checking announcements',
    'sync.cost_center': 'Loading cost center info',
    'sync.theme': 'Loading theme',
    'sync.save_order_lists': 'Loading order lists',
    'sync.save_inhouse_lists': 'Loading inhouse lists',
    'sync.order_lists_loading_progress': 'Loaded {{count}} order lists',
    'sync.inhouse_lists_loading_progress': 'Loaded {{count}} inhouse lists',
    'sync.try_again_button_label': 'TRY AGAIN',
    'sync.continue_button_label': 'CONTINUE',
    'sync.select_another_cost_center_button_label': 'SELECT COST CENTER',
    'sync.clear_cache_button_label': 'Clear app cache and try again',

    //Forms
    'form.field.validation_error.empty': 'Could not be empty',
    'form.field.validation_error.non_numeric': 'Should be numeric',
    'form.field.validation_error.numeric_too_big':
        'Should be less than or equal to {{maxValue}}',
    'form.field.validation_error.numeric_too_small':
        'Should be greater than or equal to {{minValue}}',
    'form.field.validation_error.non_whole_number': 'Should be a whole number',
    'form.field.validation_error.negative_number': 'Could not be negative',
    'form.field.validation_error.too_many_decimal_places':
        'Too many decimal places',

    // Settings
    'settings.account.label': 'Account details',
    'settings.account.unit': 'Betrieb',
    'settings.account.cost_center': 'Cost center',
    'settings.account.device_time': 'Device time',
    'settings.server_time': 'Server time',
    'settings.main_division_time': 'Main division time',

    'settings.application_information.label': 'Application information',
    'settings.application_information.database_version':
        'Database Schema Version',
    'settings.application_information.version_label': 'Version',
    'settings.application_information.version.up_to_date': 'Up to date',
    'settings.application_information.version.update_asap': 'Update asap',
    'settings.application_information.version.outdated': 'Outdated',

    'settings.language': 'Language',
    'settings.apply_button_label': 'Apply',

    'settings.system.label': 'System settings',
    'settings.system.bluetooth_devices.label': 'Bluetooth devices',
    'settings.system.screen_layout.label': 'Screen layout',
    'settings.system.screen_layout.automatic': 'Automatic',
    'settings.system.screen_layout.tablet': 'Tablet',
    'settings.system.screen_layout.mobile': 'Mobile',
    'settings.system.confirmation_dialogs': 'Confirmation dialogs',
    'settings.system.stt': 'Speech recognition',
    'settings.system.update_on_sync':
        'Automatically update local data on startup',
    'settings.system.auto_send_order_after_approval':
        'Automatically send order to supplier after approval',
    'settings.system.licenses.label': 'Licenses',
    'settings.system.licenses.open_licenses_screen_label':
        'Open licenses screen',

    'settings.reset_theme_to_default_label': 'Reset to default theme',
    'settings.delete_local_data_label': 'Delete local data',
    'history.empty': 'There is no items in history',
    'history.product_restore_alert.content': 'Restore item from the list?',
    'history.product_restore_alert.no_action': 'CANCEL',
    'history.product_restore_alert.yes_action': 'YES',
    'history.sl_item_deletion_label': 'Shopping list item deletion',
    'history.cart_item_deletion_label': 'Cart item deletion',
    'history.deleted_at': 'Deleted at :',
    'history.item_restored_successfully_label': 'Item was restored',
    'history.restore_button_label': 'RESTORE',

    // Notification labels
    'notifications.empty': 'There is no notifications',
    'notifications.content': 'Content',
    'notifications.clear_all': 'Clear all',
    'notifications.delete_all_alert.content': 'Delete item from the list?',
    'notifications.delete_all_alert.cancel_action': 'NO',
    'notifications.delete_all_alert.yes_action': 'YES',

    // Dialog labels
    'dialog.signout_confirmation': 'Do you really want to log out?',
    'dialog.logout_yes': 'Logout',
    'dialog.logout_no': 'Cancel',
    'dialog.discount.label': 'Discount',
    'dialog.discount.percentage': 'Percent, %',
    'dialog.discount.flat_amount': 'Flat Discount',

    // Common actions labels
    'action.delete': 'Delete',
    'action.retry': 'Retry',
    'action.cancel': 'Cancel',
    'action.confirm': 'Confirm',
    'action.ok': 'Ok',
    'action.yes': 'Yes',
    'action.save': 'Save',
    'action.select': 'Select',
    'action.update': 'Update',
    'action.add': 'Add',
    'action.reset': 'Reset',
    'action.test_button_label': 'Test button',

    // Common operation results
    'operation.success': 'Success',

    // Common button labels
    'button.edit': 'EDIT',

    //Common product labels
    'product.core_tag': 'CORE',
    'product.current_offer_tag': 'CURRENT OFFER',
    'product.price_per_content_unit': 'Price/Content Unit',
    'product.price_per_order_unit': 'Price/Order Unit',
    'product.quantity': 'Quantity',
    'product.total': 'Total',
    'product.change_offer_action': 'Change offer',

    // Languages names
    'language.en_gb': 'English',
    'language.es_es': 'Spanish',
    'language.de_de': 'German',
    'language.fr_fr': 'French',
    'language.it_it': 'Italian',
    'language.th_th': 'Thai',
    'language.tr_tr': 'Turkish',
    'language.hu_hu': 'Hungarian',
    'language.vi_vn': 'Vietnamese',
    'language.pt_pt': 'Portuguese',
    'language.nl_nl': 'Dutch',
    'language.ja_jp': 'Japanese',
    'language.zh_cn': 'Simplified Chinese',
    'language.zh_hk': 'Chinese (Cantonese)',
    'language.en_gb_en_name': 'English',
    'language.es_es_en_name': 'Spanish',
    'language.de_de_en_name': 'German',
    'language.fr_fr_en_name': 'French',
    'language.it_it_en_name': 'Italian',
    'language.th_th_en_name': 'Thai',
    'language.tr_tr_en_name': 'Turkish',
    'language.hu_hu_en_name': 'Hungarian',
    'language.vi_vn_en_name': 'Vietnamese',
    'language.pt_pt_en_name': 'Portuguese',
    'language.nl_nl_en_name': 'Dutch',
    'language.ja_jp_en_name': 'Japanese',
    'language.zh_cn_en_name': 'Simplified Chinese',
    'language.zh_hk_en_name': 'Chinese (Cantonese)',

    // Common search labels
    'search.no_results': 'No results were found for the current filters',
    'search.no_connection': 'Unavailable while offline',

    // User authorization error
    'user.auth_error.dialog_title': 'Authorization error',
    'user.auth_error.suspended__named':
        'This account has been temporarily suspended. Please try again in {{minutes}} minute(s).',
    'user.auth_error.deactivate':
        'This account has been deactivated. Please contact support.',
    'user.auth_error.password_expired':
        'Your password has expired and must be changed in the next screen.',

    // System information messages
    'info.online_mode_only': 'This feature works only in online mode.',
    'info.online_edit_only': 'Changing this requires network connection.',
    'info.no_changes': 'No changes. Nothing to save.',
    'info.saved_successfully': 'Saved successfully',
    'info.no_network_connection':
        'Please, check your network connection and try again.',
    'info.user_have_no_cost_centers':
        'This user has not assigned cost centers. Please contact support',
    'info.pdf_document_not_loaded':
        'Document not found. Please check web application.',
    'info.image_not_loaded': 'Image not found',
    'info.delete_local_data_confirmation':
        'Are you sure to delete all local data?',
    'info.delete_local_data_confirmation_no': 'No',
    'info.delete_local_data_confirmation_yes': 'Yes',
    'info.reset_to_default_theme_confirmation':
        'The custom theme and logo will be changed to the default.',
    'info.theme_reset_confirmation_no': 'No',
    'info.theme_reset_confirmation_yes': 'Yes',
    'info.ean_scanner_error': 'Error occurred while scanning ean code.',
    'info.ean_scanner_permission_error':
        'Error occurred while scanning ean code, no permission given.',
    'info.you_have_no_access': 'You have no access to this functionality',
    'info.please_fill_all_fields': 'Please fill in all fields',

    //Announcements
    'announcements.tile': 'Announcements',
    'announcements.empty_label': 'No announcements found',
    'announcements.info_dialog.content':
        'Swipe card left to mark message as read',
    'announcements.info_dialog.title': 'Info',
    'announcements.continue_button_label': 'CONTINUE',
    'announcements.mark_as_read_successfully_label':
        'Announcement marked as read',

    //Quantity bottom sheets labels
    'quantity_bottom_sheet.quantity_error.zero_value_not_allowed':
        'Zero value not allowed',
    'quantity_bottom_sheet.quantity_error.fractions_number_not_allowed':
        'Please, round the result',
    'quantity_bottom_sheet.quantity_error.minimum_quantity':
        'Minimum allowed quantity: {{minimumAllowedQuantity}}',
    'quantity_bottom_sheet.quantity_error.maximum_allowed_quantity':
        'Maximum allowed quantity: {{maximumAllowedQuantity}}',
    'quantity_bottom_sheet.quantity_error.maximum_fraction_digits_exited':
        'Please, round the result',
    'quantity_bottom_sheet.quantity_error.negative_value_is_not_allowed':
        'Negative value is not allowed',
    'quantity_bottom_sheet.quantity_error.infinity':
        'The quantity cant be infinity',

    // Cart labels

    'cart.view_pdf_button_label': 'View PDF',
    'cart.add_free_text_order_button_label': 'Add Free Text order',
    'cart.auto_consolidate_button_label': 'Auto Consolidate',
    'cart.request_approval_button_label': 'REQUEST APPROVAL',
    'cart.continue_button_label': 'CONTINUE',
    'cart.empty_label': 'Your shopping cart is empty',
    'cart.check_connection_and_try_again_label':
        'Please check your network connection and try again',
    'cart.no_permission_to_create_order_message':
        'You have no permission to create orders',
    'cart.no_items_to_approve_with_budget':
        'Budget should be selected for cart items to be approved',
    'cart.no_items_to_send_with_budget':
        'Budget should be selected for cart orders to be sent',

    'local_cart.empty_label': 'Local cart is empty',

    'cart.products_tab_label': 'PRODUCTS',
    'cart.products.filter.status.label': 'Status',
    'cart.products.filter.status.all': 'All',
    'cart.products.filter.status.not_approved': 'Require approval',
    'cart.products.filter.status.approved': 'Ready to send',
    'cart.products.search_available_only_online':
        'Search available only online',
    'cart.products.cost_type_lookup_title': 'Cost types',
    'cart.order_value_label': 'ORDER VALUE',
    'cart.product.origin': 'Origin',
    'cart.product.supplier': 'Supplier',
    'cart.product.content_units_per_order_unit': 'Content units per order unit',
    'cart.product.details_button_label': 'DETAILS',
    'cart.product.price_per_unit': 'Price per unit',
    'cart.product.packing_unit': 'Packing unit',
    'cart.product.requested': 'Requested',
    'cart.product.quantity': 'Quantity',
    'cart.product.total': 'Total',
    'cart.product.approval_required': 'Approval required',
    'cart.product.yes_label': 'Yes',
    'cart.product.no_label': 'No',
    'cart.products_count_label__named': '{{count}} products',
    'cart.product.comment_label': 'Comment',
    'cart.product.comment_title': 'Comment',
    'cart.product.comment_update_alert.cancel_action': 'CANCEL',
    'cart.product.comment_update_alert.add_action': 'ADD',
    'cart.product.comment_update_alert.update_action': 'UPDATE',
    'cart.product.delete_alert.content': 'Are you sure to delete this product?',
    'cart.product.delete_alert.no_action': 'CANCEL',
    'cart.product.delete_alert.yes_action': 'YES',
    'cart.product.better_price_available': 'Better price available',
    'cart.product.comment_updated_successfully': 'Comment updated successfully',
    'cart.product.cost_type_updated_successfully':
        'Cost type updated successfully',
    'cart.product.flat_discount_updated_successfully':
        'Discount updated successfully. The discount will be calculated as a percentage (%).',
    'cart.product.percentage_discount_updated_successfully':
        'Discount updated successfully',
    'cart.product.deleted_successfully': 'Deleted successfully',
    'cart.product.qty_updated_successfully': 'Quantity updated successfully',
    'cart.product.edit_quantity_button_label': 'EDIT QUANTITY',
    'cart.product.comment_button_label': 'COMMENT',
    'cart.product.cost_type': 'Cost Type',
    'cart.product.please_select_cost_type': 'Please select the Cost Type',
    'cart.product.budget_will_not_be_calculated_for_cost_type':
        'Budget will not be calculated for this cost type',
    'cart.product.cost_type_has_no_budget':
        'Selected cost type has no corresponding budget associated with it',
    'cart.product.discount': 'Discount',
    'cart.product.already_approved': 'This product is already approved',

    'local_cart.product.total': 'Total',
    'local_cart.product.delete_alert.content':
        'Are you sure to delete this product?',
    'local_cart.product.delete_alert.no_action': 'CANCEL',
    'local_cart.product.delete_alert.yes_action': 'YES',
    'local_cart.product.details_button_label': 'DETAILS',
    'local_cart.product.supplier': 'Supplier',
    'local_cart.product.content_units_per_order_unit':
        'Content units per order unit',
    'local_cart.product.origin': 'Origin',
    'local_cart.product.quantity': 'Quantity',
    'local_cart.product.qty_updated_successfully':
        'Quantity updated successfully',
    'local_cart.product.deleted_successfully': 'Deleted successfully',
    'local_cart.uploaded_successfully': 'Uploaded successfully',
    'local_cart.uploaded_partially': 'Not all products were uploaded',
    'local_cart.uploaded_view_log_button_label': 'VIEW LOG',
    'local_cart.upload_button': 'UPLOAD',
    'local_cart.upload_alert.content':
        'Do you want to upload local cart products? This will overwrite quantity of the same products already in cart.',
    'local_cart.upload_alert.no_action': 'NO',
    'local_cart.upload_alert.yes_action': 'YES',
    'local_cart.processing_products': 'Processing products...',
    'local_cart.products_processed': 'Products processed:',

    'cart.uploading_result.status.ok': 'Success',
    'cart.uploading_result.status.product_not_found': 'Product not found',
    'cart.uploading_result.status.missing_customer_id': 'Missing customer ID',
    'cart.uploading_result.status.cart_item_can_not_be_modified':
        'Item cant be modified in cart',
    'cart.uploading_result.status.oci_can_not_add_product':
        'OCI can not add product',
    'cart.uploading_result.status.scan_item_can_not_be_added':
        'Scan item can not be added',
    'cart.uploading_result.status.unknown': 'UNKNOWN',
    'cart.uploading_result.product.label': 'Product',
    'cart.uploading_result.status.label': 'Adding status',
    'cart.uploading_result.add_successful_label': 'Success',
    'cart.uploading_result.failed_to_add_label': 'Failed',
    'cart.adding_results.title': 'Products adding log',

    'cart.order.order_value': 'Order value',
    'cart.order.order_until_label': 'Order until: {{time}}, today',
    'cart.order.delivery_date_override_label':
        'The date of delivery has been amended by the orderer and may not be matching the lead time of the supplier. Please contact the supplier to ensure that you will receive the items on the requested date of delivery',
    'cart.order.minimum_order_amount_label': 'Minimum order amount: {{amount}}',
    'cart.order.minimum_quantity_surcharge_label':
        'Minimum quantity surcharge: {{quantity}}',
    'cart.order.delivery_date': 'Date of delivery',
    'cart.order.request_authorization_button_label': 'REQUEST AUTHORIZATION',
    'cart.order.delivery_date_updated_successfully':
        'Delivery date updated successfully',
    'cart.order.discount_updated_successfully': 'Discount updated successfully',
    'cart.order.discount_update_title': "Discount for all Suppliers' articles",
    'cart.order.external_comment.label': 'External comment',
    'cart.order.external_comment.last_edited': 'Last edited',
    'cart.order.external_comment.edit': 'Edit comment',
    'cart.order.external_comment.add': 'Add external comment',
    'cart.order.external_comment.articles_count': '{{count}} articles',
    'cart.order.external_comment.lead_time_day': '+{{count}} day',
    'cart.order.external_comment.lead_time_days': '+{{count}} days',
    'cart.order.external_comment.view_history': 'View history',
    'cart.order.external_comment.delete': 'Delete',
    'cart.order.external_comment.helper_text':
        'External comments at the Supplier & Lead time level are visible throughout the entire process, from the Shopping Cart to approval, ordering, and receiving. They appear in emails, documents, and Purchase Order PDFs sent to suppliers.',
    'cart.order.external_comment_updated_successfully':
        'External comment updated successfully',

    'cart.order.external_comment.history.title': 'External comment history',
    'cart.order.external_comment.history.deleted': 'DELETED',

    'cart.order.attachments.title': 'Add attachments',
    'cart.order.attachments.too_many_files': 'You can attach up to 5 files',
    'cart.order.attachments.file_is_too_large':
        'Maximum file size allowed is 10 MB',
    'cart.order.attachments.no_attachments': 'No current attachments',
    'cart.order.attachments.take_a_photo': 'TAKE A PHOTO',
    'cart.order.attachments.attach_file': 'ATTACH FILE',
    'cart.order.attachments.rename': 'Rename',
    'cart.order.attachments.delete': 'Delete',
    'cart.order.attachments.ordering_title': 'Ordering',
    'cart.order.attachments.documents': '{{count}} documents',
    'cart.order.attachments.document': '{{count}} document',
    'cart.order.attachments.delete_confirmation_content':
        'Are you sure want to delete this attachment?',
    'cart.order.attachments.delete_confirmation_yes_label': 'YES',
    'cart.order.attachments.delete_confirmation_no_label': 'NO',
    'cart.order.attachments.rename_confirmation_label': 'SAVE',
    'cart.order.attachments.rename_cancel_label': 'CANCEL',
    'cart.order.attachments.rename_field_label': 'Document name',

    'cart.overview.articles_title': 'Articles',
    'cart.overview.suppliers_title': 'Suppliers',
    'cart.overview.notes_title': 'Notes',
    'cart.overview.offline_articles_title': 'Offline articles',
    'cart.overview.budget_title': 'Budget',
    'cart.overview.filter.status.label': 'Status',
    'cart.overview.filter.status.all': 'All',
    'cart.overview.filter.status.not_approved': 'Require approval',
    'cart.overview.filter.status.approved': 'Ready to send',
    'cart.overview.search_available_only_online':
        'Search available only online',
    'cart.orders_to_approve.title': 'Orders to approve',
    'cart.orders_to_approve.no_orders': 'You have no orders to approve',
    'cart.order_to_approve.value_to_approve': 'Order value to approve',
    'cart.order_to_approve.comment': 'Comment',
    'cart.order_to_approve.comment_label': 'Comment',
    'cart.order_to_approve.comment_title': 'Comment',
    'cart.order_to_approve.approver': 'Approver',
    'cart.order_to_approve.attachments_label': 'Attachments',
    'cart.order_to_approve.no_attachments': 'No documents attached',
    'cart.order_to_approve.attachment': '{{count}} document',
    'cart.order_to_approve.attachments': '{{count}} documents',
    'cart.order_to_approve.send_after_approval_warning':
        'The products from some suppliers in the list may be sent automatically after last approval',
    'cart.order_to_approve.comment_update_alert.cancel_action': 'CANCEL',
    'cart.order_to_approve.comment_update_alert.add_action': 'ADD',
    'cart.order_to_approve.comment_update_alert.update_action': 'UPDATE',
    'cart.order_to_approve.request_approval_alert.content':
        'Are you sure to request approval for this order?',
    'cart.order_to_approve.request_approval_alert.no_action': 'CANCEL',
    'cart.order_to_approve.request_approval_alert.yes_action': 'YES',
    'cart.order_to_approve.approval_requested_successfully':
        'Approval requested successfully',
    'cart.order_to_approve.approval_requested_successfully_with_upload_error':
        'Approval requested successfully, but some attached documents was not uploaded.',
    'cart.order_to_approve.approver_required': 'Please, select approver first',
    'cart.order_to_approve.budget_required': 'Budget is not set for this order',
    'cart.order_to_approve.budget_error': 'Budget Error',

    'cart.orders_to_send.title': 'Send orders',
    'cart.orders_to_send.no_orders': 'You have no orders to send',
    'cart.orders_to_send.see_orders_to_approve_button': 'See orders to approve',
    'cart.orders_to_send.send_button_label': 'SEND',
    'cart.orders_to_send.send_alert.content':
        'Are you sure to send selected orders?',
    'cart.orders_to_send.send_alert.no_action': 'CANCEL',
    'cart.orders_to_send.send_alert.yes_action': 'YES',
    'cart.orders_to_send.send_with_only_create_alert.content':
        'At least one of your orders is marked to not submit to the supplier. Are you sure to send selected orders?',
    'cart.orders_to_send.send_with_only_create_alert.no_action': 'CANCEL',
    'cart.orders_to_send.send_with_only_create_alert.yes_action': 'YES',
    'cart.order_to_send.order_value': 'Order value',
    'cart.order_to_send.discount_amount': 'Discount amount',
    'cart.order_to_send.last_order_time': 'Last order time',
    'cart.order_to_send.date_of_delivery': 'Date of delivery',
    'cart.order_to_send.delivery_instructions': 'Delivery instructions',
    'cart.order_to_send.delivery_instructions_label': 'Delivery instructions',
    'cart.order_to_send.delivery_instructions_title': 'Delivery instructions',
    'cart.order_to_send.delivery_instructions_update_alert.cancel_action':
        'CANCEL',
    'cart.order_to_send.comment_update_alert.add_action': 'ADD',
    'cart.order_to_send.comment_update_alert.update_action': 'UPDATE',
    'cart.order_to_send.not_submit_to_supplier_checkbox_label':
        'NOT SUBMIT TO SUPPLIER',
    'cart.order_to_send.send_order_checkbox_label': 'SEND ORDER',
    'cart.order_to_send.merge_orders_button_label': 'Merge orders',
    'cart.order_to_send.delivery_date_updated_successfully':
        'Delivery date successfully',
    'cart.order_to_send.external_comment_updated_successfully':
        'External comment updated successfully',
    'cart.order_to_send.status.ok': 'Ok',
    'cart.order_to_send.status.no_error': 'No errors',
    'cart.order_to_send.status.no_order_email_address':
        'No order email address',
    'cart.order_to_send.status.no_customer_id': 'No customer id',
    'cart.order_to_send.status.not_found': 'Order not found',
    'cart.order_to_send.status.wrong_delivery_date': 'Wrong delivery date',
    'cart.order_to_send.status.approval_required': 'Approval required',
    'cart.order_to_send.status.no_order': 'No order',
    'cart.order_to_send.status.backend_error': 'Backend error',
    'cart.order_to_send.status.minimal_order_value_is_not_reached':
        'Minimal order value is not reached',
    'cart.order_to_send.status.supplier_fto_disabled':
        'Free Text Orders are disabled for the supplier',
    'cart.order_to_send.status.cost_types_is_not_valid': 'Budget is not set',
    'cart.order_to_send.status.unknown': 'Unknown',
    'cart.order_to_send.status.label': 'Status',

    'cart.orders_to_merge.title': 'Merge orders',
    'cart.orders_to_merge.no_orders': 'You have no orders to merge',
    'cart.orders_to_merge.merge_button_label': 'MERGE SELECTED',
    'cart.orders_to_merge.merge_alert.content':
        'Are you sure to merge selected orders?',
    'cart.orders_to_merge.merge_alert.no_action': 'CANCEL',
    'cart.orders_to_merge.merge_alert.yes_action': 'YES',
    'cart.orders_to_merge.orders_merged_successfully':
        'Orders merged successfully',
    'cart.order_to_merge.select_to_merge_label': 'SELECT TO MERGE',

    'cart.sending_results.title': 'Sending results',
    'cart.sending_result.order_value': 'Order value',
    'cart.sending_result.discount_amount': 'Discount amount',
    'cart.sending_result.date_of_delivery': 'Delivery date',
    'cart.sending_result.order_status': 'Order status',
    'cart.sending_result.order_marked_to_only_create':
        'The order marked to not send to the supplier',
    'cart.sending_result.sent_successful_label': 'SENT SUCCESSFUL',
    'cart.sending_result.failed_to_send_label': 'FAILED TO SEND',

    'cart.product_offers.changes_available_only_online':
        'Changes available only online',
    'cart.product_offers.title': 'Product offers',
    'cart.product_offers.offer.packing_info': 'Packing info',
    'cart.product_offers.offer.price_per_unit': 'Price per unit',
    'cart.product_offers.offer.quantity': 'Quantity',
    'cart.note.add_title': 'Add note',
    'cart.note.edit_title': 'Edit note',
    'cart.note.note_filed_label': 'Note',
    'cart.note.add_button_label': 'ADD',
    'cart.note.edit_button_label': 'EDIT',
    'cart.note.save_button_label': 'SAVE',
    'cart.note.delete_alert_content': 'Do you really want to remove cart note?',
    'cart.note.delete_alert_yes_action': 'YES',
    'cart.note.delete_alert_no_action': 'NO',
    'cart.note.updated_successfully': 'Cart note updated successfully',
    'cart.note.no_note_title': 'No note',
    'cart.note.no_note_description': 'You can add some note here',

    'add_to_cart_button.add_to_cart.label': 'Add to cart',
    'add_to_cart_button.product_approved_in_cart.message':
        'This product is already approved in cart',
    'add_to_cart_button.consolidated_cart_item.message':
        'This product was consolidated',
    'add_to_cart_button.offline.message': 'Impossible to change while offline',
    'add_to_cart_button.quantity_field_label': 'Quantity',
    'add_to_cart_button.quantity_add_label': 'ADD',
    'add_to_cart_button.quantity_update_label': 'UPDATE',
    'add_to_cart_button.product_delete_from_cart_alert.content':
        'Delete item from the cart?',
    'add_to_cart_button.product_delete_from_cart_alert.no_action': 'NO',
    'add_to_cart_button.product_delete_from_cart_alert.yes_action': 'YES',
    'add_to_cart_button.last_ordered_quantity.label': 'Last ordered quantity:',
    'add_to_cart_button.delivery_time.label': 'Delivery time:',
    'add_to_cart_button.days.label': '{{days}} days',
    'add_to_cart_button.directly_available.label': 'Directly available',
    'add_to_cart_button.quantity_in_approval.label': 'Quantity in approval:',
    'add_to_cart_button.current_price': 'Price:',
    'add_to_cart_button.bulk_prices_button_label': '% BULK PRICE OFFERS',
    'add_to_cart_button.cart_update_error.label':
        'Error updating products in cart',
    'add_to_cart_button.cart_update_success.label': 'Cart updated successfully',
    'add_to_cart_button.cart_delete_success.label':
        'Successfully removed from cart',
    'advanced_search.filter.availability': 'Availability',
    'advanced_search.filter_value.is_available': 'Available only',
    'advanced_search.filter_value.is_new': 'New only',
    'advanced_search.filter_value.core_item': 'Core item',
    'request_supplier_customer_id_button.label': 'REQUEST SUPPLIER ID',
    'request_supplier_customer_id_button.offline_message':
        'Impossible while offline',
    'request_supplier_customer_id_button.no_permission_for_requesting_customer_id':
        "You don't have permission to request customer id",
    'add_to_cart_button.impossible_to_change_quantity.message':
        'Impossible to change current product quantity',
    'add_to_cart_button.minimum_buyable_qty': 'Minimum buyable quantity:',
    'add_to_cart_button.price_levels': 'Price levels:',

    'cart.auto_consolidate.title': 'Select cost centers',
    'cart.auto_consolidate.cost_centers.select_all': 'Select all',
    'cart.auto_consolidate.consolidate_button': 'Consolidate',
    'cart.auto_consolidate.no_cost_centers_selected':
        'No cost centers selected',
    'cart.auto_consolidate.available_only_online':
        'Auto consolidation is available only while online',
    'cart.auto_consolidate.success_message':
        'Auto consolidation was successful',

    'cart.menu_count_label': '({{count}})',

    //Freetext orders
    'cart.free_text_order.form.create_title': 'Create Free Text Order',
    'cart.free_text_order.form.edit_title': 'Edit Free Text Order',
    'cart.free_text_order.form.created_successfully':
        'Order created successfully',

    'cart.free_text_order.form.title_field_label': 'Item title',
    'cart.free_text_order.form.supplier_label': 'Supplier',
    'cart.free_text_order.form.category_label': 'Product category',
    'cart.free_text_order.form.quantity_label': 'Quantity',
    'cart.free_text_order.form.unit_price_label': 'Price',
    'cart.free_text_order.form.unit_price_set_button': 'SET',
    'cart.free_text_order.form.content_units_per_order_unit_label':
        'Number of content units per order unit',
    'cart.free_text_order.form.quantity_set_button': 'SET',
    'cart.free_text_order.form.order_unit_label': 'Order unit',
    'cart.free_text_order.form.content_unit_label': 'Content unit',
    'cart.free_text_order.form.description_label': 'Item description',
    'cart.free_text_order.form.item_number_label': 'Item number',
    'cart.free_text_order.form.inventory_unit_label': 'Inventory unit',
    'cart.free_text_order.form.vat_gst_rate_label': 'VAT/GST rate',
    'cart.free_text_order.form.optional_details_label': 'Optional details',
    'cart.free_text_order.form.please_fill_all_required_fields':
        'Please, fill all required fields',
    'cart.free_text_order.form.save_button_label': 'SAVE',
    'cart.free_text_order.form.default_price_alert':
        'Price is required for approval process. If price is not defined, it might be a default {{default_price}} per unit.',
    'cart.free_text_order.form.select_supplier_first':
        'Please, select Supplier first',
    'cart.free_text_order.form.quantity_change_restricted':
        "You can't change quantity for the current product.",
    'cart.free_text_order.form.price_change_restricted':
        "You can't change price for the current product.",
    'cart.free_text_order.form.item_approved_in_cart':
        'This item is already approved in cart',
    'cart.free_text_order.form.read_only_field_edit_message':
        "You can't change this field",
    'cart.free_text_order.form.item_in_cart_field_edit_message':
        "You can't change this field for the item already in cart",
    'cart.free_text_order.form.read_only_field_existing_product_message':
        "You can't change this field for existing product",
    'cart.free_text_order.supplier_lookup.title': 'Supplier',
    'cart.free_text_order.supplier_lookup.only_online':
        'Supplier search is available only online',
    'cart.free_text_order.supplier_lookup.select_button_label': 'SELECT',
    'cart.free_text_order.product_lookup.title': 'Item title',
    'cart.free_text_order.product_lookup.add_button_label': 'ADD',
    'cart.free_text_order.product_lookup.previously_ordered_label':
        'PREVIOUSLY ORDERED',
    'cart.free_text_order.product_lookup.only_online':
        'Previously ordered products can be loaded only while online',
    'cart.free_text_order.product_lookup.not_found':
        'No previously ordered products found. You can ADD a new one.',

    'free_text_order.badge_label': 'FREE TEXT ORDER',

    //Advanced search
    'advanced_search.no_permissions_label':
        'You have no permissions to access products search',
    'advanced_search.filter.button.clear_all.label': 'CLEAR ALL',
    'advanced_search.filter.clear.label': 'CLEAR',
    'advanced_search.filter.apply.label': 'APPLY',
    'advanced_search.filter.no_filters_available.label': 'No filters available',
    'advanced_search.catalog_indexing_is_in_progress':
        'Search indexes is not ready for current catalog. Pleas try again later.',
    'advanced_search.filter.all_filters.label': 'All filters',

    'advanced_search.filter.type.WSO': 'WSO',
    'advanced_search.filter.type.WSW': 'WSW',
    'advanced_search.filter.type.EXT': 'External Article',
    'advanced_search.filter.type.TMP': 'Temporary Article',

    'advanced_search.filter.orderUnit': 'Order unit',
    'advanced_search.filter.contentUnit': 'Content unit',
    'advanced_search.filter.price': 'Price',
    'advanced_search.filter.supplierName': 'Supplier',
    'advanced_search.filter.currencyIso4217': 'Currency',
    'advanced_search.filter.categoryLevel1': 'Category level 1',
    'advanced_search.filter.categoryLevel2': 'Category level 2',
    'advanced_search.filter.categoryLevel3': 'Category level 3',
    'advanced_search.filter.origin': 'Country of origin',
    'advanced_search.filter.addLeadTimeInDays': 'Delivery time',
    'advanced_search.filter.delivery_time.directly_available_value':
        'Directly available',
    'advanced_search.filter.delivery_time.days_value': '{{value}} days',
    'advanced_search.filter.labels': 'Labels',
    'advanced_search.filter.certificates': 'Certified',
    'advanced_search.product.new_tag.label': 'NEW',
    'advanced_search.product.bulk_price_label': 'BULK PRICE',
    'advanced_search.search_available_only_online':
        'Search available only online',
    'advanced_search.product_offer.offer_end_label': 'Offer end:',
    'advanced_search.product_offer.not_available': 'Product is not available',
    'advanced_search.product_offer.not_found': 'Product offer not found',
    'advanced_search.product_offer.no_connection':
        'Please check your network connection',
    'advanced_search.product_offer.no_customer_id':
        'No customer ID for supplier',
    'advanced_search.customer_id_requested_label':
        'Customer id requested at {{requested-at}} by {{requested-by}}',
    'advanced_search.product_offer.supplier': 'Supplier',
    'advanced_search.product_offer.supplier_item_link_label':
        'Details from Supplier',
    'advanced_search.product_offer.supplier_item_link_text':
        'Show Article Details',
    'advanced_search.product_offer.content_units_per_order_unit':
        'Number of content unit per order unit',
    'advanced_search.product_offer.country_of_the_origin': 'Country of origin',
    'advanced_search.product_offer.product_id': 'Article no.',
    'advanced_search.product_offer.barcode': 'Barcode',
    'advanced_search.product_offer.price_last_updated_at':
        'Price last updated at',
    'advanced_search.product_offer.allergens': 'Allergens',
    'advanced_search.product_offer.nutrients': 'Nutrition',
    'advanced_search.product_offer.additives': 'Additives',
    'advanced_search.product_offer.min_unit_order_label': 'Minimum unit order',
    'advanced_search.product_offer.min_order_value_label':
        'Minimum order value',
    'advanced_search.product_offer.min_unit_order.any_label': 'Any',
    'advanced_search.product_offer.min_order_value.any_label': 'Any',
    'advanced_search.product_offer.description': 'Description',
    'advanced_search.product_offer.add_to_list.title': 'Add to list',
    'advanced_search.product_offer.add_to_list.button.label': 'ADD TO LIST',
    'advanced_search.product_offer.add_to_list.dialog.button_label': 'Add',
    'advanced_search.product_offer.added_to_list_successfully_label':
        'Product was added to the list',
    'advanced_search.product_offer.no_permission_for_requesting_customer_id':
        "You don't have permission to request customer ID",
    'advanced_search.product_offer.product_description_title':
        'PRODUCT DESCRIPTION',
    'advanced_search.product_offer.supplier_conditions_title':
        'SUPPLIER CONDITIONS',
    'advanced_search.product_offer.bulk_prices_title': 'BULK PRICES',
    'advanced_search.search_results.product_quantity_in_cart_updated_successfully_label':
        'Cart updated successfully',
    'advanced_search.search_results.added_to_list_successfully_label':
        'Product was added to the list',
    'advanced_search.search_results.product_removed_from_cart_successfully_label':
        'Product was removed from the cart',
    'advanced_search.search_results.product_is_approved_in_cart_label':
        'This product is already approved in cart',

    'advanced_search.sorting.best_match': 'Best match',
    'advanced_search.sorting.price_asc': 'Price low to high',
    'advanced_search.sorting.price_desc': 'Price high to low',
    'advanced_search.sorting.default_sort': 'Default',

    'catalog.search.index_details_button_label': 'Index details',
    'catalog.search.index_details_title': 'Catalog index details',
    'catalog.index.status.in_progress': 'In progress',
    'catalog.index.status.done': 'Done',
    'catalog.index.status.unknown': 'Unknown',
    'catalog.index.details.last_indexed_label': 'Last indexed at',
    'catalog.index.details.products_indexed_label': 'Products indexed',
    'catalog.index.details.total_products_label': 'Total products',
    'catalog.index.details.indexing_status_label': 'Indexing status',
    'catalog.index.details.reindex_label': 'REINDEX',
    'catalog.index.details.refresh_label': 'REFRESH',
    'catalog.index.details.reindex_request_disabled':
        'Reindexing can not be requested right now',
    'catalog.index.details.reindex_request_success': 'Reindexing was requested',

    // Approvals
    'approvals.approver_selection.title': 'Approver selection',
    'approvals.approver_selection.select_button_label': 'SELECT',
    'approvals.approver_selection.selection_available_only_online':
        'Approver selection available only online',

    'approvals.request_id': 'Request id',
    'approvals.category': 'Approval category',
    'approvals.order_value_to_be_approved': 'Order value to be approved',
    'approvals.approver': 'Approver',
    'approvals.purchase_requests.approved.title': 'Approved purchase requests',
    'approvals.purchase_requests.search.placeholder':
        'Purchase requests search',
    'approvals.purchase_requests.search.date_from': 'From date',
    'approvals.purchase_requests.search.date_to': 'To date',
    'approvals.purchase_requests.filter.assigned_to_me': 'Assigned to me',
    'approvals.purchase_requests.filter.open': 'Open',
    'approvals.purchase_requests.filter.in_progress': 'In progress',
    'approvals.purchase_requests.filter.approved': 'Approved',
    'approvals.purchase_requests.filter.closed': 'Closed',
    'approvals.purchase_requests.sort.by_approved_at_desc': 'Approved at desc',
    'approvals.purchase_requests.sort.by_approved_at_asc': 'Approved at asc',
    'approvals.purchase_requests.sort.by_declined_at_desc': 'Declined at desc',
    'approvals.purchase_requests.sort.by_declined_at_asc': 'Declined at asc',
    'approvals.purchase_requests.sort.by_closed_at_desc': 'Closed at desc',
    'approvals.purchase_requests.sort.by_closed_at_asc': 'Closed at asc',
    'approvals.purchase_requests.sort.by_type_desc': 'Type desc',
    'approvals.purchase_requests.sort.by_type_asc': 'Type asc',
    'approvals.purchase_requests.sort.by_total_desc': 'Total desc',
    'approvals.purchase_requests.sort.by_total_asc': 'Total asc',
    'approvals.purchase_request.move_to_cart': 'Move to cart',
    'approvals.purchase_request.delete': 'Delete',
    'approvals.purchase_request.added_to_card_at': 'Added to cart at',
    'approvals.purchase_request.requested_at': 'Requested at',
    'approvals.purchase_request.requested_by': 'Requested by',
    'approvals.purchase_request.decline_reason': 'Decline reason',
    'approvals.purchase_request.accept_reason': 'Accept reason',
    'approvals.purchase_request.status': 'Status',
    'approvals.purchase_request.type': 'Type',
    'approvals.purchase_request.approval_user': 'Approval user',
    'approvals.purchase_request.approval_last_user': 'Approval last user',
    'approvals.purchase_request.approval_last_date': 'Approval last date',
    'approvals.purchase_request.total': 'Total',
    'approvals.purchase_request.approver': 'Approver',
    'approvals.purchase_request.log.name': 'Name',
    'approvals.purchase_request.log.message': 'Message',
    'approvals.purchase_request.log.date': 'Date',
    'approvals.purchase_request.log.user': 'User',
    'approvals.purchase_request.log.no_record':
        'No log records for this purchase request',
    'approvals.purchase_request.documents.no_documents':
        'No documents for this purchase request',
    'approvals.purchase_request.approval_trail.no_record':
        'No approval trail items found for this purchase request',
    'approvals.purchase_request.documents.user': 'User',
    'approvals.purchase_request.documents.date': 'Date',
    'approvals.purchase_request.documents.name': 'Name',
    'approvals.purchase_request.chat.no_messages':
        'No messages for this purchase request',
    'approvals.purchase_request.chat.message_placeholder': 'Message',
    'approvals.purchase_request.request_authorization': 'Request authorization',
    'approvals.purchase_request.update': 'Update',
    'approvals.purchase_request.comment': 'Comment',
    'approvals.purchase_request.status_approved': 'Approved',
    'approvals.purchase_request.status_declined': 'Declined',
    'approvals.purchase_request.status_resetted': 'Resetted',
    'approvals.purchase_request.status_in_progress': 'In progress',
    'approvals.purchase_request.send_after_approval_warning':
        'The products from some suppliers in the list may be sent automatically after last approval',

    'approvals.purchase_request.item.change_quantity_title': 'Change quantity',
    'approvals.purchase_request.item.last_order_date': 'Last order date',
    'approvals.purchase_request.item.last_order_qty': 'Last order qty',
    'approvals.purchase_request.item.last_order_price': 'Last order price',
    'approvals.purchase_request.item.quantity_in_approval':
        'Quantity in approval',
    'approvals.purchase_request.item.delivery_time_label': 'Delivery time',
    'approvals.purchase_request.item.delivery_time.days': '{{days}} days',
    'approvals.purchase_request.item.delivery_time.directly_available':
        'Directly available',
    'approvals.purchase_request.item.qty_updated_successfully':
        'Quantity updated successfully',
    'approvals.purchase_request.item.cost_type_updated_successfully':
        'Cost type updated successfully',
    'approvals.purchase_request.item.status_updated_successfully':
        'Status updated successfully',
    'approvals.purchase_request.item.changing_quantity_not_allowed':
        'Changing quantity is not allowed for this product',
    'approvals.purchase_request.item.quantity_field_label': 'Quantity',
    'approvals.purchase_request.item.quantity_update_label': 'UPDATE',
    'approvals.purchase_request.item.edit_quantity_button_label': 'EDIT',
    'approvals.purchase_request.item.disable_button_label': 'DISABLE',
    'approvals.purchase_request.item.enable_button_label': 'ENABLE',
    'approvals.purchase_request.better_price_available':
        'Better price available',
    'approvals.purchase_request.looking_for_the_next':
        'Looking for the next purchase request',
    'approvals.purchase_request.all_purchase_requests_are_processed':
        'All purchase requests are processed',
    'approvals.purchase_request.better_offers': 'Better offers',
    'approvals.purchase_request.offers': 'Other offers',
    'approvals.purchase_request.show_changes_button_label': 'SHOW CHANGES',
    'approvals.purchase_request.hide_changes_button_label': 'HIDE CHANGES',
    'approvals.purchase_request.qty_changed__named':
        'Quantity changed by {{changed-by}} at {{changed-at}}',
    'approvals.purchase_request.original_qty': 'Original quantity',
    'approvals.purchase_request.last_qty': 'Last quantity',
    'approvals.purchase_request.supplier_changed':
        'Supplier changed by {{changed-by}} at {{changed-at}}',
    'approvals.purchase_request.original_supplier': 'Original supplier',
    'approvals.purchase_request.last_supplier': 'Last supplier',
    'approvals.purchase_request.item_declined':
        'Declined by {{declined-by}} at {{declined-at}}',
    'approvals.purchase_request.reset_button_label': 'Reset',
    'approvals.purchase_request.reset_alert.content':
        'Are you sure to reset this purchase request?',
    'approvals.purchase_request.reset_alert.no_action': 'CANCEL',
    'approvals.purchase_request.reset_alert.yes_action': 'YES',
    'approvals.purchase_request.resetted_successfully':
        'Purchase request resetted successfully!',
    'approvals.purchase_request.move_to_cart_alert.content':
        'Are you sure to move to cart this purchase request?',
    'approvals.purchase_request.move_to_cart_alert.no_action': 'CANCEL',
    'approvals.purchase_request.move_to_cart_alert.yes_action': 'YES',
    'approvals.purchase_request.moved_to_cart_successfully':
        'Purchase request moved to cart successfully!',
    'approvals.purchase_request.details_button_label': 'Details',
    'approvals.purchase_request.log_button_label': 'Changelog',
    'approvals.purchase_request.details.title': 'Details',

    'approvals.purchase_request.product_offers.title': 'Product offers',
    'approvals.purchase_request.product_offers.offer.packing_info':
        'Packing info',
    'approvals.purchase_request.product_offers.offer.price_per_unit':
        'Price per unit',
    'approvals.purchase_request.product_offers.offer.cannot_be_used':
        'This offer cannon be used',
    'approvals.purchase_request.product_offers.offer.not_available':
        'This offer is not available',
    'approvals.purchase_request.product_offers.select_offer_button_label':
        'SELECT',
    'approvals.purchase_request.product_offers.changes_available_only_online':
        'Changes available only online',
    'approvals.purchase_request.product_offers.product_change_alert.content':
        'Are you sure to change current product?',
    'approvals.purchase_request.product_offers.product_change_alert.no_action':
        'CANCEL',
    'approvals.purchase_request.product_offers.product_change_alert.yes_action':
        'YES',
    'approvals.purchase_request.product_offers.product_changed_successfully':
        'Purchase request product changed successfully!',

    'approvals.purchase_request.product_transfer_status.unknown': 'Unknown',
    'approvals.purchase_request.product_transfer_status.not_enabled_for_supplier':
        'Not enabled for supplier',
    'approvals.purchase_request.product_transfer_status.order_contains_consolidated_basket_items':
        'Order contains consolidated basket items',
    'approvals.purchase_request.product_transfer_status.order_error':
        'Order error',
    'approvals.purchase_request.product_transfer_status.order_sent_to_supplier':
        'Order sent to supplier',

    'approvals.approval_trail.title': 'Approval Trail',
    'approvals.approval_trail.empty': 'Nothing here yet',
    'approvals.approval_trail.level_label': 'Level',
    'approvals.approval_trail.in_approval': 'IN APPROVAL',
    'approvals.approval_trail.approved_by_label': 'Approved by',
    'approvals.approval_trail.substitute_label': 'Substitute',
    'approvals.approval_trail.date_time_label': 'Date, time',
    'approvals.approval_trail.amount_label': 'Authorization amount',

    // Purchase Requests
    'approvals.purchase_requests.search_available_only_online':
        'Search available only online',
    'approvals.purchase_requests.sort.by_added_to_cart_at_asc':
        'Added to cart asc',
    'approvals.purchase_requests.sort.by_added_to_cart_at_desc':
        'Added to cart desc',
    'approvals.purchase_requests.sort.by_requested_at_asc': 'Requested at asc',
    'approvals.purchase_requests.sort.by_requested_at_desc':
        'Requested at desc',
    'approvals.purchase_requests.filter.status_label': 'Status',
    'approvals.purchase_requests.filter.status.assigned_to_me':
        'Assigned to me',
    'approvals.purchase_requests.filter.status.assigned_to_my_group':
        "My Groups' Purchase Requests",
    'approvals.purchase_requests.filter.status.open': 'Open',
    'approvals.purchase_requests.filter.status.approved': 'Approved',
    'approvals.purchase_requests.filter.status.closed': 'Closed',
    'approvals.purchase_requests.filter.status.closed_approved':
        'Closed approved',
    'approvals.purchase_requests.filter.status.closed_declined':
        'Closed declined',
    'approvals.purchase_requests.filter.status.date_range': 'Date range',
    'approvals.purchase_requests.filter.cost_center.label': 'Cost Center',
    'approvals.purchase_requests.filter.cost_center.hint_text':
        'Cost Center name',
    'approvals.purchase_requests.filter.supplier.label': 'Supplier',
    'approvals.purchase_requests.filter.supplier.hint_text': 'Supplier name',
    'approvals.purchase_requests.filter.category.label': 'Category',
    'approvals.purchase_requests.filter.category.hint_text': 'Category name',
    'approvals.purchase_requests.filter.description.label': 'Description',
    'approvals.purchase_requests.filter.description.hint_text':
        'Item description',
    'approvals.purchase_requests.purchase_request_delete_alert.content':
        'Are you sure to delete this purchase requests?',
    'approvals.purchase_requests.purchase_request_delete_alert.no_action':
        'CANCEL',
    'approvals.purchase_requests.purchase_request_delete_alert.yes_action':
        'YES',
    'approvals.purchase_request.approve_button_label': 'APPROVE',
    'approvals.purchase_request.decline_button_label': 'DECLINE',
    'approvals.purchase_request.update_button_label': 'UPDATE',
    'approvals.purchase_request.request_approval_button_label':
        'REQUEST APPROVAL',
    'approvals.purchase_request.move_to_cart_button_label': 'MOVE TO CART',
    'approvals.purchase_request.send_directly_to_supplier.button_label':
        'SEND DIRECTLY TO SUPPLIER',
    'approvals.purchase_request.send_directly_to_supplier.alert.content':
        'Are you sure to send order directly to supplier?',
    'approvals.purchase_request.send_directly_to_supplier.alert.no_action':
        'CANCEL',
    'approvals.purchase_request.send_directly_to_supplier.alert.yes_action':
        'YES',
    'approvals.purchase_request.send_directly_to_supplier.sent_successfully_label':
        'Order sent successfully to the supplier!',
    'approvals.purchase_request.send_directly_to_supplier.open_log_button_label':
        'LOG',
    'approvals.purchase_request.send_directly_to_supplier.sending_error_label':
        'Error happen while sending order directly to the supplier.',

    'approvals.purchase_request.orders_sending_result.title': 'Sending results',
    'approvals.purchase_request.orders_sending_result.order_value':
        'Order value',
    'approvals.purchase_request.orders_sending_result.status': 'Status',
    'approvals.purchase_request.orders_sending_result.order_status':
        'Order status',
    'approvals.purchase_request.orders_sending_result.supplier': 'Supplier',
    'approvals.purchase_request.orders_sending_result.sending_status.sent_successful_label':
        'SENT SUCCESSFULLY',
    'approvals.purchase_request.orders_sending_result.sending_status.not_sent':
        'NOT SENT',
    'approvals.purchase_request.available_only_online':
        'Purchase request is available only online',

    'approvals.purchase_requests.purchase_request_update_alert.content':
        'Are you sure to update this purchase request?',
    'approvals.purchase_requests.purchase_request_update_alert.no_action':
        'CANCEL',
    'approvals.purchase_requests.purchase_request_update_alert.yes_action':
        'YES',
    'approvals.purchase_requests.purchase_request_updated_successfully':
        'Purchase request updated successfully!',
    'approvals.purchase_requests.approval_request_alert.content':
        'Are you sure to request approval?',
    'approvals.purchase_requests.approval_request_alert.no_action': 'CANCEL',
    'approvals.purchase_requests.approval_request_alert.yes_action': 'YES',
    'approvals.purchase_requests.approval_requested_successfully':
        'Approval requested successfully',
    'approvals.purchase_requests.move_to_cart_alert.content':
        'Are you sure to move this purchase request to cart?',
    'approvals.purchase_requests.move_to_cart_alert.no_action': 'CANCEL',
    'approvals.purchase_requests.move_to_cart_alert.yes_action': 'YES',
    'approvals.purchase_requests.purchase_request_moved_to_cart_successfully':
        'Purchase request moved to cart successfully!',
    'approvals.purchase_requests.item.open_order_action': 'Open order',
    'approvals.purchase_requests.item.delivery_date': 'Delivery date',
    'approvals.purchase_requests.item.stock_on_hand': 'SOH',
    'approvals.purchase_requests.item.min_stock': 'Min',
    'approvals.purchase_requests.item.max_stock': 'Max',
    'approvals.purchase_requests.item.deleted_message':
        'This item has been deleted by {{deleted-by}} at {{deleted-at}}',
    'approvals.purchase_requests.item.log_button_label': 'Article History',

    'approvals.purchase_requests.item.attachments_title': 'Attachments',

    'approvals.purchase_requests.item.edit.edit_title': 'Edit Article',
    'approvals.purchase_requests.item.edit.create_title': 'Create Offer',
    'approvals.purchase_requests.item.edit.check_network_connection':
        'Please, check your network connection',
    'approvals.purchase_requests.item.edit.field.supplier': 'Supplier',
    'approvals.purchase_requests.item.edit.field.name': 'Article Name',
    'approvals.purchase_requests.item.edit.field.article_no': 'Article no.',
    'approvals.purchase_requests.item.edit.field.gtin': 'GTIN',
    'approvals.purchase_requests.item.edit.field.order_unit': 'Order unit',
    'approvals.purchase_requests.item.edit.field.content_unit': 'Content unit',
    'approvals.purchase_requests.item.edit.field.content_units_per_order_unit':
        'Number of content units per Order Unit',
    'approvals.purchase_requests.item.edit.field.order_unit_price':
        'Price per Order Unit',
    'approvals.purchase_requests.item.edit.field.currency': 'Currency',
    'approvals.purchase_requests.item.edit.field.inventory_unit_factor':
        'Inventory unit factor',
    'approvals.purchase_requests.item.edit.field.item_price': 'Price per Item',
    'approvals.purchase_requests.item.edit.field.tax_rate': 'Tax rate',
    'approvals.purchase_requests.item.edit.save': 'Save',
    'approvals.purchase_requests.item.edit.duplicate': 'Duplicate',
    'approvals.purchase_requests.item.edit.create_offer': 'Create Offer',
    'approvals.purchase_requests.item.edit.offer_saved_successfully':
        'Changes saved successfully',
    'approvals.purchase_requests.item.edit.offer_duplicate_successfully':
        'Offer was duplicated successfully',
    'approvals.purchase_requests.item.edit.offer_replaced_successfully':
        'Offer was replaced successfully',
    'approvals.purchase_requests.item.edit.offer_created_successfully':
        'Offer was created successfully',
    'approvals.purchase_requests.item.edit.article_will_be_duplicated':
        'Article will be Duplicated.',
    'approvals.purchase_requests.item.edit.new_offer_from_supplier':
        'New Offer from this supplier.',

    'approvals.purchase_requests.item.log_title': 'Article History',
    'approvals.purchase_requests.item.log.no_data':
        'No history for current article',
    'approvals.purchase_requests.item.log.new_data': 'New data',
    'approvals.purchase_requests.item.log.previous_data': 'Previous data',
    'approvals.purchase_requests.item.cost_type': 'Cost type',
    'approvals.purchase_requests.item.read_only_budget':
        "Budget can't be changed for this approved request products",

    'approvals.purchase_requests.item.edit.suppliers_title': 'Suppliers',
    'approvals.purchase_requests.item.edit.change_supplier_title':
        'Change supplier',
    'approvals.purchase_requests.item.edit.change_supplier.create_offer_button':
        'Create Offer',
    'approvals.purchase_requests.item.edit.change_supplier.change_button':
        'Change Supplier',
    'approvals.purchase_requests.item.edit.change_supplier.cant_find_offer':
        'Can not find Offer from Supplier?',

    'approvals.invoices.search_available_only_online':
        'Search available only online',
    'approvals.invoices.filter.sorting.by_invoice_date_asc': 'Invoice date ASC',
    'approvals.invoices.filter.sorting.by_invoice_date_desc':
        'Invoice date DESC',
    'approvals.invoices.filter.sorting.by_requested_at_asc': 'Requested at ASC',
    'approvals.invoices.filter.sorting.by_requested_at_desc':
        'Requested at DESC',
    'approvals.invoices.filter.sorting.by_upload_date_asc': 'Upload date ASC',
    'approvals.invoices.filter.sorting.by_upload_date_desc': 'Upload date DESC',
    'approvals.invoices.filter.sorting.by_supplier_name_asc':
        'Supplier name A-Z',
    'approvals.invoices.filter.sorting.by_supplier_name_desc':
        'Supplier name Z-A',
    'approvals.invoices.filter.status.title': 'Status',
    'approvals.invoices.filter.status.assigned_to_me': 'Assigned to me',
    'approvals.invoices.filter.status.ready_to_approve': 'Ready to approve',
    'approvals.invoices.filter.invoice_id.label': 'Invoice id',
    'approvals.invoices.filter.invoice_id.hint_text': 'Invoice id',
    'approvals.invoices.filter.supplier_invoice_id.label':
        'Supplier invoice id',
    'approvals.invoices.filter.supplier_invoice_id.hint_text': 'Invoice id',
    'approvals.invoices.filter.supplier_name.label': 'Supplier name',
    'approvals.invoices.filter.supplier_name.hint_text': 'Supplier name',

    'approvals.invoices.invoice.status.in_approval': 'IN APPROVAL',
    'approvals.invoices.invoice.details_tab': 'Details',
    'approvals.invoices.invoice.approval_trail_tab': 'Approval Trail',
    'approvals.invoices.invoice.attachments_tab': 'Attachments',
    'approvals.invoices.invoice.view_pdf': 'View PDF',
    'approvals.invoices.invoice.reset_button_label': 'Reset',
    'approvals.invoices.invoice.log_button_label': 'Changelog',
    'approvals.invoices.invoice.view_pdf_button_label': 'View PDF',
    'approvals.invoices.invoice.reset_reason_comment_title':
        'Please provide reset reason',
    'approvals.invoices.invoice.reset_reason_comment_label': 'Reset reason',
    'approvals.invoices.invoice.reset_reason_title': 'Reset reason',
    'approvals.invoices.invoice.reset_confirmation.reset_action': 'Reset',
    'approvals.invoices.invoice.request_approval': 'Request approval',
    'approvals.invoices.invoice.approval_request_confirmation.title':
        'Are you sure to request approval?',
    'approvals.invoices.invoice.approval_requested_successfully':
        'Approval requested successfully',
    'approvals.invoices.invoice.approve': 'Approve',
    'approvals.invoices.invoice.view_pdf_button': 'View PDF',
    'approvals.invoices.invoice.approve_confirmation.title':
        'Are you sure to approve this invoice?',
    'approvals.invoices.invoice.invoice_approved_successfully':
        'Invoice approved successfully!',
    'approvals.invoices.invoice.resetted_successfully':
        'Invoice resetted successfully',
    'approvals.invoices.invoice.approver': 'Approver',
    'approvals.invoices.invoice.invoice_date': 'Invoice date',
    'approvals.invoices.invoice.amount_brutto': 'Amount Gross',
    'approvals.invoices.invoice.amount_tax': 'Amount Tax',
    'approvals.invoices.invoice.amount_netto': 'Amount Net',
    'approvals.invoices.invoice.invoice_comment': 'Invoice comment',
    'approvals.invoices.invoice.attachments_label': 'Attachmnets',
    'approvals.invoices.invoice.next_approver_label': 'Next approver',
    'approvals.invoices.invoice.attachments.document': '{{count}} document',
    'approvals.invoices.invoice.attachments.documents': '{{count}} documents',

    'approvals.invoices.invoice.status.checksum_error': 'Checksum Error',
    'approvals.invoices.invoice.status.approval': 'In Approval',
    'approvals.invoices.invoice.status.approved': 'Approved',
    'approvals.invoices.invoice.status.no_errors': 'No Errors',

    'approvals.invoices.invoice.send_to_accounting': 'Send to Accounting',
    'approvals.invoices.invoice.send_to_accounting_confirmation.title':
        'Are you sure to send this invoice to accounting?',
    'approvals.invoices.invoice.send_to_accounting_confirmation.send': 'Send',
    'approvals.invoices.invoice.send_to_accounting_successful':
        'Invoice sent successfully',
    'approvals.invoices.invoice.send_to_accounting_open': 'OPEN',

    'approvals.booking_requests.search_available_only_online':
        'Search available only online',
    'approvals.booking_requests.sort.by_requested_at_asc': 'Requested at asc',
    'approvals.booking_requests.sort.by_requested_at_desc': 'Requested at desc',
    'approvals.booking_requests.sort.by_approved_at_asc': 'Approved at asc',
    'approvals.booking_requests.sort.by_approved_at_desc': 'Approved at desc',
    'approvals.booking_requests.sort.by_declined_at_asc': 'Declined at asc',
    'approvals.booking_requests.sort.by_declined_at_desc': 'Declined at desc',
    'approvals.booking_requests.sort.by_closed_at_asc': 'Closed at asc',
    'approvals.booking_requests.sort.by_closed_at_desc': 'Closed at desc',
    'approvals.booking_requests.sort.by_total_amount_asc': 'Total amount asc',
    'approvals.booking_requests.sort.by_total_amount_desc': 'Total amount desc',
    'approvals.booking_requests.filter.status_label': 'Status',
    'approvals.booking_requests.filter.status.all': 'All',
    'approvals.booking_requests.filter.status.not_assigned': 'Not assigned',
    'approvals.booking_requests.filter.status.assigned_to_me': 'Assigned to me',
    'approvals.booking_requests.filter.status.in_progress': 'In progress',
    'approvals.booking_requests.filter.status.approved': 'Approved',
    'approvals.booking_requests.filter.status.closed': 'Closed',
    'approvals.booking_requests.filter.status.date_range': 'Date range',
    'approvals.booking_requests.approve_button_label': 'APPROVE',
    'approvals.booking_requests.decline_button_label': 'DECLINE',
    'approvals.booking_requests.update_button_label': 'UPDATE',
    'approvals.booking_requests.request_approval_button_label':
        'REQUEST APPROVAL',
    'approvals.booking_requests.booking_approval_request_update_alert.content':
        'Are you sure to update this booking approval request?',
    'approvals.booking_requests.booking_approval_request_update_alert.no_action':
        'CANCEL',
    'approvals.booking_requests.booking_approval_request_update_alert.yes_action':
        'YES',
    'approvals.booking_requests.booking_approval_request_updated_successfully':
        'Booking approval request updated successfully!',
    'approvals.booking_requests.approval_request_alert.content':
        'Are you sure to request approval?',
    'approvals.booking_requests.approval_request_alert.no_action': 'CANCEL',
    'approvals.booking_requests.approval_request_alert.yes_action': 'YES',
    'approvals.booking_requests.approval_requested_successfully':
        'Approval requested successfully',
    'approvals.booking_requests.documents.no_documents':
        'No documents for this booking approval request',
    'approvals.booking_requests.documents.no_approval_trail':
        'No approval trail items found for this booking request',
    'approvals.booking_requests.chat.no_messages':
        'No messages for this booking approval request',
    'approvals.booking_requests.log.no_record':
        'No log records for this booking approval request',
    'approvals.booking_requests.item.inventory_unit_price_label':
        'Inventory unit price',
    'approvals.booking_requests.item.receiving_division_price_label':
        'Price at receiving division',
    'approvals.booking_requests.item.total_label': 'Total',
    'approvals.booking_requests.item.last_order_date': 'Last order date',
    'approvals.booking_requests.item.last_order_qty': 'Last order qty',
    'approvals.booking_requests.item.last_order_price': 'Last order price',
    'approvals.booking_requests.item.qty_updated_successfully':
        'Quantity updated successfully',
    'approvals.booking_requests.item.status_updated_successfully':
        'Status updated successfully',
    'approvals.booking_requests.item.stock_on_hand': 'SOH',
    'approvals.booking_requests.item.min_stock': 'Min',
    'approvals.booking_requests.item.max_stock': 'Max',
    'approvals.booking_requests.item.quantity_field_label': 'Quantity',
    'approvals.booking_requests.item.quantity_update_label': 'UPDATE',
    'approvals.booking_requests.show_changes_button_label': 'SHOW CHANGES',
    'approvals.booking_requests.hide_changes_button_label': 'HIDE CHANGES',
    'approvals.booking_requests.qty_changed':
        'Quantity changed by {{changed-by}} at {{changed-at}}',
    'approvals.booking_requests.original_qty': 'Original quantity',
    'approvals.booking_requests.last_qty': 'Last quantity',
    'approvals.booking_requests.reset_button_label': 'Reset',
    'approvals.booking_requests.details_button_label': 'Details',
    'approvals.booking_requests.view_pdf_button_label': 'View PDF',
    'approvals.booking_requests.log_button_label': 'Changelog',
    'approvals.booking_requests.reset_alert.content':
        'Are you sure to reset this booking approval request?',
    'approvals.booking_requests.reset_alert.no_action': 'CANCEL',
    'approvals.booking_requests.reset_alert.yes_action': 'YES',
    'approvals.booking_requests.resetted_successfully':
        'Booking approval request resetted successfully!',
    'approvals.booking_request.details.title': 'Details',
    'approvals.booking_request.details.total': 'Total',
    'approvals.booking_request.details.list_name': 'List name',
    'approvals.booking_request.details.requested_at': 'Requested at',
    'approvals.booking_request.details.requested_by': 'Requested by',
    'approvals.booking_request.details.status_label': 'Status',
    'approvals.booking_request.details.type': 'Type',
    'approvals.booking_request.details.approval_user': 'Approval user',
    'approvals.booking_request.details.status.in_progress': 'In progress',
    'approvals.booking_request.details.status.approved': 'Approved',
    'approvals.booking_request.details.status.declined': 'Declined',
    'approvals.booking_request.details.status.not_assigned': 'Not assigned',
    'approvals.booking_request.comment': 'Comment',
    'approvals.booking_request.approver': 'Approver',
    'approvals.booking_request.total': 'Total',
    'approvals.booking_request.product.enable_button_label': 'ENABLE',
    'approvals.booking_request.product.disable_button_label': 'DISABLE',
    'approvals.booking_request.item.edit_quantity_button_label': 'EDIT',
    'approvals.booking_request.available_only_online':
        'Booking approval request is available only online',

    'approvals.purchase_requests.reset_alert.content':
        'Are you sure to reset this purchase request?',

    'approvals.receiving_requests.search_available_only_online':
        'Search available only while online',
    'approvals.receiving_requests.decline_button_label': 'DECLINE',
    'approvals.receiving_requests.approve_button_label': 'APPROVE',
    'approvals.receiving_requests.receiving_approval_request_approve_alert.content':
        'Are you sure want to approve current request?',
    'approvals.receiving_requests.receiving_approval_request_decline_alert.content':
        'Are you sure want to decline current request?',
    'approvals.receiving_requests.receiving_approval_request_update_alert.no_action':
        'NO',
    'approvals.receiving_requests.receiving_approval_request_update_alert.yes_action':
        'YES',
    'approvals.receiving_requests.receiving_approval_request_updated_successfully':
        'Receiving approval request update successfully',
    'approvals.receiving_request.products.list_only_online':
        'Products can be loaded only while online.',
    'approvals.receiving_requests.product.delivery_date': 'Delivery date:',
    'approvals.receiving_requests.product.iu_ordered': 'Quantity IU ordered:',
    'approvals.receiving_requests.product.iu_delivered':
        'Quantity IU delivery note:',
    'approvals.receiving_requests.product.iu_price_ordered':
        'Price IU ordered:',
    'approvals.receiving_requests.product.iu_price_delivered':
        'Price IU delivery note:',
    'approvals.receiving_requests.product.total_amount_ordered':
        'Total ordered:',
    'approvals.receiving_requests.product.total_amount_delivered':
        'Total delivery note:',
    'approvals.receiving_requests.product.quantity_variance':
        'Quantity variance:',
    'approvals.receiving_requests.product.value_variance': 'Value variance:',
    'approvals.receiving_requests.product.total_variance': 'Total variance:',
    'approvals.receiving_requests.product.comment_button_label': 'ADD COMMENT',
    'approvals.receiving_requests.product.comment_label': 'Comment',
    'approvals.receiving_requests.products.product_comment_updated_successfully':
        'Product comment updated',
    'approvals.receiving_requests.product.add_comment.title':
        'Update product comment',
    'approvals.receiving_requests.product.add_comment.field_label': 'Comment',
    'approvals.receiving_requests.product.add_comment.action_label': 'UPDATE',
    'approvals.request.messages.can_be_loaded_online':
        'Messages can be loaded only while online',
    'approvals.request.messages.empty':
        'There are no messages for this request',
    'approvals.requests.documents.approval_trail_only_online':
        'Approval trail can be loaded only while online',
    'approvals.request.documents.can_be_loaded_online':
        'Documents can be loaded only while online',
    'approvals.request.old_documents_alert':
        'These files have been attached previously. They will not be appearing at Goods Receiving and Invoices pages. Please re-upload them to see at the next stages.',

    'approvals.receiving_requests.sort.by_requested_at_asc': 'Requested at asc',
    'approvals.receiving_requests.sort.by_requested_at_desc':
        'Requested at desc',
    'approvals.receiving_requests.sort.by_approved_at_asc': 'Approved at asc',
    'approvals.receiving_requests.sort.by_approved_at_desc': 'Approved at desc',
    'approvals.receiving_requests.sort.by_declined_at_asc': 'Declined at asc',
    'approvals.receiving_requests.sort.by_declined_at_desc': 'Declined at desc',
    'approvals.receiving_requests.sort.by_closed_at_asc': 'Closed at asc',
    'approvals.receiving_requests.sort.by_closed_at_desc': 'Closed at desc',
    'approvals.receiving_requests.sort.by_total_amount_asc': 'Total amount asc',
    'approvals.receiving_requests.sort.by_total_amount_desc':
        'Total amount desc',
    'approvals.receiving_requests.filter.status_label': 'Status',
    'approvals.receiving_requests.filter.status.any': 'Any',
    'approvals.receiving_requests.filter.status.not_assigned': 'Not assigned',
    'approvals.receiving_requests.filter.status.assigned_to_me':
        'Assigned to me',
    'approvals.receiving_requests.filter.status.in_progress': 'In progress',
    'approvals.receiving_requests.filter.status.approved': 'Approved',
    'approvals.receiving_requests.filter.status.closed': 'Closed',
    'approvals.receiving_requests.filter.status.date_range': 'Date range',
    'approvals.approval_requests.reset_button_label': 'Reset',
    'approvals.approval_requests.details_button_label': 'Details',
    'approvals.approval_requests.view_pdf_button_label': 'View PDF',
    'approvals.approval_requests.log_button_label': 'Log',
    'approvals.receiving_requests.reset_alert.content':
        'Are you sure to reset this receiving approval request?',
    'approvals.receiving_request.details.title': 'Details',
    'approvals.receiving_request.details.total': 'Total',
    'approvals.receiving_request.details.order_id': 'Order id',
    'approvals.receiving_request.details.requested_at': 'Requested at',
    'approvals.receiving_request.details.requested_by': 'Requested by',
    'approvals.receiving_request.details.status_label': 'Status',
    'approvals.receiving_request.details.type': 'Type',
    'approvals.receiving_request.details.approval_user': 'Approval user',
    'approvals.receiving_request.details.status.in_progress': 'In progress',
    'approvals.receiving_request.details.status.approved': 'Approved',
    'approvals.receiving_request.details.status.declined': 'Declined',
    'approvals.receiving_request.details.status.not_assigned': 'Not assigned',
    'approvals.requests.reset_alert.no_action': 'CANCEL',
    'approvals.requests.reset_alert.yes_action': 'YES',
    'approvals.requests.chat.no_messages': 'No messages',
    'approvals.request.message_sent': 'Message sent',
    'approvals.request.log_menu_item': 'Activity Log',
    'approvals.request.approval_trail_menu_item': 'Approval Trail',
    'approvals.requests.resetted_successfully': 'Approval request was resetted',
    'approvals.receiving_requests.update_button_label': 'UPDATE',
    'approvals.receiving_requests.request_approval_button_label':
        'REQUEST APPROVAL',
    'approvals.receiving_requests.approval_request_alert.content':
        'Are you sure to request approval?',
    'approvals.receiving_requests.approval_request_alert.no_action': 'CANCEL',
    'approvals.receiving_requests.approval_request_alert.yes_action': 'YES',
    'approvals.receiving_requests.booking_approval_request_update_alert.content':
        'Are you sure to update this receiving approval request?',
    'approvals.receiving_requests.booking_approval_request_update_alert.no_action':
        'CANCEL',
    'approvals.receiving_requests.booking_approval_request_update_alert.yes_action':
        'YES',
    'approvals.receiving_request.comment': 'Comment',
    'approvals.receiving_request.approver': 'Approver',
    'approvals.receiving_request.total': 'Total',
    'approvals.receiving_requests.approval_requested_successfully':
        'Approval requested successfully',
    'approvals.receiving_request.available_only_online':
        'Receiving approval request is available only online',

    'approvals.capex.active_tab': 'Active',
    'approvals.capex.authorized_tab': 'Authorized',
    'approvals.capex.closed_tab': 'Closed',
    'approvals.capex_requests.search_available_only_online':
        'Search available only while online',
    'approvals.capex_requests.status.created': 'CREATED',
    'approvals.capex_requests.status.in_approval': 'IN APPROVAL',
    'approvals.capex_requests.status.approved': 'APPROVED',
    'approvals.capex_requests.status.declined': 'DECLINED',
    'approvals.capex_requests.status.deleted': 'DELETED',
    'approvals.capex_requests.status.closed': 'CLOSED',
    'approvals.capex_requests.status.unknown': 'UNKNOWN',
    'approvals.capex_requests.budgeted_status.budgeted': 'Budgeted',
    'approvals.capex_requests.budgeted_status.not_budgeted': 'Not budgeted',
    'approvals.capex_requests.budgeted_status.budget_not_defined':
        'Budget not defined',
    'approvals.capex_requests.budgeted_status.unknown': 'Unknown',
    'approvals.capex_requests.sort.by_created_at_desc': 'Recently added',
    'approvals.capex_requests.sort.by_name_asc': 'Name (A-Z)',
    'approvals.capex_requests.sort.by_name_desc': 'Name (Z-A)',
    'approvals.capex_requests.filter.division_label': 'Division',
    'approvals.capex_requests.filter.division_hint': 'Division ID',
    'approvals.capex_requests.filter.status_label': 'Status',
    'approvals.capex_requests.filter.status.active': 'All Active',
    'approvals.capex_requests.filter.status.created': 'Created',
    'approvals.capex_requests.filter.status.in_progress': 'In approval',
    'approvals.capex_requests.filter.status.assigned_to_me': 'Assigned to me',
    'approvals.capex_requests.filter.status.authorized': 'All Authorized',
    'approvals.capex_requests.filter.status.approved': 'Approved',
    'approvals.capex_requests.filter.status.declined': 'Declined',
    'approvals.capex_requests.filter.status.closed': 'Closed',
    'approvals.capex_requests.total_amount': 'Total amount',
    'approvals.capex_requests.used_amount': 'Used amount',

    'approvals.capex_request.approve_button_label': 'Approve',
    'approvals.capex_request.decline_button_label': 'Decline',
    'approvals.capex_request.request_approval_button_label': 'Request Approval',
    'approvals.capex_request.fields_not_filled_error_message':
        'Mandatory fields are not filled. Please go to P2P/CAPEX.',

    'approvals.capex_request.overview.title': 'Overview',
    'approvals.capex_request.overview.fields_not_filled_error':
        'Cannot Request Approval. Mandatory fields are not filled. Please go to P2P/CAPEX.',
    'approvals.capex_request.overview.last_approver_commented':
        'Last Approver Commented:',
    'approvals.capex_request.overview.budget': 'Budget',
    'approvals.capex_request.overview.date_nad_time': 'Date and Time',
    'approvals.capex_request.overview.division': 'Division',
    'approvals.capex_request.overview.cost_center': 'Cost Center',
    'approvals.capex_request.overview.department': 'Department',
    'approvals.capex_request.overview.created_by': 'Created by',
    'approvals.capex_request.overview.total_amount': 'Total Amount',
    'approvals.capex_request.overview.amount_in_division_currency':
        'Amount in Division currency',
    'approvals.capex_request.overview.used_amount': 'Used Amount',
    'approvals.capex_request.overview.used_amount_in_division_currency':
        'Used Amount in Division currency',
    'approvals.capex_request.overview.supplier': 'Supplier',

    'approvals.capex_request.details.title': 'Details',
    'approvals.capex_request.details.capex_description_title':
        'CAPEX Description',
    'approvals.capex_request.details.additional_details_title':
        'Additional Details',
    'approvals.capex_request.details.suppliers_title': 'Suppliers',
    'approvals.capex_request.details.supplier.selected_tag': 'SELECTED',
    'approvals.capex_request.details.supplier.reason_of_selection':
        'Reason for Selecting Supplier',
    'approvals.capex_request.details.supplier.contract_period':
        'Contract Period',
    'approvals.capex_request.details.accounting_title': 'Accounting',
    'approvals.capex_request.details.instructions_for_accounting':
        'Instructions for accounting',

    'approvals.capex_request.documents.title': 'Documents',

    'approvals.capex_request.invoices.title': 'Assigned invoices',
    'approvals.capex_request.invoices.amount_gross': 'Invoice Amount (gross)',

    'approvals.capex_request.messages.title': 'Messages',
    'approvals.capex_request.messages.add_label': 'Add a Message',
    'approvals.capex_request.messages.message_label': 'Message',

    'approvals.capex_request.update.available_only_online':
        'CAPEX approval request is available only online',
    'approvals.capex_request.update.approve_title': 'Approve CAPEX?',
    'approvals.capex_request.update.decline_title': 'Decline CAPEX?',
    'approvals.capex_request.update.request_title': 'Request Approval',
    'approvals.capex_request.update.can_not_be_authorized':
        'This CAPEX can no longer be approved or declined',
    'approvals.capex_request.update.can_not_be_requested':
        'Approval of this CAPEX can no longer be requested',
    'approvals.capex_request.update.user_can_not_authorize':
        'You have no rights to approve or decline this CAPEX',
    'approvals.capex_request.update.please_select_approver':
        'Please, select approver first',
    'approvals.capex_request.update.please_add_comment': 'Please, add comment',
    'approvals.capex_request.update.approved_successfully':
        'CAPEX was approved successfully',
    'approvals.capex_request.update.declined_successfully':
        'CAPEX approval was declined',
    'approvals.capex_request.update.requested_successfully':
        'Approval Requested Successfully',
    'approvals.capex_request.update.last_approver': 'Last Approver',
    'approvals.capex_request.update.last_date': 'Date, Time',
    'approvals.capex_request.update.last_comment': 'Last Approver Commented:',
    'approvals.capex_request.update.add_comment': 'Add a Comment',
    'approvals.capex_request.update.comment_label': 'Comment',
    'approvals.capex_request.update.approval_level': 'Approval Level',
    'approvals.capex_request.update.approval_level_text':
        'Level {{level}}, {{amount}}',
    'approvals.capex_request.update.approver_label': 'Select Approver',
    'approvals.capex_request.update.next_approver_label': 'Next Approver',
    'approvals.capex_request.update.request_approval_confirmation.title':
        'Request Approval?',
    'approvals.capex_request.update.request_approval_confirmation.yes':
        'Send Request',

    'approvals.activity_log.title': 'Activity Log',
    'approvals.log.available_only_online': 'Changelog available only online',
    'approvals.log.no_records': 'No records',

    'approvals.request.attachments_label': 'Attachments',
    'approvals.request.attachments_document_count': '{{count}} document',
    'approvals.request.attachments_documents_count': '{{count}} documents',

    // Invoices archive
    'invoices_archive.details_tab': 'Details',
    'invoices_archive.activity_log_tab': 'Activity log',
    'invoices_archive.attachments_tab': 'Attachments',
    'invoices_archive.search_available_only_online':
        'Search available only online',
    'invoices_archive.filter.status_label': 'Status',
    'invoices_archive.filter.status.open': 'Open',
    'invoices_archive.filter.status.ready_to_approve': 'Ready to approve',
    'invoices_archive.filter.status.assigned_to_me': 'Assigned to me',
    'invoices_archive.filter.status.in_approval': 'In approval',
    'invoices_archive.filter.status.ready_to_transfer_to_accounting':
        'Ready to transfer to accounting',
    'invoices_archive.filter.status.sepa_payment': 'SEPA payment',
    'invoices_archive.filter.sorting.by_invoice_date_asc': 'Invoice date ASC',
    'invoices_archive.filter.sorting.by_invoice_date_desc': 'Invoice date DESC',
    'invoices_archive.filter.sorting.by_supplier_name_asc': 'Supplier name A-Z',
    'invoices_archive.filter.sorting.by_supplier_name_desc':
        'Supplier name Z-A',
    'invoices_archive.filter.status.date_range': 'Date range',
    'invoices_archive.invoice.supplier': 'Supplier',
    'invoices_archive.invoice.information_title': 'Invoice Information',
    'invoices_archive.invoice.requested_by_user': 'Requested by',
    'invoices_archive.invoice.id': 'Invoice id',
    'invoices_archive.invoice.invoice_date': 'Invoice date',
    'invoices_archive.invoice.booking_date': 'Booking date',
    'invoices_archive.invoice.accounting_instruction':
        'Accounting instructions',
    'invoices_archive.invoice.invoice_comment': 'Invoice comment',
    'invoices_archive.invoice.accounting_assignment_information_title':
        'Account Assignment Information',
    'invoices_archive.invoice.no_account_assignment_records':
        'There is no account assignment records',
    'invoices_archive.invoice.division': 'Division',
    'invoices_archive.invoice.cost_type': 'Cost type',
    'invoices_archive.invoice.cost_center': 'Costcenter',
    'invoices_archive.invoice.amount_net': 'Amount Net',
    'invoices_archive.invoice.amount_gross': 'Amount Gross',
    'invoices_archive.invoice.tax_amount': 'Tax amount',
    'invoices_archive.invoice.view_pdf_button_label': 'View PDF',

    // Invoices
    'invoices.type.delivery_note_not_found': 'NO DELIVERY INFO FOUND',
    'invoices.type.with_delivery_note': 'WITH DELIVERY NOTE',
    'invoices.type.without_delivery_note': 'NO DELIVERY INFO',
    'invoices.scanned.scan': 'Scan',
    'invoices.scanned.no_scanned_invoices': 'There are no \nscanned \ninvoices',
    'invoices.scanned.no_scanned_invoices_offline':
        'There are no scanned invoices.\n\nUploaded invoices could be viewed only with an active internet connection.',
    'invoices.scanned.no_scanned_pages': 'There is no scanned pages',
    'invoices.scanned.page.title': 'Page {{number}}',
    'invoices.scanned.page.count': 'page',
    'invoices.scanned.pages.count': 'pages',
    'invoices.scanned.page.position': 'Page',
    'invoices.scanned.page_delete_confirmation':
        'Are you sure to delete this page?',
    'invoices.scanned.page_delete_confirmation_no_action': 'NO',
    'invoices.scanned.page_delete_confirmation_yes_action': 'YES',
    'invoices.scanned.one_a_time_warning':
        'At the moment, the system supports scanning only one invoice at a time.',
    'invoices.scanned.page.edit.title': 'Edit',
    'invoices.scanned.page.edit.save': 'Save changes',

    'invoices.tax_breakdown.title': 'Tax breakdown',
    'invoices.tax_breakdown.total_tax': 'Total tax',
    'invoices.tax_breakdown.amount_net': 'Amount Net',
    'invoices.tax_breakdown.amount_gross': 'Amount Gross',

    'invoices.scanned.invoice.scan_button_label': 'Scan new page',
    'invoices.scanned.invoice.attach_button_label': 'Attach page',
    'invoices.scanned.invoice.upload_button_label': 'Upload invoice',
    'invoices.scanned.invoice.has_no_pages_label':
        'Scanned document has no pages',
    'invoices.scanned.invoice.upload_alert.title': 'Upload Invoice?',
    'invoices.scanned.invoice.upload_alert.no_action': 'Cancel',
    'invoices.scanned.invoice.upload_alert.yes_action': 'Upload',
    'invoices.scanned.invoice.uploaded_successfully_label':
        'Invoice uploaded successfully',
    'invoices.scanned.invoice.invoice_id_label': 'Invoice ID',
    'invoices.scanned.invoice.scanning_id_label': 'Scanning ID',
    'invoices.scanned.invoice.document_unavailable': 'Document unavailable',

    'invoices.scanned.processing_status.short.ok': 'Ok',
    'invoices.scanned.processing_status.short.processing': 'Processing',
    'invoices.scanned.processing_status.short.error': 'Error',
    'invoices.scanned.processing_status.short.doubled': 'Duplicate',
    'invoices.scanned.processing_status.short.overdue': 'Overdue',
    'invoices.scanned.processing_status.short.deleted': 'Deleted',
    'invoices.scanned.processing_status.short.unknown': 'Unknown',

    'invoices.scanned.processing_status.extended.ok': 'Invoice Processing Done',
    'invoices.scanned.processing_status.extended.processing':
        'Invoice Processing',
    'invoices.scanned.processing_status.extended.error':
        'Invoice Processing Error',
    'invoices.scanned.processing_status.extended.doubled': 'Invoice Duplicate',
    'invoices.scanned.processing_status.extended.overdue': 'Invoice Overdue',
    'invoices.scanned.processing_status.extended.deleted': 'Invoice Deleted',
    'invoices.scanned.processing_status.extended.unknown':
        'Invoice Status Unknown',

    'invoices.scanned.invoice.web_info.title':
        'To see uploaded Invoice, switch to WEB version.',
    'invoices.scanned.invoice.web_info.description.ok':
        'Transactions / Invoices / Open Invoices',
    'invoices.scanned.invoice.web_info.description.error':
        'Transactions / Invoices / Error Invoices',
    'invoices.scanned.invoice.web_info.description.doubled':
        'Transactions / Invoices / Double Invoices',
    'invoices.scanned.invoice.web_info.description.overdue':
        'Transactions / Invoices / Overdue',
    'invoices.scanned.invoice.web_info.description.deleted':
        'Transactions / Invoices / Deleted Invoices',
    'invoices.scanned.invoice.web_info.description.unknown':
        'Transactions / Invoices',

    'invoices.document_scanner.crop_black_white_label': 'Black White',
    'invoices.document_scanner.crop_reset_label': 'Reset',
    'invoices.document_scanner.crop_label': 'Crop',
    'invoices.document_scanner.scan_label': 'Scanning',

    // Orders
    'orders.astore.title': 'AStore Orders',
    'orders.delivery_date': 'Delivery date',
    'orders.order_date': 'Order date',
    'orders.order_articles_to_merge': 'Articles to Merge',
    'orders.order_articles_merge_needed': 'Merge needed',
    'orders.amount': 'Amount',
    'orders.order.only_create_status': 'Not sent to the supplier',
    'orders.order.marked_to_only_create_label':
        'This order was not sent to the supplier',
    'orders.order.view_pdf_button_label': 'View PDF',
    'orders.order.send_message_to_supplier.button_label': 'Send E-Mail',
    'orders.order.send_message_to_supplier.label': 'Send E-Mail',
    'orders.order.send_message_to_supplier.reply_to_email_label':
        'Reply to email',
    'orders.order.send_message_to_supplier.email_format_error':
        'Wrong email format',
    'orders.order.send_message_to_supplier.email_is_empty_error':
        'Please type email',
    'orders.order.send_message_to_supplier.message_label': 'Message',
    'orders.order.send_message_to_supplier.message_is_empty_error':
        'Please type message',
    'orders.order.send_message_to_supplier.message_sent_successfully':
        'Message sent',
    'orders.order.send_message_to_supplier.discard_message_alert.title':
        'Discard message',
    'orders.order.send_message_to_supplier.discard_message_alert.content':
        'Are you sure you want to discard this draft?',
    'orders.order.send_message_to_supplier.discard_message_alert.no_action':
        'NO',
    'orders.order.send_message_to_supplier.discard_message_alert.yes_action':
        'YES',
    'orders.order.send_message_to_supplier.send_button_label': 'SEND',

    'orders.search_available_only_online':
        'Orders search available only online',
    'orders.order.products_search_available_only_online':
        'Products search available only online',

    'orders.filter.sorting.by_order_date_asc': 'Order date A-Z',
    'orders.filter.sorting.by_order_date_desc': 'Order date Z-A',
    'orders.filter.status_label': 'Status',
    'orders.filter.status.all': 'All',
    'orders.filter.status.open': 'Open',
    'orders.filter.status.completed': 'Completed',
    'orders.filter.date_range': 'Date range',
    'orders.filter.orderer_label': 'Orderer',
    'orders.filter.orderer.any': 'Any',
    'orders.filter.cost_center_label': 'Cost center',
    'orders.filter.cost_center.any': 'Any',

    'orders.astore.filter.sorting.by_order_date_asc':
        'Order date (oldest first)',
    'orders.astore.filter.sorting.by_order_date_desc':
        'Order date (newest first)',
    'orders.astore.filter.sorting.by_supplier_asc': 'Supplier (A-Z)',
    'orders.astore.filter.sorting.by_supplier_desc': 'Supplier (Z-A)',
    'orders.astore.filter.date_range': 'Order date range',
    'orders.astore.filter.supplier_label': 'Supplier',
    'orders.astore.filter.supplier_hint': 'Supplier name',
    'orders.astore.filter.order_number_label': 'Order number',
    'orders.astore.filter.order_number_hint': 'Order number',

    'orders.astore.can_be_merged_only_in_web_label':
        'Articles can be merged only at WEB version.',
    'orders.astore.can_be_merged_only_in_web_path':
        'Ordering / Order Archive / AStore Orders',

    'orders.order.title': 'Order',
    'orders.order.articles.title': 'Articles',
    'orders.order.external_comment.title': 'External comment',
    'orders.order.order_unit_price': 'Price / Order unit',
    'orders.order.content_unit_price': 'Price / Content unit',
    'orders.order.order_quantity': 'Order quantity',
    'orders.order.order_total': 'Total',
    'orders.order.discount_amount': 'Discount amount',
    'orders.order.total_after_discount': 'Total after discount',
    'orders.order.order_again.button_label': 'Order again',
    'orders.order.order_again.title': 'Order again',
    'orders.order.order_again.products_not_found_label': 'Products not found',
    'orders.order.order_again.total_value_label': 'TOTAL VALUE',
    'orders.order.order_again.send_to_the_cart_button_label':
        'SEND TO THE CART',
    'orders.order.order_again.product.package_ordered': 'Package ordered',
    'orders.order.order_again.product.price_ordered': 'Price ordered',
    'orders.order.order_again.product.actual': 'Actual',
    'orders.order.order_again.product.sum': 'Sum',
    'orders.order.order_again.product.quantity_field_label': 'Quantity',
    'orders.order.order_again.product.quantity_update_button_label': 'UPDATE',
    'orders.order.order_again.product.edit_button_label': 'EDIT',
    'orders.order.order_again.send_alert.content':
        'Are you sure to send products to the cart?',
    'orders.order.order_again.send_alert.no_action': 'CANCEL',
    'orders.order.order_again.send_alert.yes_action': 'YES',
    'orders.order.order_again.ordered_again_successfully_label':
        'Ordered again successfully',
    'orders.order.order_again.ordered_again_partially_label':
        'Not all products were added to cart',
    'orders.order.order_again.processing_products': 'Processing products...',
    'orders.order.order_again.products_processed': 'Products processed:',
    'orders.order.order_again.sent_view_log_button_label': 'VIEW LOG',

    'orders.order.target_cost_centers.button_label': 'Target cost centers',
    'orders.breakdown_by_cost_centers.title': 'Breakdown by cost center',
    'orders.target_cost_centers.only_online':
        'Target cost centers can be viewed only online',
    'orders.target_cost_centers.total_label': 'Total',
    'orders.target_cost_centers.order_number': 'Order number',

    'orders.external_comment.none': 'No external comments',

    'orders.product.order_history_title': 'Order History',
    'orders.product.order_history': 'Order History',

    // Order lists
    'order_lists.filter.sorting.by_name_asc': 'Name A-Z',
    'order_lists.filter.sorting.by_name_desc': 'Name Z-A',
    'order_lists.filter.sorting.most_used_first': 'Most used first',
    'order_lists.filter.sorting.last_modified_first': 'Last modified first',
    'order_lists.filter.type_label': 'Type',
    'order_lists.filter.type.all': 'All',
    'order_lists.filter.type.read_only': 'Read only',
    'order_lists.filter.type.cost_center_only': 'Cost center only',
    'order_lists.filter.type.recipes': 'Recipes',
    'order_lists.new_list_name_label': 'Name of the order list',
    'order_lists.new_list_added_successfully_label':
        'New list added successfully',
    'order_lists.search_available_only_online':
        'Order lists search available only online',

    'order_lists.order_list.renamed_successfully_label':
        'List renamed successfully',
    'order_lists.order_list.edit_name_label': 'Edit name',
    'order_lists.order_list.download_list_label': 'Download list',
    'order_lists.order_list.list_overview_label': 'Order list overview',
    'order_lists.order_list.reorder_products_label': 'Reorder products',
    'order_lists.order_list.product_deleted_successfully_label':
        'Product deleted successfully',
    'order_lists.order_list.undo_product_deletion_successfully_label':
        'Product returned successfully',
    'order_lists.order_list.product_quantity_in_cart_updated_successfully_label':
        'Product quantity in cart updated successfully',
    'order_lists.order_list.product_supplier_updated_successfully_label':
        'Product supplier updated successfully',
    'order_lists.order_list.product_removed_from_cart_successfully_label':
        'Product removed from cart successfully',
    'order_lists.order_list.downloaded_successfully_label':
        'List downloaded successfully',
    'order_lists.order_list.already_downloaded_message':
        'This list was already downloaded in the past',
    'order_lists.order_list.open_downloaded_list_button_label': 'OPEN',
    'order_lists.order_list.product_delete_alert.content':
        'Delete item from the list?',
    'order_lists.order_list.product_delete_alert.no_action': 'NO',
    'order_lists.order_list.product_delete_alert.yes_action': 'YES',
    'order_lists.order_list.product_delete_from_cart_alert.content':
        'Delete item from the cart?',
    'order_lists.order_list.product_delete_from_cart_alert.no_action': 'NO',
    'order_lists.order_list.product_delete_from_cart_alert.yes_action': 'YES',
    'order_lists.order_list.products_search_available_only_online':
        'Order list products search available only online',
    'order_lists.order_list.open_catalog_button_label': 'OPEN CATALOG',

    'order_lists.products_change_order.title': 'Reorder products',
    'order_lists.products_change_order.save_button.label': 'Save',
    'order_lists.products_reorder.successful_label':
        'List products reordered successfully',
    'order_lists.products_change_order.available_only_online':
        'Products reorder available only online',

    'order_lists.supplier_change.title': 'Change supplier',
    'order_lists.supplier_change.button_label': 'CHANGE SUPPLIER',
    'order_lists.supplier_change.action_available_only_online':
        'This action available only online',
    'order_lists.supplier_change.search_available_only_online':
        'Supplier search available only online',
    'order_lists.copy_to_list.title': 'Copy to list',
    'order_lists.copy_to_list.button_label': 'COPY',
    'order_lists.move_to_list.title': 'Move to list',
    'order_lists.move_to_list.button_label': 'MOVE',
    'order_lists.add_to_list.title': 'Add to list',
    'order_lists.add_to_list.button_label': 'ADD',
    'order_lists.order_list.product.units_in_cart_label': 'Units in cart:',

    'order_lists.order_list.product.supplier': 'Supplier',
    'order_lists.order_list.product.added_on': 'Product added on',
    'order_lists.order_list.product.content_units_per_order_unit':
        'Number of content unit per order unit ',
    'order_lists.order_list.product.country_of_the_origin':
        'Country of the origin',
    'order_lists.order_list.product.product_id': 'Article no.',
    'order_lists.order_list.product.barcode': 'Barcode',
    'order_lists.order_list.product.price_last_updated_at':
        'Price last updated at',
    'order_lists.order_list.product.supplier_changed_successfully_label':
        'Supplier changed successfully',
    'order_lists.order_list.product.quantity_field_label': 'Quantity',
    'order_lists.order_list.product.quantity_add_label': 'ADD',
    'order_lists.order_list.product.quantity_update_label': 'UPDATE',
    'order_lists.order_list.product.delete_button_label': 'Delete',
    'order_lists.order_list.product.change_supplier_button_label':
        'Change supplier',
    'order_lists.order_list.product.details_button_label': 'Details',
    'order_lists.order_list.product.copy_to_list_button_label': 'Copy to list',
    'order_lists.order_list.product.move_to_list_button_label': 'Move to list',
    'order_lists.order_list.product.copied_successfully_label':
        'Product copied successfully',
    'order_lists.order_list.product.moved_successfully_label':
        'Product moved successfully',
    'order_lists.order_list.product.comment_edited_successfully_label':
        'Product comment edited successfully!',
    'order_lists.order_list.product.comment_deleted_successfully_label':
        'Product comment deleted successfully!',
    'order_lists.order_list.product.comment_added_successfully_label':
        'Product comment added successfully!',
    'order_lists.order_list.product.comment.comment_label': 'COMMENT',
    'order_lists.order_list.product.comment.no_comment_label':
        'There is no comment for this product',
    'order_lists.order_list.product.comment.add_button_label': 'ADD COMMENT',
    'order_lists.order_list.product.comment.add.title': 'Add comment',
    'order_lists.order_list.product.comment.edit.title': 'Edit comment',
    'order_lists.order_list.product.comment.edit.field_label': 'Comment',
    'order_lists.order_list.product.comment.edit.save_button_label':
        'SAVE COMMENT',
    'order_lists.order_list.product.comment.delete_alert.content':
        'Are you sure to delete comment?',
    'order_lists.order_list.product.comment.delete_alert.no_action': 'NO',
    'order_lists.order_list.product.comment.delete_alert.yes_action': 'YES',
    'order_lists.order_list.product.comment.edit_button_label': 'Edit comment',
    'order_lists.order_list.product.comment.delete_button_label':
        'Delete comment',

    'order_lists.order_list.read_note.note_label': 'NOTE',
    'order_lists.order_list.read_note.edit_button_label': 'Edit note',
    'order_lists.order_list.read_note.delete_button_label': 'Delete note',
    'order_lists.order_list.read_note.no_note_label':
        'There is no note for a list',
    'order_lists.order_list.read_note.add_button_label': 'ADD NOTE',
    'order_lists.order_list.read_note.edit.title': 'Edit note',
    'order_lists.order_list.read_note.add.title': 'Add note',
    'order_lists.order_list.read_note.edit.field_label': 'Note',
    'order_lists.order_list.read_note.edit.save_button_label': 'SAVE NOTE',
    'order_lists.order_list.read_note.delete_alert.content':
        'Are you sure to delete read note?',
    'order_lists.order_list.read_note.delete_alert.no_action': 'NO',
    'order_lists.order_list.read_note.delete_alert.yes_action': 'YES',
    'order_lists.order_list.read_note.deleted_successfully':
        'Read note deleted successfully!',
    'order_lists.order_list.read_note.edited_successfully':
        'Read note edited successfully!',
    'order_lists.order_list.read_note.added_successfully':
        'Read note added successfully!',
    'order_lists.create.title': 'New Order List',
    'order_lists.create.name_should_not_be_empty':
        'List name could not be empty',
    'order_lists.create.create_button_label': 'CREATE',

    'order_lists.selection.item_is_already_on_list':
        'This item is already on list: {{list}}',

    'order_lists.local.title': 'Saved order lists',
    'order_lists.local.no_lists_saved':
        'You have no saved order lists. Do you want to load them now?',
    'order_lists.local.load_button_label': 'LOAD',
    'order_lists.local.load_successfully': 'Loaded successfully!',
    'order_lists.local.no_lists_in_cost_center':
        'There are no lists in this cost center!',
    'order_lists.local.last_updated_at_label':
        'Order lists was last updated at {{date}}',
    'order_lists.local.update_button_label': 'UPDATE',
    'order_lists.local.update.alert.content':
        'Are you sure to update loaded order lists?',
    'order_lists.local.update.alert.no_action': 'CANCEL',
    'order_lists.local.update.alert.yes_action': 'YES',
    'order_lists.local.open_button_label': 'OPEN SAVED ORDER LISTS',
    'order_lists.local.need_catalog_rebuild':
        'Not all lists where loaded because Catalog needs to be rebuilt. Try again later.',
    'order_lists.local.loaded_count': 'Lists loaded:',
    'order_lists.local.loading_lists': 'Loading lists...',
    'order_lists.order_list.local.last_updated_at_label':
        'This order list is saved locally. Last update was at {{date}}',

    // Common lists labels
    'lists.name_has_been_updated': 'List name has been changed successfully',
    'lists.error_during_update':
        'Error occurred while updating list. Data has not been saved.',

    // Dashboard labels
    'dashboard.welcome_label__named': 'Welcome\n{{user-name}}',

    // Suppliers
    'suppliers.filter.status_label': 'Status',
    'suppliers.filter.status.all': 'All',
    'suppliers.filter.status.favorite': 'Favorite',
    'suppliers.filter.status.wso': 'External',
    'suppliers.filter.status.wsw': 'Internal',
    'suppliers.filter.sorting.by_name_asc': 'Name A-Z',
    'suppliers.filter.sorting.by_name_desc': 'Name Z-A',
    'suppliers.supplier_favorite_mark_updated_successfully':
        'Supplier favorite mark updated successfully',
    'suppliers.no_permission_to_view_supplier_products_alert.content':
        'You have no permissions to view supplier products',
    'suppliers.search_available_only_online': 'Search available only online',
    'suppliers.expired_contract_label': 'EXPIRED CONTRACT(S)',
    'suppliers.soon_to_expire_contract_label': 'SOON TO EXPIRE CONTRACT(S)',

    'supplier.contact_label': 'Contact',
    'supplier.phone': 'Phone',
    'supplier.second_phone': 'Phone 2',
    'supplier.fax_label': 'Fax',
    'supplier.internet_label': 'Internet',
    'supplier.terms_of_payment_label': 'Terms of payment',
    'supplier.terms_of_delivery_label': 'Terms of delivery',
    'supplier.delivery_days_label': 'Delivery days',
    'supplier.customer_id': 'Customer id',
    'supplier.customer_id_requested_by': 'Customer id requested by',
    'supplier.customer_id_requested_at': 'Customer id requested at',
    'supplier.customer_id_is_not_provided_label':
        'Customer id for this supplier is not provided',
    'supplier.customer_id_requested_label__named':
        'Customer id requested at {{requested-at}} by {{requested-by}}',
    'supplier.request_customer_id_button_label': 'REQUEST',
    'supplier.contact_person': 'Contact person',
    'supplier.title': 'Supplier',
    'supplier.products_button_label': 'PRODUCTS',
    'supplier.oci_catalog_button_label': 'OCI CATALOG',
    'supplier.details_available_only_online':
        'Supplier details available only online',
    'supplier.allow_free_text_orders_label': 'Allow free text orders',
    'supplier.no_permissions_to_update_message':
        "You don't have permissions to update supplier",
    'supplier.update_success_message': 'Supplier updated',

    'supplier.customer_id_request.send_button': 'SEND',
    'supplier.customer_id_request.no_permission':
        "You don't have permission to send this request",
    'supplier.customer_id_request.title': 'Customer id request',
    'supplier.customer_id_request.requested_successfully':
        'Customer id requested successfully',
    'supplier.customer_id_request.reply_to_email_filed_label': 'Reply to email',
    'supplier.customer_id_request.wrong_email_format_label':
        'Email format is wrong',
    'supplier.customer_id_request.alert.content':
        'Are you sure you want to request customer id?',
    'supplier.customer_id_request.alert.no_action': 'CANCEL',
    'supplier.customer_id_request.alert.yes_action': 'YES',

    'supplier.expired_contract_label': 'EXPIRED CONTRACT(S)',
    'supplier.soon_to_expire_contract_label': 'SOON TO EXPIRE CONTRACT(S)',
    'supplier.documents_title': 'Documents',
    'supplier.contract_documents': '{{count}} documents',
    'supplier.contract.halal.halal_label': 'HALAL',
    'supplier.contract.halal.expired_halal_label': 'EXPIRED HALAL',
    'supplier.contracts.contract.start_end_date': 'Start-End Date',

    'supplier.can_send_orders_directly_after_approval.label':
        'Can send orders directly after approval',
    'supplier.can_send_orders_directly_after_approval.disabled': 'Disabled',
    'supplier.can_send_orders_directly_after_approval.enabled_only_for_one':
        'Enabled (1 cost center)',
    'supplier.can_send_orders_directly_after_approval.enabled_for_n':
        'Enabled ({{count}} cost centers)',

    'supplier.send_orders_after_approval.cost_centers.filter.sorting.by_name_asc':
        'Name A-Z',
    'supplier.send_orders_after_approval.cost_centers.filter.sorting.by_name_desc':
        'Name Z-A',
    'supplier.send_orders_after_approval.cost_centers.filter.sorting.by_id_asc':
        'ID A-Z',
    'supplier.send_orders_after_approval.cost_centers.filter.sorting.by_id_desc':
        'ID Z-A',
    'supplier.send_orders_after_approval.cost_centers.title': 'Cost Centers',
    'supplier.send_orders_after_approval.cost_centers.search_available_only_online':
        'Search available only online',

    'supplier.documents.filter.sorting.by_name_asc': 'Name (A-Z)',
    'supplier.documents.filter.sorting.by_name_desc': 'Name (Z-A)',
    'supplier.documents.filter.sorting.recently_added': 'Recently added',
    'supplier.documents.filter.sorting.expiration_date': 'Expiration date',
    'supplier.documents.filter.type_label': 'Type',
    'supplier.documents.filter.type.any': 'Any',
    'supplier.documents.filter.type.certificate': 'Certificate',
    'supplier.documents.filter.type.certificate_halal': 'Certificate Halal',
    'supplier.documents.filter.type.catalog': 'Catalog',
    'supplier.documents.filter.type.contract': 'Contract',
    'supplier.documents.filter.type.other': 'Other',

    'supplier.documents.filter.status_label': 'Status',
    'supplier.documents.filter.status.all': 'All',
    'supplier.documents.filter.status.expire_soon': 'Expire soon',
    'supplier.documents.filter.status.expired': 'Expired',
    'supplier.documents.filter.status.normal': 'Normal',
    'supplier.documents.filter.status.no_expiration_date': 'No expiration date',

    'supplier.documents.private_message':
        'You cant open this document because it is private',
    'supplier.documents.title': 'Contracts',
    'supplier.documents.document.private': 'Private',
    'supplier.documents.document_type.certificate': 'Certificate',
    'supplier.documents.document_type.catalog': 'Catalog',
    'supplier.documents.document_type.contract': 'Contract',
    'supplier.documents.document_type.other': 'Other document',
    'supplier.documents.document_type.halal_certificate': 'Certificate Halal',
    'supplier.documents.document_type.unknown': 'Unknown',
    'supplier.documents.document.expired': 'Expired',
    'supplier.documents.document.expire_in_days': 'Expire in {{days}} days',
    'supplier.documents.document.expire_today': 'Expire today',
    'supplier.documents.no_files': 'The document contains no files',
    'supplier.documents.search_available_only_online':
        'Documents can be loaded only while online',
    'supplier.documents.document.halal.certificate': 'Certificate',
    'supplier.documents.document.halal.articles': 'Articles',
    'supplier.documents.document.start_end_date': 'Start-End Date',
    'supplier.halal.start_end_date_alert.title': 'Start and end date',
    'supplier.halal.document.date_unknown': 'Date unknown',
    'supplier.halal.document.loading_error': 'Document loading error',
    'supplier.halal.document.open_in_external_app': 'Open in external app',
    'supplier.halal.start_end_date_alert.close_action': 'Close',
    'supplier.halal.start_end_date_alert.show_certificate_action':
        'Show Certificate',
    'supplier.halal.no_preview': 'Preview is not available',
    'supplier.halal.no_files': "The certificate doesn't contain any files",
    'supplier.halal.products.online_search':
        'Products can be loaded only while online',
    'supplier.halal.product.files.loading_error': 'Product files loading error',
    'supplier.halal.product.order_unit': 'Order Unit',
    'supplier.halal.product.content_unit': 'Content Unit',
    'supplier.halal.product.no_files': 'The product contains no files',
    'supplier.halal.product.search_available_only_online':
        'Search of product files available only online',

    // In-house lists labels
    'inhouse_lists.title': 'Inhouse lists',
    'inhouse_lists.inhouse_list.product.quantity_field_label': 'Quantity',
    'inhouse_lists.inhouse_list.product.quantity_add_label': 'ADD',
    'inhouse_lists.inhouse_list.product.quantity_edit_label': 'EDIT',
    'inhouse_lists.inhouse_list.product.quantity_update_label': 'UPDATE',
    'inhouse_lists.inhouse_list.send_request_button': 'SEND REQUEST',
    'inhouse_lists.inhouse_list.product.select_source_stock':
        'You must select source stock first',

    'inhouse_lists.inv_unit_price': 'Inventory unit price',
    'inhouse_lists.requested_amount': 'Requested amount',
    'inhouse_lists.sort.a_z': 'A-Z',
    'inhouse_lists.sort.z_a': 'Z-A',
    'inhouse_list.search_available_only_online':
        'Inhouse lists search available only online',
    'inhouse_list.item_added_successfully_label': 'Item added successfully',

    'inhouse_lists.inhouse_list.product.all_fields_are_required_message':
        'All fields are required',
    'inhouse_lists.inhouse_list.update_product_label': 'Update product',
    'inhouse_lists.inhouse_list.product.product_update.title': 'Update product',
    'inhouse_lists.inhouse_list.product.source_stock_label': 'Source stock',
    'inhouse_lists.inhouse_list.product.target_stock_label': 'Target stock',
    'inhouse_lists.inhouse_list.product.incoming_cost_type_label':
        'Incoming stock type',
    'inhouse_lists.inhouse_list.product.update_action_button': 'UPDATE',
    'inhouse_lists.inhouse_list.product.update_success': 'Product was updated',

    'inhouse_lists.local.title': 'Saved Inhouse lists',
    'inhouse_lists.local.no_lists_saved':
        'You have no saved inhouse lists. Do you want to load them now?',
    'inhouse_lists.local.load_button_label': 'LOAD',
    'inhouse_lists.local.load_successfully': 'Loaded successfully!',
    'inhouse_lists.local.last_updated_at_label':
        'Inhouse lists was last updated at {{date}}',
    'inhouse_lists.local.update_button_label': 'UPDATE',
    'inhouse_lists.local.update.alert.content':
        'Are you sure to update loaded inhouse lists?',
    'inhouse_lists.local.update.alert.no_action': 'CANCEL',
    'inhouse_lists.local.update.alert.yes_action': 'YES',
    'inhouse_lists.local.open_button_label': 'OPEN SAVED INHOUSE LISTS',
    'inhouse_lists.local.loaded_count': 'Lists loaded:',
    'inhouse_lists.local.loading_lists': 'Loading lists...',
    'inhouse_lists.inhouse_list.local.last_updated_at_label':
        'This inhouse list is saved locally. Last update was at {{date}}',
    'inhouse_lists.local.product.quantity_updated': 'Quantity update success',
    'inhouse_lists.local.no_lists_in_cost_center':
        'There are no lists in this cost center!',

    // Transfer Lists
    'transfer_lists.filter.sorting.by_default_asc': 'Default ascending',
    'transfer_lists.filter.sorting.by_default_desc': 'Default descending',
    'transfer_lists.filter.sorting.by_name_asc': 'Name A-Z',
    'transfer_lists.filter.sorting.by_name_desc': 'Name Z-A',
    'transfer_lists.filter.type_label': 'Type',
    'transfer_lists.filter.type.all': 'All',
    'transfer_lists.filter.type.in_house': 'In House',
    'transfer_lists.filter.type.inter_property': 'Inter Property',

    'transfer_lists.cost_center_lookup.title': 'Cost Center lookup',
    'transfer_lists.cost_center_lookup.button_label': 'SELECT',
    'transfer_lists.cost_center_lookup.search_available_only_online':
        'Search available only online',

    'transfer_lists.transfer_list.type.in_house': 'In House list',
    'transfer_lists.transfer_list.type.inter_property': 'Inter Property list',
    'transfer_lists.transfer_list.unsupported_type':
        'Type of this transfer list is not supported yet',
    'transfer_lists.transfer_list.delete_alert.content':
        'Are you sure to delete this list?',
    'transfer_lists.transfer_list.delete_alert.no_action': 'CANCEL',
    'transfer_lists.transfer_list.delete_alert.yes_action': 'DELETE',
    'transfer_lists.transfer_list.deleted_successfully_label':
        'List deleted successfully!',

    'transfer_lists.transfer_list.create.title.in_house': 'New In House list',
    'transfer_lists.transfer_list.create.title.inter_property':
        'New Inter Property list',
    'transfer_lists.transfer_list.create.please_fill_in_all_values':
        'Please fill in all values',
    'transfer_lists.transfer_list.create.button_label': 'CREATE',
    'transfer_lists.transfer_list.create.name_field_label': 'List name',
    'transfer_lists.transfer_list.create.cost_center_label': 'Cost Center',
    'transfer_lists.transfer_list.create.editable_label': 'Editable',
    'transfer_lists.transfer_list.create.created_successfully':
        'List created successfully!',

    'transfer_lists.transfer_list.menu.edit_name_label': 'Edit name',
    'transfer_lists.transfer_list.menu.add_item_label': 'Add item',
    'transfer_lists.transfer_list.menu.reorder_items_label':
        'Change items order',

    'transfer_lists.transfer_list.rename.new_name_label': 'New name',
    'transfer_lists.transfer_list.rename.renamed_successfully':
        'List renamed successfully!',

    'transfer_lists.transfer_list.items.search_available_only_online':
        'Item search available only online',
    'transfer_lists.transfer_list.items.item.remove_alert.content':
        'Are you sure want to remove this item from list?',
    'transfer_lists.transfer_list.items.item.remove_alert.no_action': 'NO',
    'transfer_lists.transfer_list.items.item.remove_alert.yes_action': 'YES',
    'transfer_lists.transfer_list.items.item.removed_successfully_label':
        'Item removed successfully!',
    'transfer_lists.transfer_list.items.item.qty_updated_successfully_label':
        'Item quantity updated successfully!',
    'transfer_lists.transfer_list.items.item.source_division_label':
        'Source division',
    'transfer_lists.transfer_list.items.item.source_stock_qty_label':
        'Total stock',
    'transfer_lists.transfer_list.items.item.receiving_division_price_label':
        'Price at receiving division',
    'transfer_lists.transfer_list.items.item.source_item_label': 'Source item',
    'transfer_lists.transfer_list.items.item.order_not_possible_due_to_errors':
        'Order not possible due to item errors',
    'transfer_lists.transfer_list.items.item.errors.label':
        'Item has next errors:',
    'transfer_lists.transfer_list.items.item.errors.mapping_error':
        'Mapping error',
    'transfer_lists.transfer_list.items.item.errors.inventory_unit_mapping_error':
        'Inventory unit mapping error',

    'transfer_lists.inter_property_list.source_division_lookup.title':
        'Source Division lookup',
    'transfer_lists.inter_property_list.source_division_lookup.button_label':
        'SELECT',
    'transfer_lists.inter_property_list.source_division_lookup.search_available_only_online':
        'Search available only online',
    'transfer_lists.inter_property_list.target_division_lookup.title':
        'Target Division lookup',
    'transfer_lists.inter_property_list.target_division_lookup.button_label':
        'SELECT',
    'transfer_lists.inter_property_list.target_division_lookup.search_available_only_online':
        'Search available only online',

    'transfer_lists.in_house_list.item_add.title': 'Add item',
    'transfer_lists.in_house_list.item_add.button_label': 'ADD',
    'transfer_lists.in_house_list.item_add.search_available_only_online':
        'Item search available only online',
    'transfer_lists.in_house_list.item_add.added_successfully_label':
        'Item added successfully!',

    'transfer_lists.inter_property_list.item_add.title': 'Add item',
    'transfer_lists.inter_property_list.item_add.button_label': 'ADD',
    'transfer_lists.inter_property_list.item_add.source_division_label':
        'Source division',
    'transfer_lists.inter_property_list.item_add.item_label': 'Article',
    'transfer_lists.inter_property_list.item_add.store_label':
        'Receiving store',
    'transfer_lists.inter_property_list.item_add.cost_type_label': 'Cost type',
    'transfer_lists.inter_property_list.item_add.category_label': 'Category',
    'transfer_lists.inter_property_list.item_add.inventory_unit_average_price_label':
        'Inventory unit average price',
    'transfer_lists.inter_property_list.item_add.comment_label': 'Comment',
    'transfer_lists.inter_property_list.item_add.update_comment_button_label':
        'UPDATE',
    'transfer_lists.inter_property_list.item_add.select_source_division_first_label':
        'Please select source division first',
    'transfer_lists.inter_property_list.item_add.add_alert.content':
        'Are you sure to add this item to the list?',
    'transfer_lists.inter_property_list.item_add.add_alert.no_action': 'CANCEL',
    'transfer_lists.inter_property_list.item_add.add_alert.yes_action': 'YES',
    'transfer_lists.inter_property_list.item_add.item_added_successfully_label':
        'Item added successfully!',
    'transfer_lists.inter_property_list.item_add.item_in_ims_updated_successfully_label':
        'Item in IMS updated successfully!',
    'transfer_lists.inter_property_list.item_add.available_only_online':
        'Item adding available only online',

    'transfer_lists.inter_property_list.item_to_add.title': 'Select item',
    'transfer_lists.inter_property_list.item_to_add.button_label': 'SELECT',
    'transfer_lists.inter_property_list.item_to_add.search_available_only_online':
        'Search available only online',
    'transfer_lists.inter_property_list.item_to_add.item.already_in_list_message':
        'This item is already in list',
    'transfer_lists.inter_property_list.item_to_add.item.can_not_be_added_message':
        'This item can not be added',
    'transfer_lists.inter_property_list.item_to_add.item.store_not_mapped_message':
        'Store not mapped',

    'transfer_lists.inter_property_list.item_mapping.item_is_not_in_ims_label':
        '{{item_name}} is not in IMS',
    'transfer_lists.inter_property_list.item_mapping.merge_item_description':
        'You can merge this item with the existing one',
    'transfer_lists.inter_property_list.item_mapping.merge_item_button_label':
        'MERGE',
    'transfer_lists.inter_property_list.item_mapping.add_to_warehouse_description':
        'You can add this item to your warehouse',
    'transfer_lists.inter_property_list.item_mapping.add_to_warehouse_button_label':
        'ADD TO IMS',
    'transfer_lists.inter_property_list.item_mapping.add_to_warehouse_alert.content':
        'Are you sure to add this item to warehouse?',
    'transfer_lists.inter_property_list.item_mapping.add_to_warehouse_alert.no_action':
        'CANCEL',
    'transfer_lists.inter_property_list.item_mapping.add_to_warehouse_alert.yes_action':
        'YES',
    'transfer_lists.inter_property_list.item_mapping.added_to_warehouse_successfully_label':
        'Item added to warehouse successfully!',

    'transfer_lists.inter_property_list.item_merge.title': 'Merge item',
    'transfer_lists.inter_property_list.item_merge.button_label': 'MERGE',
    'transfer_lists.inter_property_list.item_merge.search_available_only_online':
        'Item search available only online',
    'transfer_lists.inter_property_list.item_merge.merge_alert.content':
        'Are you sure to merge with this item?',
    'transfer_lists.inter_property_list.item_merge.merge_alert.no_action':
        'CANCEL',
    'transfer_lists.inter_property_list.item_merge.merge_alert.yes_action':
        'YES',
    'transfer_lists.inter_property_list.item_merge.merged_successfully_label':
        'Item merged successfully!',
    'transfer_lists.inter_property_list.item_merge.item.inventory_unit_not_mapped_message':
        'Inventory unit of this item is not mapped',
    'transfer_lists.inter_property_list.item_merge.item.can_not_be_merged_message':
        'This item can not be merged',

    'transfer_lists.inter_property_list.item_remap.title': 'Remap item',
    'transfer_lists.inter_property_list.item_remap.no_items_to_select_from_label':
        'There are no items to select from',
    'transfer_lists.inter_property_list.item_remap.search_available_only_online':
        'Item search available only online',
    'transfer_lists.inter_property_list.item_remap.remap_alert.content':
        'Are you sure to remap with this item?',
    'transfer_lists.inter_property_list.item_remap.remap_alert.no_action':
        'CANCEL',
    'transfer_lists.inter_property_list.item_remap.remap_alert.yes_action':
        'YES',
    'transfer_lists.inter_property_list.item_remap.remap_successfully_label':
        'Item remapped successfully!',
    'transfer_lists.inter_property_list.item_remap.item.inventory_unit_not_mapped_message':
        'Inventory unit of this item is not mapped',
    'transfer_lists.inter_property_list.item_remap.item.mapping_error_message':
        'This item has mapping error',
    'transfer_lists.inter_property_list.item_remap.item.already_mapped':
        'This item is already mapped',
    'transfer_lists.inter_property_list.item_remap.item.select_button_label':
        'SELECT',

    'transfer_lists.order_place.title': 'Order place',
    'transfer_lists.order_place.delivery_date_label': 'Delivery date',
    'transfer_lists.order_place.comment_label': 'Comment',
    'transfer_lists.order_place.order_button_label': 'ORDER',
    'transfer_lists.order_place.order_alert.content':
        'Are you sure you want to send transfer list order?',
    'transfer_lists.order_place.order_alert.no_action': 'NO',
    'transfer_lists.order_place.order_alert.yes_action': 'YES',
    'transfer_lists.order_place.in_house_list_order_placed_successfully':
        'In House list order placed successfully!',
    'transfer_lists.order_place.inter_property_list_order_placed_successfully':
        'Inter Property list order placed successfully!',

    'transfer_lists.booking_approval_request.title': 'Booking approval request',
    'transfer_lists.booking_approval_request.approver_label': 'Approver',
    'transfer_lists.booking_approval_request.request_button_label':
        'REQUEST APPROVAL',
    'transfer_lists.booking_approval_request.request_alert.content':
        'Are you sure you want to request booking approval?',
    'transfer_lists.booking_approval_request.request_alert.no_action': 'CANCEL',
    'transfer_lists.booking_approval_request.request_alert.yes_action': 'YES',
    'transfer_lists.booking_approval_request.sent_successfully_label':
        'Approval request sent successfully!',

    'transfer_lists.transfer_list.reorder_items.title': 'Change items order',
    'transfer_lists.transfer_list.reorder_items.save_button_label': 'SAVE',
    'transfer_lists.transfer_list.reorder_items.available_only_online':
        'Changing items order is available only while online',
    'transfer_lists.transfer_list.reorder_items.order_saved_successfully':
        'Items order updated successfully!',

    'transfer_lists.orders.filter.status_label': 'Status',
    'transfer_lists.orders.filter.status.any': 'Any',
    'transfer_lists.orders.filter.status.not_approved': 'Not approved',
    'transfer_lists.orders.filter.status.pending': 'Pending',
    'transfer_lists.orders.filter.status.empty': 'Empty',
    'transfer_lists.orders.filter.status.confirmed': 'Confirmed',
    'transfer_lists.orders.filter.status.unconfirmed': 'Unconfirmed',
    'transfer_lists.orders.filter.status.booking_deleted': 'Booking deleted',
    'transfer_lists.orders.filter.status.delivered': 'Delivered',
    'transfer_lists.orders.filter.sorting.by_name_asc': 'A-Z',
    'transfer_lists.orders.filter.sorting.by_name_desc': 'Z-A',
    'transfer_lists.orders.filter.sorting.by_order_date_asc': 'Order date A-Z',
    'transfer_lists.orders.filter.sorting.by_order_date_desc': 'Order date Z-A',

    'transfer_lists.orders.order.status.booking_deleted': 'BOOKING DELETED',
    'transfer_lists.orders.order.status.delivered': 'DELIVERED',
    'transfer_lists.orders.order.status.confirmed': 'CONFIRMED',
    'transfer_lists.orders.order.status.unconfirmed': 'UNCONFIRMED',
    'transfer_lists.orders.order.status.not_approved': 'NOT APPROVED',
    'transfer_lists.orders.order.status.pending': 'PENDING',
    'transfer_lists.orders.order.status.empty': 'EMPTY',
    'transfer_lists.orders.order.status.unknown': 'UNKNOWN',
    'transfer_lists.orders.order.view_document_button_label': 'View PDF',
    'transfer_lists.orders.order.last_task_label': 'Last task',
    'transfer_lists.orders.order.last_approval_label': 'Last approval done by',
    'transfer_lists.orders.order.current_level_label':
        'Current authorization level',

    'transfer_lists.orders.order.products.product.source_division_label':
        'Source division',
    'transfer_lists.orders.order.products.product.source_item_label':
        'Source item',
    'transfer_lists.orders.order.products.product.total_amount': 'Total amount',
    'transfer_lists.orders.order.products.search_available_only_online':
        'Order overview available only online',
    'transfer_lists.orders.order.products.booking_deleted_label':
        'Booking was deleted',

    // Login labels
    'login.enter_pin_code': 'Code eingeben',
    'login.forgot_pin_code': 'Forgot Pin-code',
    'login.forgot_password': 'Forgot Password',
    'login.password_recovery_title': 'Forgotten password',
    'login.password_recovery_hint':
        'User ID should start with WS and could contain four digits or letters. You can also use your email.',
    'login.password_recovery_continue_button_label': 'CONTINUE',
    'login.password_recovery_success_header': 'We received your request.',
    'login.password_recovery_hint_accept_button_label': 'UNDERSTOOD',
    'login.password_recovery_success_message':
        'Please check your E-Mail and follow instructions',
    'login.password_recovery_login_button_label': 'GO TO LOGIN',
    'login.password_recovery_phrase':
        'Please enter your user ID or e-mail address.',
    'login.password_reset_title': 'New password',
    'login.password_reset_continue_button_label': 'CONTINUE',
    'login.password_reset_validation.not_start_with_number':
        'No number at the beginning',
    'login.password_reset_validation.length': 'Minimum 8 characters length',
    'login.password_reset_validation.uppercase': 'Contains uppercase letter',
    'login.password_reset_validation.lowercase': 'Contains lowercase letter',
    'login.password_reset_validation.contains_number_or_symbol':
        'Contains number or symbol (@!\$%-_#)',
    'login.password_reset_success_header': 'Password changed successfully',
    'login.password_reset_success_message':
        'Please login with your new password now',
    'login.password_reset_login_button_label': 'GO TO LOGIN',
    'login.login_placeholder': 'Login',
    'login.password_placeholder': 'Password',
    'login.login_or_email_placeholder': 'Login or email',
    'login.new_password_placeholder': 'Set new password',
    'login.info': 'Please enter your login information.',
    'login.no_connection': 'No connection to the server',
    'login.log_in': 'Login',
    'login.or': 'or',
    'login.sso_login': 'User Organization Login',
    'login.wrong_pin_message__named':
        'The entered PIN is not correct. Please try again or select "Forgot PIN" below. You have {{attempts-left}} attempts left',
    'login.create_pin_phrase':
        'Please create your personal PIN code with which you will open the app in the future and authorize transactions',
    'login.retype_pin_phrase': 'Please repeat the PIN entry',
    'login.two_factor_authentication_title': 'Two-factor Authentication',
    'login.two_factor_auth_verify_button_label': 'VERIFY',
    'login.two_factor_auth_code_has_been_sent':
        'A verification code has been sent to your email address:',
    'login.two_factor_authentication_code_valid_for':
        'This code will be valid for {{minutes}} minutes.',
    'login.two_factor_auth_code_it_may_take_a_minute':
        'It may take a minute to receive your code.',
    'login.two_factor_auth_code_have_not_received_it': 'Haven’t received it?',
    'login.two_factor_auth_resend_a_new_code': 'Resend a new code',
    'login.two_factor_auth_seconds_to_resend_code':
        'Resend will be available in {{seconds}} seconds',
    'login.two_factor_auth_placeholder': 'Please enter code here',
    'login.sso.title': 'User Organization Login',
    'login.sso.hotel_name_hint': 'Please enter a hotel name',
    'login.sso.hotel_name_label': 'Hotel name',
    'login.sso.continue_btn': 'Continue',
    'login.sso.external_provider_error': 'External provider not found',

    // Inventory
    'inventory.select_lists_to_send': 'Select list to send',
    'inventory.send_button.label': 'Send',
    'inventory.confirmation.no_lists_to_send': 'You have no lists to send',

    'inventory.signature.enter_name': "Enter the reviewer's name",
    'inventory.signature.please_sign_with_finger':
        'Please sign with your finger in the field below',
    'inventory.signature.signature_examiner_title__named':
        'Signature examiner #{{number}}',
    'inventory.signature.clear_button_label': 'CLEAR',
    'inventory.signature.skip_button_label': 'SKIP',
    'inventory.signature.continue_button_label': 'CONTINUE',

    'inventory.warehouse_item_added_successfully':
        'Warehouse item added successfully to the list',
    'inventory.sync.list_synced_successfully': 'Items synced successfully',
    'inventory.sync.sync_now_button_label': 'SYNC NOW',
    'inventory.sync.sync_required_label':
        'Some products not synchronized.\nPlease check internet connection and sync them',

    'inventory.eans_edit.save_button.label': 'Save',
    'inventory.eans_edit.no_eans_label': 'No eans for this product',
    'inventory.eans_edit.ean_already_in_list': 'Ean already in list',

    'inventory.item.eans_button_label': 'Eans',
    'inventory.item.inventory_unit': 'Inventory unit',
    'inventory.item.quantity': 'Quantity',
    'inventory.item.current_quantity': 'Current quantity',
    'inventory.item.update_quantity_button_label': 'UPDATE',
    'inventory.item.edit_button_label': 'EDIT',
    'inventory.item.eans_updated_successfully': 'Eans updated successfully',
    'inventory.item.cancel_quantity_button_label': 'CANCEL',

    'inventory.items.filter.status_label': 'Status',
    'inventory.items.filter.status.all': 'All',
    'inventory.items.filter.status.not_counted': 'Not counted',
    'inventory.items.filter.status.counted': 'Counted',
    'inventory.items.filter.sorting.by_name_asc': 'By name ASC',
    'inventory.items.filter.sorting.by_name_desc': 'By name DESC',
    'inventory.items.filter.sorting.by_position_asc': 'By position ASC',
    'inventory.items.filter.sorting.by_position_desc': 'By position DESC',
    'inventory.items.filter.category_label': 'Category',
    'inventory.items.filter.category.all': 'All',

    'inventory.no_lists_found': 'No inventory lists found',
    'inventory.search_in_offline_mode_is_not_working':
        'Inventory lists search in offline mode is not working',
    'inventory.open_locally_saved_lists': 'OPEN LOCALLY SAVED LISTS',
    'inventory.items_sync_in_progress': 'Inventory items sync in progress',
    'inventory.offline_inventories.title': 'Offline inventories',
    'inventory.list_sync_confirmation_no': 'No',
    'inventory.list_sync_confirmation_yes': 'Yes',
    'inventory.list_sync_after_offline_confirmation_label':
        'You was offline and have not synced items. Do you want to sync them?',
    'inventory.no_open_local_inventory_lists_label':
        'You have no open local inventory lists',
    'inventory.tap_to_sync_label': 'Tap to sync',

    'inventory.filter.sorting.by_locked_at_asc': 'Locked at ASC',
    'inventory.filter.sorting.by_locked_at_desc': 'Locked at DESC',
    'inventory.filter.status_label': 'Status',
    'inventory.filter.status.all': 'All',
    'inventory.filter.status.not_transferred': 'Not transferred',
    'inventory.filter.status.transferred': 'Transferred',
    'inventory.filter.status.locked_to_me': 'Locked to me',
    'inventory.filter.status.open': 'Open',
    'inventory.filter.status.closed': 'Closed',
    'inventory.filter.status.booked': 'Booked',
    'inventory.filter.status.signed': 'Signed',
    'inventory.list.lock_alert.content':
        'Are you sure to lock this inventory list?',
    'inventory.list.lock_alert.cancel_action': 'CANCEL',
    'inventory.list.lock_alert.confirm_action': 'LOCK',
    'inventory.list.status.closed': 'Closed',
    'inventory.list.status.locked_by_another_user': 'Locked. In progress',
    'inventory.list.items_captured_label': 'captured',
    'inventory.list.unlock_button_label': 'UNLOCK',
    'inventory.list.force_unlock_button_label': 'FORCE UNLOCK',
    'inventory.list.unlock_alert.content':
        'Are you sure to unlock this inventory list?',
    'inventory.list.unlock_alert.cancel_action': 'CANCEL',
    'inventory.list.unlock_alert.confirm_action': 'UNLOCK',
    'inventory.list.force_unlock_alert.content':
        'You are about to unlock inventory list locked by {{locked-by}}. Your action will be logged. Are you sure?',
    'inventory.list.force_unlock_alert.cancel_action': 'CANCEL',
    'inventory.list.force_unlock_alert.confirm_action': 'UNLOCK',
    'inventory.list.unlock_menu_item_label': 'Unlock',
    'inventory.list.approve_menu_item_label': 'Approve',
    'inventory.list.unlocked_successfully':
        'Inventory list unlocked successfully!',
    'inventory.list.approve_button_label': 'APPROVE',
    'inventory.list.approved_successfully':
        'Inventory list approved successfully!',
    'inventory.list.already_locked_by_label__named':
        'Inventory list already locked by {{locked-by}}',
    'inventory.list.offline_label': 'OFFLINE',
    'inventory.list.add_item_button_label': 'Add warehouse item',
    'inventory.list.view_qr_code_pdf_button_label': 'View QR-code PDF',
    'inventory.list.view_list_intake_report_button_label':
        'View list intake report',
    'inventory.list.view_list_difference_report_button_label':
        'View list difference report',
    'inventory.list.booking_voucher_button_label': 'Booking voucher',
    'inventory.list.closed_offline_message':
        'Closed list can be viewed only while online',
    'inventory.list.item_not_found_by_ean.title': 'Product not found in list',
    'inventory.list.item_not_found_by_ean.message':
        'You can assign this EAN to the product in a list or add a new warehouse item',
    'inventory.list.item_not_found_by_ean.scan_again': 'Scan again',
    'inventory.list.item_not_found_by_ean.cancel_scanning': 'Cancel scanning',

    // Bookings
    'bookings.filter.any': 'Any',
    'bookings.filter.only_this_cost_center': 'Only this cost center',
    'bookings.filter.only_confirmed': 'Only confirmed',
    'bookings.filter.only_unconfirmed': 'Only unconfirmed',
    'bookings.search.placeholder': 'Search',
    'bookings.qty': 'Quantity',
    'bookings.search_available_only_online':
        'Bookings search available only online',
    'bookings.approval_request.title': 'Booking approval request',
    'bookings.approval_request.no_approvers_found': 'No approvers found',
    'bookings.approval_request.approver': 'Approver',

    'bookings.booking.status.confirmed': 'CONFIRMED',
    'bookings.booking.status.unconfirmed': 'UNCONFIRMED',
    'bookings.booking.status.archived': 'ARCHIVED',
    'bookings.booking.status.declined': 'DECLINED',

    'bookings.booking.details.title': 'Details',
    'bookings.booking.details.button_label': 'Details',
    'bookings.booking.details.id': 'Id',
    'bookings.booking.details.transfer_type': 'Transfer type',
    'bookings.booking.details.booking_reason': 'Booking reason',
    'bookings.booking.details.booking_date': 'Booking date',
    'bookings.booking.details.source_store': 'Source store',
    'bookings.booking.details.target_store': 'Target store',
    'bookings.booking.details.reference_title': 'Edit booking reference',
    'bookings.booking.details.reference_label': 'Reference',
    'bookings.booking.details.reference_edit.cancel_button_label': 'CANCEL',
    'bookings.booking.details.reference_edit.update_button_label': 'UPDATE',
    'bookings.booking.details.booking_updated_successfully':
        'Booking updated successfully!',
    'bookings.booking.details.booking_reason_is_not_specified':
        'Booking reason is not specified',
    'bookings.booking.details.snack_bar_button_label': 'DETAILS',
    'bookings.booking.details.recipe_transfer_label': 'Recipe transfer',
    'bookings.booking.book_confirmation_dialog': 'DETAILS',
    'bookings.booking.book_alert.content': 'Are you sure to book?',
    'bookings.booking.book_alert.no_action': 'CANCEL',
    'bookings.booking.book_alert.yes_action': 'YES',
    'bookings.booking.booking_booked_successfully':
        'Booking booked successfully!',
    'bookings.booking.voucher_button_label': 'View voucher',
    'bookings.booking.view_order_pdf_button_label': 'View order PDF',
    'bookings.booking_reason_lookup.search_available_only_online':
        'Search available only online',
    'bookings.booking_reason_lookup.title': 'Reasons',
    'bookings.booking_reason_lookup.select_button_label': 'SELECT',
    'bookings.reason_lookup.search_available_only_online':
        'Booking reasons search is possible only online',

    // Bookings manage
    'bookings.manage.filter.status_label': 'Status',
    'bookings.manage.filter.status.any': 'Any',
    'bookings.manage.filter.status.ready_to_book': 'Ready to book',
    'bookings.manage.filter.status.confirmed': 'Confirmed',
    'bookings.manage.filter.status.unconfirmed': 'Unconfirmed',
    'bookings.manage.filter.status.not_approved': 'Not approved',
    'bookings.manage.filter.status.pending': 'Pending',
    'bookings.manage.filter.status.empty': 'Empty',
    'bookings.manage.filter.status.delivered': 'Delivered',
    'bookings.manage.filter.status.booked': 'Booked',
    'bookings.manage.filter.status.archived': 'Archived',
    'bookings.manage.filter.status.declined': 'Declined',
    'bookings.manage.filter.cost_center_label': 'Cost center',
    'bookings.manage.filter.cost_center.any': 'Any',
    'bookings.manage.filter.cost_center.only_this': 'Only this',
    'bookings.manage.filter.transfer_type_label': 'Transfer type',
    'bookings.manage.filter.transfer_type.any': 'Any',
    'bookings.manage.filter.transfer_type.out_not_system': 'out not system',
    'bookings.manage.filter.transfer_type.out_order_inhouse':
        'out order inhouse',
    'bookings.manage.filter.transfer_type.out_order_inter_property':
        'out order inter property',
    'bookings.manage.filter.transfer_type.stock_correction': 'stock_correction',
    'bookings.manage.filter.transfer_type.stock_transfer': 'stock transfer',
    'bookings.manage.filter.transfer_type.stock_out_recipe': 'stock out recipe',
    'bookings.manage.filter.transfer_type.stk_ip_transfer': 'STK IP Transfer',
    'bookings.manage.filter.sorting.by_name_asc': 'A-Z',
    'bookings.manage.filter.sorting.by_name_desc': 'Z-A',
    'bookings.manage.filter.sorting.by_transfer_date_asc': 'Transfer date A-Z',
    'bookings.manage.filter.sorting.by_transfer_date_desc': 'Transfer date Z-A',
    'bookings.manage.filter.sorting.by_order_date_asc': 'Order date A-Z',
    'bookings.manage.filter.sorting.by_order_date_desc': 'Order date Z-A',
    'bookings.manage.filter.status.date_range': 'Date range',
    'bookings.manage.booking.declined': 'Declined',
    'bookings.manage.booking.declined_at': 'Declined at: {{declined_at}}',
    'bookings.manage.booking.inter_property.label': 'Inter-property',
    'bookings.manage.booking.st_ip_transfer.label': 'STK_IP_Transfer',
    'bookings.manage.booking.inter_property.number_of_hotels_confirmed':
        '{{confirmed_count}} of {{total_count}} hotels confirmed their bookings',
    'bookings.manage.booking_delete_alert.content':
        'Are you sure to delete this booking?',
    'bookings.manage.booking_cannot_be_deleted': 'Booking can not be deleted!',
    'bookings.manage.booking_cannot_be_updated': 'Booking can not be updated!',
    'bookings.manage.booking_delete_alert.no_action': 'CANCEL',
    'bookings.manage.booking_delete_alert.yes_action': 'YES',
    'bookings.manage.booking_deleted_successfully':
        'Booking deleted successfully!',
    'bookings.manage.booking_will_be_consumed': 'Booking will be consumed',

    'bookings.manage.products.filter.status_label': 'Status',
    'bookings.manage.products.filter.status.all': 'ALL',
    'bookings.manage.products.filter.status.complete': 'COMPLETE',
    'bookings.manage.products.filter.status.incomplete': 'INCOMPLETE',
    'bookings.manage.products.product_added_successfully':
        'Warehouse item added successfully to the booking',
    'bookings.manage.products.product_deleted_successfully':
        'Product deleted successfully!',
    'bookings.manage.products.search_available_only_online':
        'Booking manage available only online',
    'bookings.manage.products.product_delete_button.label': 'Delete',
    'bookings.manage.products.product_delete_alert.content':
        'Are you sure to delete this booking?',
    'bookings.manage.products.product_delete_alert.no_action': 'CANCEL',
    'bookings.manage.products.product_delete_alert.yes_action': 'YES',

    'bookings.manage.product.title': 'Details',
    'bookings.manage.product.updated_successfully': 'Updated successfully!',
    'bookings.manage.product.deleted_successfully': 'Deleted successfully!',
    'bookings.manage.product.stock': 'Stock',
    'bookings.manage.product.price_per_inventory_unit': 'Price/inventory unit',
    'bookings.manage.product.details_button_label': 'Details',
    'bookings.manage.product.edit_button_label': 'EDIT',
    'bookings.manage.product.barcode': 'Barcode',
    'bookings.manage.product.source_division': 'Source division',
    'bookings.manage.product.target_division': 'Target division',
    'bookings.manage.product.source_store': 'Source store',
    'bookings.manage.product.target_store': 'Target store',
    'bookings.manage.product.source_cost_type': 'Source cost type',
    'bookings.manage.product.target_cost_type': 'Target cost type',
    'bookings.manage.product.source_cost_center': 'Source cost center',
    'bookings.manage.product.target_cost_center': 'Target cost center',
    'bookings.manage.product.quantity_field_label': 'Quantity',
    'bookings.manage.product.quantity_update_button_label': 'UPDATE',
    'bookings.manage.product.confirm_button_label': 'CONFIRM',
    'bookings.manage.product.confirmed_label': 'CONFIRMED',

    'bookings.booking.error.booking_date_is_not_valid':
        'Booking date is not valid',
    'bookings.booking.error.booking_date_in_the_future':
        'Booking date in the future',
    'bookings.booking.error.booking_date_after_open_inventory':
        'Booking date after open inventory',
    'bookings.booking.error.booking_date_before_closed_inventory':
        'Booking date before closed inventory',
    'bookings.booking.error.not_approved': 'Not approved',
    'bookings.booking.error.declined': 'Declined',
    'bookings.booking.error.has_unconfirmed_records': 'Has unconfirmed records',
    'bookings.booking.error.not_approved_by_target': 'Not approved by target',
    'bookings.booking.error.declined_by_target': 'Declined by target',
    'bookings.booking.error.booking_reason_is_missing':
        'Booking reason is missing',

    'bookings.booking.book_result.title': 'Booking result',
    'bookings.booking.book_result.booking_id': 'Booking result',
    'bookings.booking.book_result.ok_status_label': 'OK',
    'bookings.booking.book_result.error_status_label': 'ERROR',
    'bookings.booking.book_result.hades_export_error_label':
        'HADES export error',

    'bookings.booking.item.can_not_be_updated': 'Item can not be updated',
    'bookings.booking.item.can_not_be_confirmed': 'Item can not be confirmed',
    'bookings.booking.item.show_details_button_label': 'DETAILS',
    'bookings.booking.item.errors.zero_qty_not_allowed':
        'Zero quantity is not allowed',
    'bookings.booking.item.errors.low_stock_qty': 'Low stock quantity',
    'bookings.booking.item.errors.qty_is_not_set': 'Quantity is not set',
    'bookings.booking.item.errors.src_cost_center_id_is_not_set':
        'Source cost center is not set',
    'bookings.booking.item.errors.src_store_id_is_not_set':
        'Source store is not set',
    'bookings.booking.item.errors.src_cost_type_id_is_not_set':
        'Source cost type is not set',
    'bookings.booking.item.errors.src_warehouse_id_is_not_set':
        'Source warehouse is not set',
    'bookings.booking.item.errors.tar_cost_center_id_is_not_set':
        'Target cost center is not set',
    'bookings.booking.item.errors.tar_division_id_is_not_set':
        'Target division is not set',
    'bookings.booking.item.errors.tar_division_item_is_not_mapped':
        'Target division item is not mapped',
    'bookings.booking.item.errors.tar_store_id_is_not_set':
        'Target store is not set',
    'bookings.booking.item.errors.tar_cost_type_id_is_not_set':
        'Target cost type is not set',
    'bookings.booking.item.errors.tar_warehouse_id_is_not_set':
        'Target warehouse is not set',
    'bookings.booking.item.errors.price_is_not_set': 'Price is not set',
    'bookings.booking.item.errors.inventory_unit_is_not_set':
        'Inventory unit is not set',

    'bookings.booking.item.details.select_source_cost_center_first_label':
        'Please select source cost center first',
    'bookings.booking.item.details.select_target_cost_center_first_label':
        'Please select target cost center first',
    'bookings.booking.item.details.select_target_division_first_label':
        'Please, select target division first',

    'bookings.create.title': 'Create booking',
    'bookings.create.transaction_type_reason_and_date_are_required':
        'Transaction type, reason and date are required',
    'bookings.create.create_button_label': 'CREATE',
    'bookings.create.transaction_type_label': 'Transaction type',
    'bookings.create.transaction_type_not_selected':
        'Please select transaction type first',
    'bookings.create.reason_label': 'Booking reason',
    'bookings.create.date_label': 'Booking date',
    'bookings.create.reference_label': 'Reference',
    'booking.create_button_label': 'Create booking',

    'bookings.book.book_button_label': 'BOOK',
    'bookings.book.title': 'Book',
    'bookings.book.delivery_date_label': 'Delivery date',
    'bookings.book.delivery_date_is_empty_error': 'Please select delivery date',
    'bookings.book.note_label': 'Note',
    'bookings.book.note_is_empty_error': 'Please type note',
    'bookings.book.booked_successfully_label': 'Book successfully!',
    'bookings.book.view_booking_log_button_label': 'LOG',

    // Process Receivings
    'process_receivings.in_progress_status': 'In Approval',
    'process_receivings.approved_status': 'Approved',
    'process_receivings.declined_status': 'Declined',
    'process_receivings.can_be_booked_status': 'Can be booked',
    'process_receivings.processing_status': 'Processing',
    'process_receivings.orders.title': 'Orders',
    'process_receivings.non_po_receivings.title': 'Non-Po Receivings',
    'process_receivings.provisional_booking.title':
        'Provisional Booking ({{count}})',
    'process_receivings.create_provisional_booking_button_label':
        'Book to Provisional',
    'process_receivings.orders.booking_date': 'Booking Date',
    'process_receivings.orders.provisional_booking_date':
        'Provisional Booking Date',
    'process_receivings.orders.delivery_date': 'Delivery Date',
    'process_receivings.orders.total': 'Total',
    'process_receivings.orders.create_button': 'Create',
    'process_receivings.orders.view_pdf_button': 'View PDF',
    'process_receivings.orders.view_pdf.close_button': 'Close',
    'receivings.non_po_receivings.filter.status_label': 'Status',
    'process_receivings.non_po.filter.sorting.by_creation_date_asc':
        'Creation date (oldest first)',
    'process_receivings.non_po.filter.sorting.by_creation_date_desc':
        'Creation date (newest first)',
    'receivings.non_po_receivings.filter.status.all': 'All',
    'receivings.non_po_receivings.filter.status.in_approval': 'In Approval',
    'receivings.non_po_receivings.filter.status.not_in_approval':
        'Not in Approval',
    'process_receivings.details.title': 'Receiving details',
    'process_receivings.details.details_tab_title': 'Details',
    'process_receivings.details.order_items_tab_title': 'Delivered items',
    'process_receivings.details.delivery_tab_title': 'Delivery',
    'process_receivings.details.attachments_tab_title': 'Attachments',
    'process_receivings.order.request_approval.button_label':
        'Request approval',

    'process_receivings.details.errors_label': 'Errors',
    'process_receivings.details.warnings_label': 'Warnings',
    'process_receivings.details.order_info': 'Order info',
    'process_receivings.details.total_value': 'Total value of order',
    'process_receivings.details.checksum': 'Checksum',
    'process_receivings.details.split_delivery': 'Split delivery sum',
    'process_receivings.details.amount': 'Amount',
    'process_receivings.details.total_vat_amount': 'VAT/GST',
    'process_receivings.details.total_tax': 'Total Tax',
    'process_receivings.details.total_amount': 'Total amount',
    'process_receivings.details.summary': 'Summary',
    'process_receivings.details.net_amount': 'Net amount',
    'process_receivings.details.vat_amount': 'VAT amount',
    'process_receivings.details.gross_amount': 'Gross amount',
    'process_receivings.view_pdf.subtitle': 'Receiving',
    'process_receivings.details.discount_total_updated_successfully':
        'Total discount was updated',
    'process_receivings.details.edit_discount.discount_label': 'Discount rate',
    'process_receivings.details.edit_discount.update_button': 'Update',
    'process_receivings.details.exchange_rate': 'Exchange rate',
    'process_receivings.details.exchange_rate_date': 'Exchange rate date',
    'process_receivings.details.errors_hint':
        'Please check the items indicated with a red dot • which need your attention.',
    'process_receivings.order_items.offline':
        'You can view order items only while online',
    'process_receivings.order_items.empty': 'No Delivered Articles',
    'process_receivings.order_items.quantity': 'Qty: {{quantity}}',
    'process_receivings.order_items.discount': 'Discount: {{discount}}',
    'process_receivings.details.delivery.delivery_note_data.title':
        'Delivery note data',
    'process_receivings.details.delivery.delivery_note_data.description':
        'Delivery note need to be completed',
    'process_receivings.details.delivery.related_orders.title':
        'Related orders',
    'process_receivings.details.delivery.related_orders.description':
        'Manage orders which came together',
    'process_receivings.details.delivery.deposit_items.title': 'Deposit items',
    'process_receivings.details.delivery.deposit_items.description':
        'Manage items to return with delivery',
    'process_receivings.details.delivery.additional_delivery.title':
        'Additional delivery',
    'process_receivings.details.delivery.additional_delivery.description':
        'Manage not delivered items',
    'process_receivings.details.delivery.cost_centers.title':
        'Breakdown by cost centers',
    'process_receivings.details.delivery.cost_centers.description':
        'Target cost centers',
    'process_receivings.details.delivery.const_centers_breakdown.title':
        'Breakdown by cost center',
    'process_receivings.details.delivery.partial_history.title':
        'History of Partial Receiving',
    'process_receivings.details.delivery.partial_history.description':
        'View history of Partial Receiving',
    'process_receivings.details.order_items.delete_alert.content':
        'Delete delivered Article?',
    'process_receivings.details.order_items.delete_alert.no_action': 'Cancel',
    'process_receivings.details.order_items.delete_alert.yes_action': 'Delete',
    'process_receivings.details.order_items.deleted_successfully':
        'The product has been removed. {{name}}',
    'process_receivings.delivery_note_tax_invoice_number':
        'Delivery note no./ tax invoice no.',
    'process_receivings.provisional_bookings.filter.sorting.by_order_date_asc':
        'Order date (oldest first)',
    'process_receivings.provisional_bookings.filter.sorting.by_order_date_desc':
        'Order date (newest first)',
    'process_receivings.provisional_bookings.filter.sorting.by_delivery_date_asc':
        'Delivery date (oldest first)',
    'process_receivings.provisional_bookings.filter.sorting.by_delivery_date_desc':
        'Delivery date (newest first)',
    'process_receivings.provisional_bookings.filter.status_label': 'Status',
    'process_receivings.provisional_bookings.filter.status.active': 'Active',
    'process_receivings.provisional_bookings.filter.status.processing':
        'Processing',
    'process_receivings.provisional_bookings.filter.status.processed':
        'Processed',
    'process_receivings.provisional_bookings.filter.status.can_be_booked':
        'Can be booked',
    'process_receivings.provisional_bookings.filter.status.error': 'Error',
    'process_receivings.provisional_bookings.filter.supplier_name.label':
        'Supplier',
    'process_receivings.provisional_bookings.filter.supplier_name.hint_text':
        'Supplier name',
    'process_receivings.provisional_bookings.filter.product_name.label':
        'Product',
    'process_receivings.provisional_bookings.filter.product_name.hint_text':
        'Product name',
    'process_receivings.provisional_bookings.filter.user_name.label': 'User',
    'process_receivings.provisional_bookings.filter.user_name.hint_text':
        'User name',
    'process_receivings.provisional_bookings.filter.sent_cost_center_name.label':
        'Cost Center - Order Sent By',
    'process_receivings.provisional_bookings.filter.sent_cost_center_name.hint_text':
        'Cost Center name',
    'process_receivings.provisional_bookings.filter.requested_cost_center_name.label':
        'Cost Center - Order Requested By',
    'process_receivings.provisional_bookings.filter.requested_cost_center_name.hint_text':
        'Cost Center name',
    'process_receivings.provisional_bookings.filter.order_date_range':
        'Order date range',
    'process_receivings.provisional_bookings.search_available_only_online':
        'Provisional bookings search available only online',
    'process_receivings.provisional_bookings.search_no_result':
        'No Provisional Bookings',
    'process_receivings.provisional_bookings.open_inventory_warning':
        'Inventories Open: Booking Currently Unavailable.',
    'process_receivings.provisional_bookings.processing_warning':
        'New provisional bookings can not be booked until the previous one is processed.',
    'process_receivings.provisional_bookings.can_be_booked_message':
        'Provisional Bookings can be Booked. Inventory Closed.',
    'process_receivings.provisional_bookings.booking_in_web_title':
        'Can be booked, only at WEB version.',
    'process_receivings.provisional_bookings.booking_in_web_body':
        'Transactions / Process receivings / Provisional Bookings',

    'process_receivings.freight_cost.title': 'Freight and Import cost',

    'process_receivings.freight_cost.main_label': 'Freight cost',
    'process_receivings.freight_cost.supplier_label':
        'Supplier the other charges',
    'process_receivings.freight_cost.custom_duty_label': 'Custom Duty',
    'process_receivings.freight_cost.handling_duty_label':
        'Handling charges Duty',

    'process_receivings.freight_cost.exchange_rate': 'Exchange Rate',
    'process_receivings.freight_cost.supplier': 'Supplier',
    'process_receivings.freight_cost.amount': 'Amount',
    'process_receivings.freight_cost.currency': 'Currency',
    'process_receivings.freight_cost.exchange': 'Exchange',
    'process_receivings.freight_cost.supplier_currency': 'Supplier Currency',

    'process_receivings.freight_cost.total.title':
        'Total Freight and Import cost',
    'process_receivings.freight_cost.total.items': 'Total Items',
    'process_receivings.freight_cost.total.deposit_items':
        'Total Deposit Items',

    'process_receivings.freight_cost.total.discounts':
        'Total Cash Discount / Other Discounts',
    'process_receivings.freight_cost.total.freight_and_import_cost':
        'Total Freight and Import cost',
    'process_receivings.freight_cost.total.delivery_note':
        'Total Delivery Note (Net)',
    'process_receivings.freight_cost.total.tax': 'Total Tax',
    'process_receivings.freight_cost.total.gross': 'Total Gross',
    'process_receivings.freight_cost.total.save_button': 'Save',
    'process_receivings.freight_cost.total.apply_button': 'Apply',
    'process_receivings.freight_cost.total.save_successful':
        'Freight costs saved successfully!',

    'process_receivings.details.error.booking_date_is_not_valid':
        'Booking date is not valid',
    'process_receivings.details.error.booking_date_in_the_future':
        'Booking date is in the future',
    'process_receivings.details.error.delivery_note_date_is_not_valid':
        'Delivery note date is not valid',
    'process_receivings.details.error.delivery_note_date_in_the_future':
        'Delivery note date is in the future',
    'process_receivings.details.error.delivery_note_date_in_the_past':
        'Delivery note date is in the past',
    'process_receivings.details.error.booking_date_after_open_inventory':
        'Booking date after open inventory',
    'process_receivings.details.error.booking_date_before_closed_inventory':
        'Booking date before closed inventory',
    'process_receivings.details.error.checksum_error': 'Checksum error',
    'process_receivings.details.error.delivery_note_is_missing':
        'Delivery note is missing',
    'process_receivings.details.error.delivery_note_is_empty':
        'Delivery note is empty',
    'process_receivings.details.error.one_or_more_order_items_incomplete':
        'One or more order items incomplete',
    'process_receivings.details.error.deposit_item_error': 'Deposit item error',
    'process_receivings.details.error.cost_center_is_not_set':
        'Cost center is not set',
    'process_receivings.details.error.delivery_note_already_assigned':
        'Delivery note already assigned',
    'process_receivings.details.error.invoice_assigned_but_delivery_note_is_not_invoice':
        'Invoice assigned but delivery note is not invoice',
    'process_receivings.details.error.currency_exchange_rate_error':
        'Currency exchange rate error',
    'process_receivings.details.error.previous_inventory_opened':
        'Previous inventory opened',
    'process_receivings.details.error.inventory_was_opened_same_day':
        'Inventory was opened same day',
    'process_receivings.details.error.provisional_booking_exists':
        'Provisional booking already exist',
    'process_receivings.details.error.provisional_booking_after_open_inventory':
        'Inventories Open. Booking Currently unavailable',
    'process_receivings.details.error.item_stock_below_zero_error':
        'One of the items has negative stock amount.',

    'process_receivings.details.warning.only_provisional_booking_is_applicable':
        'Opened Inventory. Can be booked only to Provisional Bookings.',

    'process_receiving.disabled_store': 'XXXX - Store',

    'process_receiving.order_item.editing_not_allowed':
        'Editing is not allowed for your role',
    'process_receiving.order_item.error.item_qty_is_missing':
        'Item qty is missing',
    'process_receiving.order_item.error.item_price_is_missing':
        'Item price is missing',
    'process_receiving.order_item.error.vat_is_missing': 'Vat is missing',
    'process_receiving.order_item.error.inventory_unit_is_missing':
        'Inventory unit is missing',
    'process_receiving.order_item.error.inventory_factor_is_missing':
        'Inventory factor is missing',
    'process_receiving.order_item.error.content_units_per_order_unit_is_missing':
        'Content units per order unit is missing',
    'process_receiving.order_item.error.order_unit_is_missing':
        'Order unit is missing',
    'process_receiving.order_item.error.content_unit_is_missing':
        'Content unit is missing',
    'process_receiving.order_item.error.split_delivery_missing_cost_center':
        'Split delivery missing cost center',
    'process_receiving.order_item.error.split_delivery_missing_store':
        'Split delivery missing store',
    'process_receiving.order_item.error.split_delivery_missing_cost_type':
        'Split delivery missing cost type',
    'process_receiving.order_item.error.item_stock_below_zero_error':
        'Item stock below zero',
    'process_receiving.order_item.error.item_archived': 'Item archived',

    'process_receiving.order_item.warning.receiving_variance':
        'Receiving variance',
    'process_receiving.order_item.warning.price_does_not_match':
        "Price doesn't match",
    'process_receiving.order_item.warning.cost_center_does_not_match':
        "Cost center doesn't match",

    // Receivings
    'receivings.title': 'Receivings',
    'receivings.search_available_only_online':
        'Receivings search available only online',
    'receivings.filter.date_range': 'Date range',
    'receivings.filter.order_date': 'Order date',
    'receivings.filter.supplier_name.label': 'Supplier',
    'receivings.filter.supplier_name.hint_text': 'Supplier name',
    'receivings.filter.product_name.label': 'Product',
    'receivings.filter.product_name.hint_text': 'Product name',
    'receivings.filter.user_name.label': 'User',
    'receivings.filter.user_name.hint_text': 'User name',
    'receivings.filter.sent_cost_center_name.label':
        'Cost Center - Order Sent By',
    'receivings.filter.sent_cost_center_name.hint_text': 'Cost Center name',
    'receivings.filter.requested_cost_center_name.label':
        'Cost Center - Order Requested By',
    'receivings.filter.requested_cost_center_name.hint_text':
        'Cost Center name',

    'receivings.receiving_delete_success_message':
        'Receiving was deleted successfully',
    'receivings.receiving_cant_be_deleted_message':
        'This receiving can not be deleted',
    'receivings.receiving_delete_confirmation.title': 'Delete Receiving?',

    'receivings.astore.order_merge_needed':
        '{{count}} AStore Order. Merge Articles Needed.',
    'receivings.astore.orders_merge_needed':
        '{{count}} AStore Orders. Merge Articles Needed.',

    'receivings.orders.filter.status_label': 'Status',
    'receivings.orders.title': 'Order receivings',
    'receivings.orders.search_available_only_online':
        'Orders search available only online',
    'receivings.orders.search_no_result':
        "No results found. Try to use filters if search query doesn't work for you.",
    'receivings.orders.filter.sorting.by_order_date_asc':
        'Order date, Oldest first',
    'receivings.orders.filter.sorting.by_order_date_desc':
        'Order date, Latest first',
    'receivings.orders.filter.sorting.by_delivery_date_asc':
        'Delivery date, Oldest first',
    'receivings.orders.filter.sorting.by_delivery_date_desc':
        'Delivery date, Latest first',
    'receivings.orders.filter.status.all': 'All',
    'receivings.orders.filter.status.today': 'Today',
    'receivings.orders.filter.status.in_progress': 'In progress',
    'receivings.orders.filter.status.pending': 'Pending',
    'receivings.orders.filter.order_date': 'Order date',
    'receivings.orders.filter.delivery_date': 'Delivery date',
    'receivings.orders.filter.order_id.label': 'Order',
    'receivings.orders.filter.order_id.hint_text': 'Order ID',
    'receivings.orders.filter.supplier_name.label': 'Supplier',
    'receivings.orders.filter.supplier_name.hint_text': 'Supplier name',
    'receivings.orders.filter.product_name.label': 'Product',
    'receivings.orders.filter.product_name.hint_text': 'Product name',
    'receivings.orders.filter.user_name.label': 'User',
    'receivings.orders.filter.user_name.hint_text': 'User name',
    'receivings.orders.filter.sent_cost_center_name.label':
        'Cost Center - Order Sent By',
    'receivings.orders.filter.sent_cost_center_name.hint_text':
        'Cost Center name',
    'receivings.orders.filter.requested_cost_center_name.label':
        'Cost Center - Order Requested By',
    'receivings.orders.filter.requested_cost_center_name.hint_text':
        'Cost Center name',

    'receivings.order_item.inventory_units_lookup.title': 'Inventory Unit',

    'receivings.order.start_receiving_process_alert.content':
        'Start receiving process?',
    'receivings.order.start_receiving_process_alert.no_action': 'CANCEL',
    'receivings.order.start_receiving_process_alert.yes_action': 'YES',

    'receivings.order.in_progress_label': 'IN PROGRESS',
    'receivings.order.view_pdf_button.label': 'VIEW PDF',

    'receivings.order.delivery_details.title': 'Delivery details',
    'receivings.order.delivery_details.read_only':
        "This receiving can't be updated",
    'receivings.order.delivery_details.cost_center': 'Cost center',
    'receivings.order.delivery_details.delivery_note_total':
        'Delivery note total',
    'receivings.order.delivery_details.delivery_note_total_update_button_label':
        'UPDATE',
    'receivings.order.delivery_details.delivery_note_number':
        'Delivery note number',
    'receivings.order.delivery_details.delivery_note_is_invoice':
        'Delivery note is invoice',
    'receivings.order.delivery_details.delivery_note_date':
        'Delivery note date',
    'receivings.order.delivery_details.booking_date_label': 'Booking date',
    'receivings.order.delivery_details.checksum':
        'Checksum: Total Delivery note',
    'receivings.order.delivery_details.date_dialog.ok': 'Ok',
    'receivings.order.delivery_details.date_dialog.cancel': 'Cancel',
    'receivings.order.delivery_details.date_dialog.select_date': 'Select date',
    'receivings.order.delivery_details.provisional_booking_date':
        'Provisional booking date',
    'receivings.order.delivery_details.total_delivery_note':
        'Total delivery note',
    'receivings.order.delivery_details.cost_center_updated_successfully':
        'Cost center updated successfully!',
    'receivings.order.delivery_details.delivery_note_id_updated_successfully':
        'Delivery note id updated successfully!',
    'receivings.order.delivery_details.delivery_note_is_invoice_updated_successfully':
        'Delivery note is invoice updated successfully!',
    'receivings.order.delivery_details.delivery_note_date_updated_successfully':
        'Delivery note date updated successfully!',
    'receivings.order.delivery_details.booking_date_updated_successfully':
        'Booking date updated successfully!',
    'receivings.order.delivery_details.delivery_note_total_updated_successfully':
        'Delivery note total updated successfully!',
    'receivings.order.confirm_alert.content':
        'Are you sure to confirm this receiving?',
    'receivings.order.confirm_alert.no_action': 'CANCEL',
    'receivings.order.confirm_alert.yes_action': 'YES',
    'receivings.order.confirmed_successfully':
        'Receiving confirmed successfully!',
    'receivings.order.provisional_book_alert.title':
        'Are you sure you want to book to Provisional Booking?',
    'receivings.order.provisional_book_alert.content':
        'Provisional Bookings can be managed from web app.',
    'receivings.order.provisional_book_alert.book_action': 'Book',
    'receivings.order.provisional_book_alert.total_amount_label':
        'Total order amount',
    'receivings.order.provisional_book_alert.order_number_label':
        'Order number',
    'receivings.order.provisional_booking_successful':
        'Provisional booking created successfully!',

    'receivings.order.request_approval.title': 'Request approval',
    'receivings.order.request_approval.approver': 'Approver',
    'receivings.order.request_approval.total': 'Total',
    'receivings.order.request_approval.type': 'Type',
    'receivings.order.request_approval.cost_center': 'Cost Center',
    'receivings.order.request_approval.confirmation.title': 'Request Approval?',
    'receivings.order.request_approval.confirmation.yes_action': 'Send Request',
    'receivings.order.request_approval.requested_successfully':
        'Approval requested successfully!',

    'receivings.order_items.title': 'Order items',
    'receivings.order_items.total_amount': 'Total value of order',
    'receivings.order_items.number_of_items': 'Number of items',
    'receivings.order_items.add.item_from_order': 'Item from Order',
    'receivings.order_items.add.item_from_supplier': 'Item from Supplier',
    'receivings.order_items.add.item_from_inventory_management':
        'Item from Inventory Management',
    'receivings.order_items.filter.sorting.by_name_asc': 'Name A-Z',
    'receivings.order_items.filter.sorting.by_name_desc': 'Name Z-A',
    'receivings.order_items.filter.sorting.by_id_asc': 'Id A-Z',
    'receivings.order_items.filter.sorting.by_id_desc': 'Id Z-A',
    'receivings.order_items.item_added_successfully_label':
        'Item added successfully!',
    'receivings.order_items.items_added_successfully_label':
        'Added successfully!',
    'receivings.order_items.add_discount_button_label':
        'Update discount for all',
    'receivings.order_items.add_discount.discount_field_label': 'Discount',
    'receivings.order_items.add_discount.discount_value_description':
        'Percent value (%)',
    'receivings.order_items.add_discount.update_button_label': 'UPDATE',
    'receivings.order_items.discount_updated_successfully_label':
        'Updated successfully!',
    'receivings.order_items.delete_all_receiving_items_button_label':
        'Delete all receiving items',
    'receivings.order_items.add_all_receiving_items_button_label':
        'Add all receiving items',
    'receivings.order_items.delete_all_alert.title': 'Delete all Articles?',
    'receivings.order_items.delete_all_alert.content':
        'Attention! You are about to perform an irreversible action. If you are sure, proceed. If not, cancel the operation.',
    'receivings.order_items.deleted_all_successfully':
        'All articles are removed',

    'receivings.order_item.title': 'Order Item',
    'receivings.order_item.quantity': 'Quantity: {{quantity}}',
    'receivings.order_item.discount': 'Discount: {{discount}}',
    'receivings.order_item.measure_button_label': 'MEASURE',
    'receivings.order_item.check_tab_title': 'Check',
    'receivings.order_item.article_tab_title': 'Article',
    'receivings.order_item.split_tab_title': 'Split',
    'receivings.order_item.additional_tab_title': 'Additional',
    'receivings.order_item.store': 'Store',
    'receivings.order_item.cost_center': 'Cost center',
    'receivings.order_item.cost_type': 'Cost type',
    'receivings.order_item.tax_rate': 'Tax Rate',
    'receivings.order_item.add_comment_label': 'Add comment',
    'receivings.order_item.update_warning':
        'Please be aware that following fields can filled only once and cannot be changed later',
    'receivings.order_item.category': 'Category',
    'receivings.order_item.inventory_unit': 'Inventory unit',
    'receivings.order_item.inventory_unit_factor': 'Inventory unit factor',
    'receivings.order_item.unit_of_calculation': 'Unit of calculation',
    'receivings.order_item.unit_of_calculation_factor':
        'Unit of calculation factor',
    'receivings.order_item.quantity_update_button_label': 'UPDATE',
    'receivings.order_item.article_number': 'Article no.',
    'receivings.order_item.ean': 'EAN',
    'receivings.order_item.order_unit': 'Order Unit',
    'receivings.order_item.content_units': 'Content unit',
    'receivings.order_item.content_units_per_order_unit_number':
        'Number of content units per order unit',
    'receivings.order_item.price_per_order_unit': 'Price per order unit',
    'receivings.order_item.category_updated_successfully':
        'Category updated successfully!',
    'receivings.order_item.vat_updated_successfully':
        'Vat updated successfully!',
    'receivings.order_item.order_unit_updated_successfully':
        'Order unit updated successfully!',
    'receivings.order_item.content_unit_updated_successfully':
        'Content unit updated successfully!',
    'receivings.order_item.number_of_content_units_per_order_unit_updated_successfully':
        'Content units per order unit updated successfully!',
    'receivings.order_item.inventory_unit_updated_successfully':
        'Inventory unit updated successfully!',
    'receivings.order_item.inventory_units_per_order_unit_updated_successfully':
        'Inventory units per order unit updated successfully!',
    'receivings.order_item.calculation_unit_updated_successfully':
        'Calculation unit updated successfully!',
    'receivings.order_item.calculation_units_per_order_unit_updated_successfully':
        'Calculation units per order unit updated successfully!',
    'receivings.order_item.total_amount_updated_successfully':
        'Total amount updated successfully!',
    'receivings.order_item.discount_updated_successfully':
        'Discount updated successfully!',
    'receivings.order_item.item_updated_successfully':
        'Article updated successfully!',
    'receivings.order_item.weight_updated_successfully':
        'Weight updated successfully!',
    'receivings.order_item.article_temperature_updated_successfully':
        'Article temperature updated successfully!',
    'receivings.order_item.truck_temperature_updated_successfully':
        'Truck temperature updated successfully!',
    'receivings.order_item.expiration_date_updated_successfully':
        'Expiration date updated successfully!',
    'receivings.order_item.field_is_read_only': "This field can't be changed",

    'receivings.order_item.check.order_info_label': 'Order Info',
    'receivings.order_item.check.inventory_label': 'Inventory',
    'receivings.order_item.check.additional_label': 'Additional',
    'receivings.order_item.check.deposit_item_label': 'Deposit item',
    'receivings.order_item.check.inventory_unit': 'Inventory Unit',
    'receivings.order_item.check.price_per_order_unit': 'Price / Order Unit',
    'receivings.order_item.check.price_per_inventory_unit':
        'Price / Inventory Unit',
    'receivings.order_item.check.delivery_quantity': 'Delivery Quantity',
    'receivings.order_item.check.contents_label': 'Contents',
    'receivings.order_item.check.order_unit': 'Order unit',
    'receivings.order_item.check.content_unit': 'Content unit',
    'receivings.order_item.check.discount': 'Discount',
    'receivings.order_item.check.discount_value_description':
        'Percent value (%)',
    'receivings.order_item.check.netto_amount': 'Net amount',
    'receivings.order_item.check.total_amount': 'Total amount',
    'receivings.order_item.check.total_update_button_label': 'UPDATE',
    'receivings.order_item.check.order_quantity': 'Order quantity',

    'receivings.order_item.article.article_details': 'Article Details',
    'receivings.order_item.article.price_per_inventory_unit':
        'Price / Inventory Unit',

    'receivings.order_item.additional.additional.title': 'Additional',
    'receivings.order_item.additional.comment.title': 'Comment',
    'receivings.order_item.additional.attachment.title': 'Attachment',
    'receivings.order_item.additional.weight_label': 'Weight',
    'receivings.order_item.additional.article_temperature_label':
        'Article temperature',
    'receivings.order_item.additional.truck_temperature_label':
        'Truck temperature',
    'receivings.order_item.additional.expiration_date_label': 'Expiration date',
    'receivings.order_item.additional.comment.add_button_label': 'Add Comment',
    'receivings.order_item.additional.comment.edit_button_label': 'Edit',
    'receivings.order_item.additional.comment.delete_button_label': 'Delete',
    'receivings.order_item.additional.comment.field_label': 'Comment',
    'receivings.order_item.additional.comment.add_title': 'Add comment',
    'receivings.order_item.additional.comment.edit_title': 'Edit comment',
    'receivings.order_item.additional.comment.edited_successfully':
        'Comment updated successfully!',
    'receivings.order_item.additional.comment.added_successfully':
        'Comment added successfully!',
    'receivings.order_item.additional.comment.deleted_successfully':
        'Comment deleted successfully!',
    'receivings.order_item.additional.comment.delete_alert.title':
        'Are you sure want to delete the comment?',

    'receivings.order_item.files.file_added_successfully':
        'File added successfully!',
    'receivings.order_item.files.file_deleted_successfully':
        'File deleted successfully!',
    'receivings.order_item.files.file_delete_confirmation.title':
        'Are you sure want to delete this attachment?',
    'receivings.order_item.files.file_delete_confirmation.file_name':
        'Attachment',
    'receivings.order_item.files.file_delete_confirmation.item_name': 'Article',
    'receivings.order_item.files.file_name_title': 'Attachment name',
    'receivings.order_item.files.file_name_field_label': 'Name',
    'receivings.order_item.files.add_button_label': 'Add',
    'receivings.order_item.files.delete_button_label': 'Delete',

    'receivings.order_item.split_delivery.split_delivery_item_title':
        'Split Delivery Item',
    'receivings.order_item.split_delivery.split_delivery_items_title':
        'Split Delivery Items',
    'receivings.order_item.split_delivery.no_items': 'No items available',
    'receivings.order_item.split_delivery.add_dialog.title':
        'Are you sure to add split delivery?',
    'receivings.order_item.split_delivery.add_dialog.no_action': 'Cancel',
    'receivings.order_item.split_delivery.add_dialog.yes_action': 'Yes',
    'receivings.order_item.split_delivery.remove': 'Remove',
    'receivings.order_item.split_delivery.delete_dialog.title':
        'Are you sure to delete this split delivery?',
    'receivings.order_item.split_delivery.delete_dialog.no_action': 'Cancel',
    'receivings.order_item.split_delivery.delete_dialog.yes_action': 'Yes',
    'receivings.order_item.split_delivery.quantity': 'Quantity',
    'receivings.order_item.split_delivery.quantity_update_button': 'Update',
    'receivings.order_item.split_delivery.cost_center_updated_successfully':
        'Split delivery cost center updated successfully!',
    'receivings.order_item.split_delivery.store_updated_successfully':
        'Split delivery store updated successfully!',
    'receivings.order_item.split_delivery.cost_type_updated_successfully':
        'Split delivery cost type updated successfully!',
    'receivings.order_item.split_delivery.qty_updated_successfully':
        'Split delivery quantity updated successfully!',
    'receivings.order_item.split_delivery.deleted_successfully':
        'Split delivery deleted successfully!',
    'receivings.order_item.split_delivery.added_successfully':
        'Split delivery added successfully!',
    'receivings.order_item.split_delivery.select_cost_center_first':
        'Please select cost center first',

    'receivings.deposit_items.title': 'Deposit items',
    'receivings.deposit_items.available_only_online':
        'Deposit items available only online',
    'receivings.deposit_items.no_items': 'No Deposit Items',
    'receivings.deposit_items.missing_info_alert':
        'Missing info at Article Master Data',
    'receivings.deposit_items.delete_item_alert.content': 'Delete Item?',
    'receivings.deposit_items.delete_item_alert.no_action': 'Cancel',
    'receivings.deposit_items.delete_item_alert.yes_action': 'Delete',
    'receivings.deposit_items.item_deleted_successfully_label':
        'Item deleted successfully!',
    'receivings.deposit_items.item_added_successfully_label':
        'Item added successfully!',
    'receivings.deposit_items.item_price_updated_successfully_label':
        'Item price updated successfully!',
    'receivings.deposit_items.item_received_qty_updated_successfully_label':
        'Item received quantity updated successfully!',
    'receivings.deposit_items.item_returned_qty_updated_successfully_label':
        'Item returned quantity updated successfully!',

    'receivings.deposit_items.item.delete_item.button_label': 'Delete',
    'receivings.deposit_items.item.price': 'Price',
    'receivings.deposit_items.item.price_update_button_label': 'UPDATE',
    'receivings.deposit_items.item.received_quantity': 'Received quantity',
    'receivings.deposit_items.item.returned_quantity': 'Returned quantity',
    'receivings.deposit_items.item.order_value': 'Order value',
    'receivings.deposit_items.item.details_menu_button_label': 'Details',
    'receivings.deposit_items.item.details.title': 'Details',
    'receivings.deposit_items.item.details.save_button_label': 'SAVE',
    'receivings.deposit_items.item.details.stock': 'Stock',
    'receivings.deposit_items.item.details.receiving_cost_type':
        'Receiving cost type',
    'receivings.deposit_items.item.details.vat': 'Vat',
    'receivings.deposit_items.item.details.saved_successfully_label':
        'Deposit item details saved successfully!',

    'receivings.receiving.details.subsequent_delivery_items_warning_label':
        'Please check the products for the next delivery under Additional delivery',
    'receivings.receiving.details.confirm_data_info_label': 'Confirm data info',
    'receivings.receiving.details.confirm_data_info':
        'In order to close receiving order you need to log into the desktop app to finalize process',
    'receivings.receiving.details.available_only_online_label':
        'Details available only online',
    'receivings.receiving.details.view_pdf_button_label': 'View PDF',
    'receivings.receiving.details.view_last_pdf_button_label': 'View last PDF',
    'receivings.receiving.details.add_freight_costs_button_label':
        'Add Freight Costs',
    'receivings.receiving.details.po': 'PO',
    'receivings.receiving.details.last_po': 'Last PO',
    'receivings.receiving.details.target_cost_centers_button_label':
        'Target cost centers',
    'receivings.receiving.details.confirm_data_button_label': 'CONFIRM DATA',
    'receivings.receiving.details.confirm_data_dialog.title': 'Confirm data',
    'receivings.receiving.details.confirm_data_dialog.content':
        'In order to close receiving order you need to log into the desktop app to finalize process',
    'receivings.receiving.details.supplier': 'Supplier',
    'receivings.receiving.details.order_number': 'Order number',
    'receivings.receiving.details.cost_center': 'Cost center',
    'receivings.receiving.details.delivery_date': 'Delivery date',
    'receivings.receiving.details.orderer': 'Orderer',
    'receivings.receiving.details.order_date_and_time': 'Order date and time',
    'receivings.receiving.details.total_value_of_order': 'Total value of order',
    'receivings.receiving.details.number_of_items': 'Number of items',
    'receivings.receiving.details.delivery_section': 'DELIVERY',
    'receivings.receiving.details.delivery_details_label': 'Delivery details',
    'receivings.receiving.details.delivery_details_description':
        'Control the delivery details',
    'receivings.receiving.details.additional_orders_label': 'Additional orders',
    'receivings.receiving.details.additional_orders_description':
        'Manage orders which came together',
    'receivings.receiving.details.deposit_items_label': 'Deposit items',
    'receivings.receiving.details.deposit_items_description':
        'Manage items to return with delivery',
    'receivings.receiving.details.subsequent_delivery_label':
        'Additional delivery',
    'receivings.receiving.details.subsequent_delivery_description':
        'Manage not delivered items',
    'receivings.receiving.details.target_cost_centers_label':
        'Target cost centers',
    'receivings.receiving.details.target_cost_centers_description':
        'View target cost centers',
    'receivings.receiving.details.attachments_label': 'Attachments',
    'receivings.receiving.details.attachments_description':
        'Related to Ordering and Receiving stages',
    'receivings.receiving.details.checksum_section': 'CHECKSUM',
    'receivings.receiving.details.total_items': 'Total items',
    'receivings.receiving.details.total_deposit_items': 'Total deposit items',
    'receivings.receiving.details.total_discount':
        'Total cash discount / other discounts',
    'receivings.receiving.details.total_delivery_note': 'Total delivery note',
    'receivings.receiving.details.exchange_rate': 'Exchange rate',
    'receivings.receiving.details.split_delivery_report_section':
        'SPLIT DELIVERY SUM',
    'receivings.receiving.details.edit_unavailable_due_to_approval_in_progress':
        'Edit is unavailable due to approval in progress',
    'receivings.receiving.details.edit_unavailable':
        'Edit is unavailable for this order',

    'receivings.receiving.attachments.title': 'Attachments',
    'receivings.receiving.attachments.online_only':
        'Attachments could be loaded only while online',

    'receivings.receiving.add_order.title': 'Add order',
    'receivings.receiving.available_orders.no_orders': 'No available orders',
    'receivings.receiving.available_orders.available_only_online':
        'Orders available only online',
    'receivings.receiving.related_orders.title': 'Related orders',
    'receivings.receiving.related_orders.no_orders': 'No related orders',
    'receivings.receiving.related_orders.available_only_online':
        'Orders available only online',
    'receivings.receiving.related_orders.add_order_confirmation.title':
        'Add Order?',
    'receivings.receiving.related_orders.add_orders_confirmation.title':
        'Add Orders?',
    'receivings.receiving.related_orders.add_order_confirmation.body':
        '{{count}} Orders',
    'receivings.receiving.related_orders.add_order_confirmation.add_action':
        'Add',
    'receivings.receiving.related_orders.add_order_alert.no_order_selected':
        'Select at least one order',
    'receivings.receiving.related_orders.order_added_successfully':
        'Order added successfully!',
    'receivings.receiving.related_orders.orders_added_successfully':
        'Orders added successfully!',
    'receivings.receiving.related_orders.orders_added_partially':
        'Not all orders where added successfully',
    'receivings.receiving.related_orders.delete_order_alert.content':
        'Delete Order?',
    'receivings.receiving.related_orders.delete_order_alert.no_action':
        'Cancel',
    'receivings.receiving.related_orders.delete_order_alert.yes_action':
        'Delete',
    'receivings.receiving.related_orders.order_deleted_successfully':
        'Order deleted successfully!',
    'receivings.receiving.order.original_order': 'Initial PO',
    'receivings.receiving.order.order_date': 'Order date',
    'receivings.receiving.order.delivery_date': 'Delivery date',
    'receivings.receiving.order.order_value': 'Order value',
    'receivings.receiving.order.pdf_view_btn_label': 'View PDF',
    'receivings.receiving.order.original_order_view_btn_label':
        'View initial PO',
    'receivings.receiving.order.add_btn_label': 'Add Order',
    'receivings.receiving.order.partial_history_btn_label':
        'History of Partial Receiving',

    'receivings.receiving.partial_history.title':
        'History of Partial Receiving',
    'receivings.receiving.partial_history.no_history': 'No partial history',
    'receivings.receiving.partial_history.available_only_online':
        'Partial history available only online',
    'receivings.receiving.partial_history.date_of_booking': 'Date of Booking',
    'receivings.receiving.partial_history.receiving_amount': 'Receiving Amount',

    'receivings.receiving.additional_delivery.label': 'Additional delivery',
    'receivings.receiving.subsequent_delivery.will_be_delivered_later':
        '{{item_name}} will be delivered later',
    'receivings.receiving.subsequent_delivery.will_be_delivered_as_scheduled':
        '{{item_name}} will be delivered as scheduled',
    'receivings.receiving.subsequent_delivery.all_articles_will_be_delivered_later':
        'All articles will be delivered later',
    'receivings.receiving.subsequent_delivery.all_articles_will_be_delivered_as_scheduled':
        'All articles will be delivered as scheduled',
    'receivings.receiving.subsequent_delivery.no_items': 'No items',
    'receivings.receiving.subsequent_delivery.available_only_online':
        'Items available only online',
    'receivings.receiving.subsequent_delivery.item_quantity_ordered': 'Ordered',
    'receivings.receiving.subsequent_delivery.item_quantity_received':
        'Received',
    'receivings.receiving.subsequent_delivery.item_quantity_difference':
        'Difference',

    'receivings.receiving.subsequent_delivery.all_will_be_delivered_later':
        'All will be Delivered Later',
    'receivings.receiving.subsequent_delivery.no_additional_delivered_articles':
        'No Additional delivered Articles',

    'receiving.receiving_booking_result.label': 'Booking Result',
    'receiving.receiving_booking_result.booked_successfully':
        'Receiving booked successfully!',
    'receiving.receiving_booking_result.booking_error':
        'There was an error booking a receiving',
    'receiving.receiving_booking_result.booking_status_unknown':
        'Unknown receiving booking status',
    'receiving.receiving_booking_result.export_ok_message':
        'Export to Allinvos: OK',
    'receiving.receiving_booking_result.export_failed_message':
        'Export: FAILED',
    'receiving.receiving_booking_result.open_delivery_note_button.title':
        'Show Booking Voucher',
    'receiving.receiving_booking_result.open_delivery_note_button.no_permissions_message':
        'You have no permissions to access delivery notes!',

    'receivings.item_lookup.order_title': 'Add Article from Order',
    'receivings.item_lookup.supplier_title': 'Add Article from Supplier',
    'receivings.item_lookup.inventory_management_title':
        'Add Article from Inventory Management',
    'receivings.item_lookup.action_button_label': 'ADD',
    'receivings.item_lookup.search_available_only_online':
        'Search available only online',
    'receivings.item_lookup.filter.sorting.by_name_asc': 'Name A-Z',
    'receivings.item_lookup.filter.sorting.by_name_desc': 'Name Z-A',
    'receivings.item_lookup.item_added_successfully_label':
        'Item added successfully!',
    'receivings.create_button_label': 'Create receiving',
    'receivings.create.title': 'Create Receiving',
    'receivings.create.all_fields_are_required': 'All fields are required',
    'receivings.create.button_label': 'Create',
    'receivings.create.supplier_label': 'Supplier',
    'receivings.create.accountancy_cost_center_label':
        'Accountancy cost center',
    'receivings.order.delivery_details.delivery_note_edit.label':
        'Delivery note',
    'receivings.order.delivery_details.delivery_note_edit.update': 'Update',

    // Delivery notes
    'delivery_notes.title': 'Delivery notes',
    'delivery_notes.available_only_online':
        'Delivery notes available only online',
    'delivery_notes.empty_label': 'No delivery notes',
    'delivery_notes.filter.sorting.by_booking_id_asc': 'Booking id A-Z',
    'delivery_notes.filter.sorting.by_booking_id_desc': 'Booking id Z-A',
    'delivery_notes.filter.sorting.by_order_id_asc': 'Order id A-Z',
    'delivery_notes.filter.sorting.by_order_id_desc': 'Order id Z-A',
    'delivery_notes.filter.sorting.by_order_received_at_asc':
        'Order received at ASC',
    'delivery_notes.filter.sorting.by_order_received_at_desc':
        'Order received at DESC',
    'delivery_notes.filter.sorting.by_delivery_note_id_asc':
        'Delivery note id ASC',
    'delivery_notes.filter.sorting.by_delivery_note_id_desc':
        'Delivery note id DESC',
    'delivery_notes.filter.sorting.by_delivery_note_date_asc':
        'Delivery note date ASC',
    'delivery_notes.filter.sorting.by_delivery_note_date_desc':
        'Delivery note date DESC',
    'delivery_notes.filter.sorting.by_delivery_note_total_asc':
        'Delivery note total ASC',
    'delivery_notes.filter.sorting.by_delivery_note_total_desc':
        'Delivery note total DESC',
    'delivery_notes.filter.sorting.by_supplier_asc': 'Supplier A-Z',
    'delivery_notes.filter.sorting.by_supplier_desc': 'Supplier Z-A',
    'delivery_notes.filter.booking_id.label': 'Booking voucher',
    'delivery_notes.filter.booking_id.hint_text': 'Booking ID',
    'delivery_notes.filter.date_range': 'Delivery note date',
    'delivery_notes.filter.supplier_name.label': 'Supplier',
    'delivery_notes.filter.supplier_name.hint_text': 'Supplier name',
    'delivery_notes.filter.delivery_note_id.label': 'Delivery note number',
    'delivery_notes.filter.delivery_note_id.hint_text': 'Delivery note number',
    'delivery_notes.filter.delivery_note_amount.label': 'Delivery note total',
    'delivery_notes.filter.delivery_note_amount.hint_text': 'Amount',
    'delivery_notes.filter.product_name.label': 'Article name',
    'delivery_notes.filter.product_name.hint_text': 'Article name',
    'delivery_notes.filter.receiving_confirmed_by.label':
        'Receiving confirmed by',
    'delivery_notes.filter.receiving_confirmed_by.hint_text': 'User name',

    'delivery_notes.booking_date': 'Booking date',
    'delivery_notes.delivery_note_no': 'Delivery note no.',
    'delivery_notes.total_delivery_note': 'Total delivery note',
    'delivery_notes.view_pdf_button_label': 'View PDF',
    'delivery_notes.booking_voucher': 'Booking voucher',
    'delivery_notes.supplier': 'Supplier',
    'delivery_notes.delivery_note_date': 'Delivery note date',
    'delivery_notes.delivery_note_total': 'Delivery note total',
    'delivery_notes.delivery_note_total_check': 'Delivery note total check',
    'delivery_notes.receiving_confirmed_by': 'Receiving confirmed by',
    'delivery_notes.invoice': 'Invoice',
    'delivery_notes.open_receiving_button_label': 'Open receiving',

    'delivery_notes.transaction_type.in_order_nos': 'NON PO RECEIVING',
    'delivery_notes.transaction_type.in_order_wso': 'PO RECEIVING',
    'delivery_notes.warnings_tag': 'WARNINGS',
    'delivery_notes.status.in_approval': 'In approval',
    'delivery_notes.status.delivery_note_enabled': 'Delivery note enabled',
    'delivery_notes.status.delivery_note_disabled': 'Delivery note disabled',
    'delivery_notes.medius_status.mapping_error':
        'Export failed. Mapping error',
    'delivery_notes.medius_status.server_error': 'Export failed. Server error',
    'delivery_notes.medius_status.exporting': 'Exporting to Medius',
    'delivery_notes.medius_status.ok': 'Exported to Medius',
    'delivery_notes.delivery_note.medius_status.mapping_error':
        'Export to Medius failed - Mapping error: {{time}}',
    'delivery_notes.delivery_note.medius_status.server_error':
        'Export to Medius failed - Server error: {{time}}',
    'delivery_notes.delivery_note.medius_status.ok':
        'Exported to Medius: {{time}}',
    'delivery_notes.delivery_note.medius_info.mapping_error':
        'Please submit a <NAME_EMAIL> and manually re-export once the mapping is fixed.',
    'delivery_notes.delivery_note.medius_info.server_error':
        'It will be automatically re-exported every 5 minutes.',
    'delivery_notes.delivery_note.medius.re_export': 'Re-export',
    'delivery_notes.delivery_note.medius.reset_success':
        'Re-export was requests',

    'delivery_notes.delivery_note.warning.archived_items':
        'Contains items which have been filed due to a change of the inventory unit',
    'delivery_notes.delivery_note.warning.incoming_goods_were_processed_but_not_booked':
        'Incoming goods were processed but not booked again',
    'delivery_notes.delivery_note.warning.price_differs_from_average_price':
        'The price of incoming goods entered, differs considerably from the current average price',

    'delivery_notes.delivery_note.title': 'Delivery note',

    'delivery_notes.toggle_success': 'Delivery note updated',

    // Filter
    'filter.label': 'Filter',
    'filter.filtering': 'Filtering',
    'filter.sorting': 'Sorting',
    'filter.sort_by': 'Sort by',
    'filter.search': 'Search',
    'filter.show_less': 'Show less',
    'filter.show_more': 'Show more',
    'filter.dates.select': 'Select',

    // Warehouse lookup
    'warehouse_lookup.store.title': 'Store lookup',
    'warehouse_lookup.cost_type.title': 'Cost type lookup',
    'warehouse_lookup.cost_center.title': 'Cost center lookup',
    'warehouse_lookup.local_supplier.title': 'Local supplier lookup',
    'warehouse_lookup.vat.title': 'VAT lookup',
    'warehouse_lookup.category.title': 'Category lookup',
    'warehouse_lookup.packing_unit.title': 'Packing unit lookup',
    'warehouse_lookup.inventory_unit.title': 'Inventory unit lookup',
    'warehouse_lookup.item.title': 'Item lookup',
    'warehouse_lookup.deposit_item.title': 'Deposit item lookup',
    'warehouse_lookup.currency.title': 'Currency lookup',
    'warehouse_lookup.action_button_label': 'SELECT',
    'warehouse_lookup.search_available_only_online':
        'Search available only online',
    'warehouse_lookup.config_booking_type.title': 'Booking type lookup',
    'warehouse_lookup.config_booking_reason.title': 'Booking reason lookup',
    'warehouse_lookup.config_transfer_type.title': 'Transfer type lookup',
    'warehouse_lookup.config_capex_type.title': 'Capex type lookup',
    'warehouse_lookup.config_recipe_type.title': 'Recipe type lookup',
    'warehouse_lookup.config_recipe_prep_type.title': 'Recipe prep type lookup',
    'warehouse_lookup.config_deposit_booking.title': 'Deposit booking lookup',
    'warehouse_lookup.config_deposit_sync.title': 'Deposit sync lookup',

    // Webshop cost center lookup
    'webshop.cost_center_lookup.title': 'Cost center',
    'webshop.cost_center_lookup.filter.sorting.by_division_asc': 'Division ASC',
    'webshop.cost_center_lookup.filter.sorting.by_division_desc':
        'Division DESC',
    'webshop.cost_center_lookup.filter.sorting.by_cost_center_asc':
        'Cost center ASC',
    'webshop.cost_center_lookup.filter.sorting.by_cost_center_desc':
        'Cost center DESC',
    'webshop.cost_center_lookup.filter.sorting.by_cost_center_id_asc':
        'Cost center ID ASC',
    'webshop.cost_center_lookup.filter.sorting.by_cost_center_id_desc':
        'Cost center ID DESC',
    'webshop.cost_center_lookup.filter.sorting.by_last_accessed_at_asc':
        'Last accessed at ASC',
    'webshop.cost_center_lookup.filter.sorting.by_last_accessed_at_desc':
        'Last accessed at DESC',

    // Budget
    'budget.calc.no_budgets': 'No budget to display',
    'budget.calc.more_info': 'More info',
    'budget.calc.annual_only': 'Annual only',
    'budget.calc.actual_annual_expenses': 'Actual Expenses, annual',
    'budget.calc.actual_period_expenses': 'Actual Expenses',
    'budget.calc.expected_annual_expenses': 'Expected Expenses, annual',
    'budget.calc.expected_period_expenses': 'Expected Expenses',
    'budget.calc.expected_annual_remaining': 'Expected Remaining, annual',
    'budget.calc.expected_period_remaining': 'Expected Remaining',
    'budget.calc.at_shopping_cart': 'at Shopping Cart',
    'budget.calc.at_approval_center': 'at Approval Center',
    'budget.calc.not_approved': 'Not Approved',
    'budget.calc.approved': 'Approved',
    'budget.calc.current_request': 'Current Request',
    'budget.calc.other_purchase_requests': 'Other Purchase Requests',
    'budget.calc.budget_exited': 'Exited',
    'budget.calc.in_budget': 'In budget',
    'budget.calc.budget_warning': '{{count}} Warning',
    'budget.calc.budget_warnings': '{{count}} Warnings',

    // Login cost center lookup
    'login.cost_center_lookup.title': 'Cost center',

    // Bulk price offers
    'bulk_price_offers.title': 'Bulk price offers',
    'bulk_price_offers.quantity_label': 'Quantity',
    'bulk_price_offers.price_label': 'Price',
    'bulk_price_offers.profit_label': 'Profit',
    'bulk_price_offers.regular_price_label': 'Regular price per unit',
    'bulk_price_offers.add_button_label': 'ADD',
    'bulk_price_offers.table.quantity': 'QUANTITY',
    'bulk_price_offers.table.price': 'PRICE',

    // Setup
    'setup.try_again_button_label': 'TRY AGAIN',

    // PDF view
    'pdf_view.title': 'PDF view',

    // Image viewer
    'image_viewer.title': 'Image view',

    // Alert dialog
    'alert_dialog.ok_button_label': 'OK',
    'alert_dialog.cancel_button_label': 'CANCEL',
    'alert_dialog.close_button_label': 'CLOSE',
    'alert_dialog.add_button_label': 'ADD',
    'alert_dialog.done_button_label': 'DONE',
    'alert_dialog.save_button_label': 'SAVE',
    'alert_dialog.delete_button_label': 'DELETE',
    'alert_dialog.undo_button_label': 'UNDO',
    'alert_dialog.retry_button_label': 'RETRY',
    'alert_dialog.refresh_button_label': 'REFRESH',
    'alert_dialog.dismiss_button_label': 'DISMISS',
    'alert_dialog.open_button_label': 'OPEN',
    'alert_dialog.loading_label': 'Loading...',
    'alert_dialog.error_title': 'Error',

    // Licenses
    'licenses.count__named': 'Licenses count: {{count}}',
    'licenses.title': 'Open source licenses',
    'licenses.open_screen_button_label': 'Open third party licenses',

    // Others labels
    'pin_confirmation.label':
        'Please enter your personal PIN code to carry out the action',

    // Formatting
    'format.date': 'dd.MM.yyyy',
    'format.time': 'HH:mm',
    'format.date_time': 'dd.MM.yyyy, HH:mm',
    'format.weekday': 'E',

    // Navigation
    'navigation.ordering_section': 'Ordering',
    'navigation.receivings_section': 'Goods Receiving',
    'navigation.approval_center_section': 'Approval Center',
    'navigation.settings_section': 'Settings',
    'navigation.transactions_section': 'Transactions',
    'navigation.test_screen': 'Test Screen',

    'navigation.dashboard': 'Dashboard',
    'navigation.inventories': 'Inventories',
    'navigation.recipe_categories': 'Recipes Overview',
    'navigation.reports': 'Reports',
    'navigation.cart': 'Shopping Cart',
    'navigation.notifications': 'Notifications',

    'navigation.order_lists': 'Order Lists',
    'navigation.catalog': 'Catalogue',
    'navigation.suppliers': 'Supplier Overview',
    'navigation.orders': 'Order Archive',
    'navigation.scanned_documents': 'Documents',
    'navigation.invoices_archive': 'Invoices',

    'navigation.process_receivings': 'Process Receiving',
    'navigation.delivery_notes': 'Delivery Notes',

    'navigation.purchase_requests': 'Authorise Orders',
    'navigation.transfer_orders_approval': 'Transfer Orders Approval',
    'navigation.receiving_approval_requests': 'Receiving Variance Approval',
    'navigation.capex_approval_requests': 'CAPEX Approval Requests',
    'navigation.invoices': 'Approve Invoices',

    'navigation.transfer_lists': 'Transfer List',
    'navigation.transfer_list_orders': 'Transfer Order Archive',
    'navigation.incomplete_bookings': 'Incomplete Bookings',

    'navigation.master_data_section': 'Master Data',
    'navigation.store_master_data': 'Store Master Data',

    'navigation.manage_devices': 'Manage Devices',
    'navigation.settings': 'App Settings',
    'navigation.help_center': 'Help Center',
    'navigation.imprint_and_legal_notice': 'Imprint & Legal Notice',
    'navigation.logout': 'Logout',
    'navigation.test': 'Test',

    // Bluetooth devices
    'bluetooth.turned_off_title': 'Bluetooth is turned off',
    'bluetooth.grant_permissions_button_label': 'Grant Permissions',
    'bluetooth.turned_off_description':
        'Please turn on the bluetooth on your device.',
    'bluetooth.no_permission_title': '“Allow” app to access the Bluetooth',
    'bluetooth.no_permission_description':
        'By using Bluetooth it is possible to connect with devices.',
    'bluetooth.location_service_disabled_title': 'Turn on location service',
    'bluetooth.location_service_disabled_description':
        'Location service is needed for proper Bluetooth work.',
    'bluetooth.unsupported_on_the_device':
        'Bluetooth is unsupported on the device',
    'bluetooth.give_bluetooth_permission_on_the_device':
        'Please give permission for Bluetooth usage for the app',
    'bluetooth.give_location_permission_on_the_device':
        'Please give permission for location usage for the app',

    'bluetooth_devices.lookup.title': 'Select device',
    'bluetooth_devices.add_button_label': 'ADD',
    'bluetooth_devices.no_devices_title': 'There are no devices',
    'bluetooth_devices.connect_button_label': 'CONNECT',

    'bluetooth_devices.device.connect_button_label': 'CONNECT',
    'bluetooth_devices.device.disconnect_button_label': 'DISCONNECT',
    'bluetooth_devices.device.rename_button_label': 'Rename',
    'bluetooth_devices.device.delete_button_label': 'Delete',
    'bluetooth_devices.device.delete_confirmation_dialog.title':
        'Delete device?',
    'bluetooth_devices.device.delete_confirmation_dialog.cancel_action':
        'Cancel',
    'bluetooth_devices.device.delete_confirmation_dialog.confirm_action':
        'Delete',

    'bluetooth_devices.device_type.select_type_header': 'Select device type',
    'bluetooth_devices.device_type.not_compatible_label': 'Not compatible',
    'bluetooth_devices.device_type.thermometer_label': 'Thermometer',
    'bluetooth_devices.device_type.scale_label': 'Electronic scale',
    'bluetooth_devices.device_type.not_compatible_dialog.title':
        'This device is not compatible with your operating system',
    'bluetooth_devices.device_type.not_compatible_dialog.message':
        'Please select another device.',
    'bluetooth_devices.device_type.not_compatible_dialog.close_action': 'Okay',

    'bluetooth_devices.device.reconnect.header': 'Connecting to Device',
    'bluetooth_devices.device.connecting_to_device_label':
        'Connecting to the device',
    'bluetooth_devices.device.retry_connection_button_label': 'Retry',
    'bluetooth_devices.device.connection_successful_greeting':
        'Congratulations!',
    'bluetooth_devices.device.connection_successful_details':
        'Your Device has been connected successfully',
    'bluetooth_devices.device.next_button_label': 'Next',
    'bluetooth_devices.device.setup.header': 'Setup Device',
    'bluetooth_devices.device.setup.measurement_verification.label':
        'Test measurement',
    'bluetooth_devices.device.setup.measurement_verification.first_step':
        'Please take a test measurement',
    'bluetooth_devices.device.setup.measurement_verification.second_step':
        'Compare the values between device and value below',
    'bluetooth_devices.device.setup.measurement_verification.confirmation_button_label':
        'Correct',
    'bluetooth_devices.device.setup.measurement_verification.original_unit':
        'Original unit:',

    'bluetooth_devices.device.rename.header': 'Rename Device',
    'bluetooth_devices.device.rename.save_button_label': 'Save',
    'bluetooth_devices.device.rename.renamed_successfully_label':
        'Device renamed successfully!',

    'bluetooth_devices.device.connected_successfully_label':
        'Device connected successfully!',
    'bluetooth_devices.device.disconnected_successfully_label':
        'Device disconnected successfully!',
    'bluetooth_devices.device.deleted_successfully_label':
        'Device deleted successfully!',
    'bluetooth_devices.device.connection_label': 'Checking if everything is ok',
    'bluetooth_devices.device.retry_connect_button_label': 'RETRY',
    'bluetooth_devices.device.setup.friendly_name_field_label': 'Friendly name',
    'bluetooth_devices.device.setup.friendly_name_field_description':
        'Add a name to help you recognize the device later in your work',
    'bluetooth_devices.device.setup.save_button_label': 'SAVE',

    'bluetooth_lookup.title': 'Device lookup',
    'bluetooth_lookup.no_devices_found_label': 'No devices found',
    'bluetooth_lookup.scan_again_button_label': 'Scan again',
    'bluetooth_lookup.scanning_error_hint':
        'Make sure Bluetooth is enabled and try again',
    'bluetooth_lookup.device_not_found_hint':
        'If the device you wish to connect is not in the list, kindly ensure that it is turned on and configured appropriately.',

    'measurement_taking.bluetooth_device_not_connected_label':
        'Device is not connected',
    'measurement_taking.take_measurement_label': 'Take measurement',
    'measurement_taking.save_button_label': 'SAVE',
    'measurement_taking.enter_manually_button_label': 'ENTER MANUALLY',
    'measurement_taking.weight_label': 'Weight',
    'measurement_taking.temperature_label': 'Temperature',
    'measurement_taking.select_device_label': 'Select Device',
    'measurement_taking.save_measurement_button_label': 'Save',
    'measurement_taking.switch_to_manual_mode_button_label': 'Manually',
    'measurement_taking.lost_connection_to_the_device_label':
        'Lost connection to the device',

    // Camera
    'camera.title.capture': 'Camera',
    'camera.title.barcode': 'Scanning...',
    'camera.no_cameras_available': 'No cameras available',

    // Reports
    'reports.search_available_only_online': 'Search available only online',
    'reports.report_available_only_online': 'Report view available only online',
    'reports.coming_soon_label': 'Reports coming soon',
    'reports.filter.category_label': 'Category',
    'reports.filter.category.any': 'Any',

    // Quantity selector
    'quantity_selector.speech.how_many_items.label': 'How many items?',
    'quantity_selector.speech.error_unavailable.label':
        'Speech recognition unavailable',
    'quantity_selector.speech.unable_to_recognize_quantity.label':
        'Unable to recognize quantity',
    'quantity_selector.speech.try_again_button.label': 'TRY AGAIN',

    //Help center
    'help_center.available_only_online':
        'Help center is available only while online',

    //IMS
    'ims.mapping_products.title': 'Item mapping',
    'ims.mapping_products.search_available_only_online':
        'Search available only while online',
    'ims.mapping_products.item_updated': 'Item updated successfully',
    'ims.mapping_products.product.item_name_label': 'Item name',
    'ims.mapping_products.product.stock_item_id_label': 'WWS ArtNo',
    'ims.mapping_products.product.inventory_unit_price_label':
        'Inventory unit price',
    'ims.mapping_products.product.category_label': 'Category',
    'ims.mapping_products.product.cost_type_label': 'Cost type',
    'ims.mapping_products.product.vat_label': 'Value added tax',
    'ims.mapping_products.product.store_label': 'Store',
    'ims.mapping_products.product.inventory_unit_label': 'Inventory unit',
    'ims.mapping_products.product.update_button_label': 'UPDATE',
    'ims.mapping_products.filter.sorting.by_stock_item_id_asc':
        'WWS Art No (A-Z)',
    'ims.mapping_products.filter.sorting.by_stock_item_id_desc':
        'WWS Art No (Z-A)',
    'ims.mapping_products.filter.sorting.by_name_asc': 'Item name (A-Z)',
    'ims.mapping_products.filter.sorting.by_name_desc': 'Item name (Z-A)',
    'ims.ihl.lookup_item_to_add_online_only':
        'Items lookup available only while online',
    'ims.ihl.lookup_item_to_add.title': 'Select item to add',
    'ims.ihl.lookup_item_to_add.already_in_list_message':
        'This item is already in list',
    'ims.ihl.lookup_item_to_add.cant_be_add_message':
        'This item cannot be added',
    'ims.ihl.lookup_item_to_add.store_not_mapped_message': 'Store not mapped',
    'ims.ihl.lookup_item_to_add.action_button_label': 'ADD',
    'ims.ihl.items.filter.sorting.by_position_asc': 'Position forward',
    'ims.ihl.items.filter.sorting.by_position_desc': 'Position backward',
    'ims.expired_documents.title': 'Expired documents',
    'ims.expired_documents.no_files': 'The document contains no files',
    'ims.expired_documents.search_available_only_online':
        'Search available only online',
    'ims.expired_documents.is_private': "You can't open because it's private",
    'ims.stores.search_available_only_online':
        'Stores search available only online',
    'ims.stores.filter.sorting.by_store_name_asc': 'Store A-Z',
    'ims.stores.filter.sorting.by_store_name_desc': 'Store Z-A',
    'ims.stores.filter.sorting.lowest_store_first': 'Lowest store first',
    'ims.stores.filter.sorting.highest_store_first': 'Highest store first',
    'ims.store.title': 'Store data',
    'ims.store.cost_center_store': 'Cost centre store',
    'ims.store.last_update': 'Last update',
    'ims.store.details_tab': 'Details',
    'ims.store.products_tab': 'Products',
    'ims.store.main_info': 'Main info',
    'ims.store.store_section': 'Store',
    'ims.store.order_list': 'Order list',
    'ims.store.order_list_last_update': 'Order list last update',
    'ims.store.months_for_inventories': 'Months for taking the inventories',
    'ims.store.months_updated_successfully':
        'Inventory months updated successfully',
    'ims.store.edit_closed_store_warning': 'Can not edit closed store',
    'ims.store.products.filter.sorting.by_position': 'Position',
    'ims.store.products.filter.sorting.by_name_asc': 'Name, A-Z',
    'ims.store.products.filter.sorting.by_name_desc': 'Name, Z-A',
    'ims.store.products.filter.sorting.by_price_asc': 'Price, ASC ',
    'ims.store.products.filter.sorting.by_price_desc': 'Price, Desc',
    'ims.store.products.filter.sorting.by_store_asc': 'Store, ASC ',
    'ims.store.products.filter.sorting.by_store_desc': 'Store, Desc',
    'ims.store.products.product.id_label': 'ID',
    'ims.store.products.product.category_label': 'Category',
    'ims.store.products.product.cost_type_label': 'Cost Type',
    'ims.store.products.product.cost_center_label': 'Cost center',
    'ims.store.products.product.price_label': 'Price',
    'ims.store.products.product.inventory_unit_label': 'Inventory Unit',
    'ims.store.products.product.store_label': 'Store',
    'ims.store.products.product.min_max_store_label': 'Min/Max Store',
    'ims.store.products.product.delete_alert_content':
        'Do you really want to remove this product?',
    'ims.store.products.product.delete_alert_yes_action': 'Delete',
    'ims.store.products.product.delete_alert_no_action': 'Cancel',
    'ims.store.products.product.deleted_successfully':
        'Product deleted successfully',
    'ims.store.offline_no_result_message':
        'Please check you network connection',
    'ims.store.products.search_available_only_online':
        'Products search available only online',
    'ims.store.products.product.edit_eans': 'GTIN codes',
    'ims.store.products.product.cannot_bee_deleted':
        "This product can't be deleted",
    'ims.product.edit_eans_title': 'Edit GTIN',
    'ims.product.no_eans': 'No GTIN codes',
    'ims.product.duplicate_ean': 'GTIN is already in the list',
    'ims.product.ean_label': 'GTIN',

    // Recipes
    'recipes.filter.sorting.by_name_asc': 'Name A-Z',
    'recipes.filter.sorting.by_name_desc': 'Name Z-A',
    'recipes.filter.sorting.by_sell_price_asc': 'Sell price, lowest first',
    'recipes.filter.sorting.by_sell_price_desc': 'Sell price, highest first',
    'recipes.filter.sorting.by_last_updated_asc': 'Last updated, oldest first',
    'recipes.filter.sorting.by_last_updated_desc': 'Last updated, newest first',
    'recipes.filter.category_label': 'Category',
    'recipes.filter.category_any_label': 'Any',
    'recipes.filter.favorites_label': 'Favorites',
    'recipes.filter.favorites_all_label': 'All recipes',
    'recipes.filter.favorites_only_label': 'Only favorites',
    'recipes.categories.filter.sorting.by_name_asc': 'Category A-Z',
    'recipes.categories.filter.sorting.by_name_decs': 'Category Z-A',
    'recipes.categories.online_search_only':
        'Search available only while online',
    'recipes.categories.category.recipes_count': 'Recipes count: {{count}}',
    'recipes.categories_tab': 'BY CATEGORY',
    'recipes.favorites_tab': 'FAVORITES',
    'recipes.recipes_tab': 'RECIPES',
    'recipes.online_search_only': 'Search available only while online',
    'recipes.add_to_favorites_error': 'Unable to change recipe favorite status',
    'recipes.recipe.preparation_time.minutes': 'min',
    'recipes.recipe.title': 'Recipe details',
    'recipes.recipe.type_label': 'Recipe type',
    'recipes.recipe.sale_price_label': 'Sale price',
    'recipes.recipe.cost_of_sales_label': 'Const of sales %',
    'recipes.recipe.inventory_unit_label': 'Inventory unit',
    'recipes.recipe.preparation_time_label': 'Preparation time',
    'recipes.recipe.ingredients_link': 'Ingredients',
    'recipes.recipe.preparation_steps_link': 'Preparation steps',
    'recipes.recipe.allergens_and_additives_link': 'Allergens & Additives',
    'recipes.recipe.nutrients_link': 'Nutritional value',
    'recipes.recipe.costs_link': 'Recipe costs',
    'recipes.recipe.notes_link': 'Notes',
    'recipes.recipe.ingredients.title': 'Ingredients',
    'recipes.recipe.ingredients.only_online':
        'Ingredients can be loaded only while online',
    'recipes.recipe.ingredients.add_to_cart_success':
        'Successfully added to cart',
    'recipes.recipe.ingredients.add_to_cart_confirmation':
        'Are you sure want to add ingredients for {{portions}} portions to the cart?',
    'recipes.recipe.ingredients.add_to_cart.yes_label': 'Yes',
    'recipes.recipe.ingredients.add_to_cart.no_label': 'No',
    'recipes.recipe.ingredients.portions_label': 'Portions',

    'recipes.recipe.share.title': 'Share recipe with',

    'recipes.recipe.order.supplier_status.selected': 'Supplier selected',
    'recipes.recipe.order.supplier_status.not_selected':
        'Supplier not selected',
    'recipes.recipe.order.supplier_status.not_available':
        'Supplier not available',
    'recipes.recipe.order.item_status.not_available': 'Item is not available',
    'recipes.recipe.order.supplier_status.unknown': 'Unknown supplier status',
    'recipes.recipe.order.item.meta_id_label': 'WS No.',
    'recipes.recipe.order.item.position_id_label': 'Article No.',
    'recipes.recipe.order.item.supplier_label': 'Supplier',
    'recipes.recipe.order.item.quantity_label': 'Quantity',
    'recipes.recipe.order_items.title': 'Order items',
    'recipes.recipe.order.move_to_cart_label': 'MOVE TO CART',
    'recipes.recipe.order.move_to_cart_success': 'Products added to cart',

    'recipes.recipe.order_item_suppliers.title': 'Available suppliers',
    'recipes.recipe.order_item_suppliers.save_label': 'SAVE',

    'recipes.recipe.preparation_steps.title': 'Preparation',
    'recipes.recipe.preparation_steps.current_step_label':
        '{{current_step}} of {{total_steps}}',
    'recipes.recipe.preparation_steps.no_steps':
        'No preparation steps found for current recipe',
    'recipes.recipe.preparation_steps.step.no_description':
        'No description for current step',
    'recipes.recipe.preparation_steps.step.previous': 'PREVIOUS',
    'recipes.recipe.preparation_steps.step.next': 'NEXT',
    'recipes.recipe.preparation_steps.only_online':
        'Preparation steps can be viewed only while online',
    'recipes.recipe.preparation_steps.final_step': 'Final step',
    'recipes.recipe.allergens_and_additives.title': 'Allergens & Additives',
    'recipes.recipe.allergens_and_additives.allergens.title': 'ALLERGENS',
    'recipes.recipe.allergens_and_additives.additives.title': 'ADDITIVES',
    'recipes.recipe.allergens_and_additives.no_allergens':
        'No allergens found for current recipe',
    'recipes.recipe.allergens_and_additives.no_additives':
        'No additives found for current recipe',
    'recipes.recipe.nutrients.title': 'Nutritional value',
    'recipes.recipe.nutrients.only_online':
        'Nutrients can be viewed only while online',
    'recipes.recipe.calculation.only_online':
        'Nutrients can be viewed only while online',
    'recipes.recipe.calculation.title': 'Recipe costs',
    'recipes.recipe.calculation.summary_title': 'SUMMARY',
    'recipes.recipe.calculation.label.specified_sales_price':
        'Specified sales price',
    'recipes.recipe.calculation.label.calculative_sales_price':
        'Calculative sales price',
    'recipes.recipe.calculation.cost_of_sale_title': 'COST OF SALES %',
    'recipes.recipe.calculation.label.total_cost_of_sales':
        'Total cost of sales recipe',
    'recipes.recipe.calculation.label.total_cost_of_sales_portion':
        'Total cost of sales portion',
    'recipes.recipe.calculation.label.not_billable_accompaniments':
        'Not billable accompaniments',
    'recipes.recipe.calculation.label.total_target_cost_of_sales_calc':
        'Total target cost of sales calculation',
    'recipes.recipe.calculation.label.expenses_cost_of_sales':
        'Expenses cost of sales display/breakage',
    'recipes.recipe.calculation.label.total_cost_of_sales_gross_calc':
        'Total cost of sales gross calculation',
    'recipes.recipe.calculation.manpower_requirements_title':
        'MANPOWER REQUIREMENTS',
    'recipes.recipe.calculation.label.hourly_rate': 'Hourly rate',
    'recipes.recipe.calculation.label.direct_mpwr_reqs':
        'Direct manpower requirements',
    'recipes.recipe.calculation.label.mpwr_reqs_prod_recipe':
        'Manpower requirements production',
    'recipes.recipe.calculation.label.semi_finished_goods_mpwr_reqs':
        'Manpower requirements for semi-finished goods',
    'recipes.recipe.calculation.label.mpwr_reqs_prod':
        'Manpower requirements for production portion',
    'recipes.recipe.calculation.label.total_mpwr_reqs_prod_portion':
        'Total Manpower requirements production portion',
    'recipes.recipe.calculation.label.indirect_mpwr_reqs_prod':
        'Indirect manpower requirements production in % to direct man power requirements',
    'recipes.recipe.calculation.label.total_mpwr_reqs_calc_portion':
        'Total Manpower requirements calculation (portion)',
    'recipes.recipe.calculation.general_expanses_title': 'GENERAL EXPENSES',
    'recipes.recipe.calculation.label.cost_price': 'Cost price',
    'recipes.recipe.calculation.label.calculative_profit_margin':
        'Calculative profit margin',

    'recipes.notes.filter.sorting.by_date_added_asc':
        'Date added (oldes first)',
    'recipes.notes.filter.sorting.by_date_added_desc':
        'Date added (newest first)',
    'recipes.notes.filter.sorting.by_date_updated_asc':
        'Date updated (oldes first)',
    'recipes.notes.filter.sorting.by_date_updated_desc':
        'Date updated (newest first)',
    'recipes.notes.filter.sorting.by_user_added_asc': 'User added (A-Z)',
    'recipes.notes.filter.sorting.by_user_added_desc': 'User added (Z-A)',
    'recipes.notes.filter.sorting.by_user_updated_asc': 'User updated (A-Z)',
    'recipes.notes.filter.sorting.by_user_updated_desc': 'User updated (Z-A)',
    'recipes.notes.delete_success': 'Note deleted successfully',
    'recipes.notes.add_success': 'Note added successfully',
    'recipes.notes.edit_success': 'Note updated successfully',
    'recipes.notes.only_online': 'Notes can be loaded only while online',
    'recipes.notes.title': 'Notes',
    'recipes.notes.add_label': 'ADD NOTE',
    'recipes.notes.new_note.title': 'New note',
    'recipes.notes.new_note.label': 'Note',
    'recipes.notes.new_note.button_label': 'ADD NOTE',
    'recipes.notes.edit_note.title': 'Edit note',
    'recipes.notes.edit_note.label': 'Note',
    'recipes.notes.edit_note.button_label': 'SAVE',
    'recipes.notes.delete_confirm_content':
        'Are you sure want to delete this note?',
    'recipes.notes.delete_confirm_no_label': 'NO',
    'recipes.notes.delete_confirm_yes_label': 'YES',

    // Pdf
    'pdf.available_only_online': 'PDF view available only online',

    //Image
    'image.available_only_online': 'Image view available only online',

    //Attachment Files
    'attachments.delete_confirmation_content':
        'Are you sure want to delete this document?',
    'attachments.delete_confirmation_yes_label': 'YES',
    'attachments.delete_confirmation_no_label': 'NO',
    'attachments.rename_confirmation_label': 'SAVE',
    'attachments.edit_name_confirmation_label': 'Update',
    'attachments.edit_name_field_label': 'Edit Name',
    'attachments.upload_confirmation_label': 'UPLOAD',
    'attachments.rename_cancel_label': 'CANCEL',
    'attachments.rename_field_label': 'Document name',
    'attachments.default_file_name': 'File',
    'attachments.action.view': 'View',
    'attachments.action.rename': 'Rename',
    'attachments.action.delete': 'Delete',
    'attachments.source.purchase_request': 'Ordering',
    'attachments.source.invoice': 'Invoice',
    'attachments.source.receiving': 'Receiving',
    'attachments.source.other': 'Other',
    'attachments.take_a_photo': 'TAKE A PHOTO',
    'attachments.attach_file': 'ATTACH FILE',
    'attachments.too_many_files': 'You can attach up to 5 files on this stage',
    'attachments.delete_success': 'Document successfully deleted',
    'attachments.rename_success': 'Document successfully renamed',
    'attachments.upload_success': 'Document attached',
    'attachments.empty': 'There are no documents attached',
    'attachments.maximum_file_size_is_10mb':
        'Maximum file size allowed is 10 MB',

    // Global errors
    'error.session_expire_title': 'Session has been expired',
    'error.session_expire_message':
        'It seems your session has been expired on server. Please login again in order to continue use application.',
    'error.session_terminated_message':
        'It seems your session has been terminated on server. Please login again in order to continue use application.',

    // App version
    'app.version.version_is_lower_than_minimal_required':
        'Your application version is {{current_version}} and it is lower than minimal supported version {{minimal_version}}.\n\nPlease update the application.',
    'app.version.version_is_lower_than_the_latest':
        'It seems your application is outdated, you are using version {{current_version}} but the latest version is {{latest_version}}. Please update your application as soon as possible',
    'app.version.update_button_label': 'UPDATE',
    'app.version.cancel_button_label': 'CANCEL',
    'app.version.store_launch_error_message':
        'Unable to open app store. Please, update the app manually or contact support.',

    // Application errors
    'app.unsupported_platform':
        'This feature is not supported on current platfrom',
    'app.connection.missing': 'Server is unavailable',
    'app.connection.interrupted': 'Error while trying to connect to the server',
    'app.connection.no_token_passed': 'No access token passed',
    'app.storage.not_initialized': 'Error while storage init',
    'app.notification.not_valid': 'Invalid push notification!',
    'app.notification.unit_and_cost_center_switch_required__named':
        'To open this notification you need to switch to the {{division}} unit and {{costcenter}} costcenter. Do you want to switch now?',
    'app.supplier.cannot_launch_oci_link': 'Error while launching OCI link',
    'app.localization.cannot_load_from_assets':
        'Error while loading localization data from assets',
    'app.localization.cannot_load_from_api':
        'Error while loading localization data from api',
    'app.storage.db_unsupported':
        'Your database version is unsupported for the upgrade. Do you want to retry without data saving?',
    'app.storage.db_backup_interrupted':
        'Is is not possible to backup your database. Do you want to retry without data saving?',
    'app.storage.db_recovery_interrupted':
        'Is is not possible to backup your database. Do you want to retry without data saving?',
    'app.no_license': 'You are not authorized to access this function',
    'app.no_permission': 'You are not authorized to access this function',
    'app.file_not_scanned': 'File not scanned. Error.',
    'app.file_not_uploaded': 'File not uploaded. Error.',
    'app.file_is_too_large': 'File is too large',
    'app.maximum_file_size_is_10mb': 'Maximum file size allowed is 10 MB',
    'app.scanned_file_not_found':
        "Scanned document doesn't saved. Please try again.",
    'app.scanned_file_not_cropped':
        "Scanned document does't cropped. Please try again",
    'app.asset.load_error': 'Asset load error',

    'app.deep_link.unknown': 'Unknown error',
    'app.sso_link.unknown': 'Unknown error',
    'app.uni_link.unknown': 'Unknown error',
    'app.sso_link.unit_and_cost_center_switch_required__named':
        'To open this link you need to switch to the {{division}} unit and {{costcenter}} costcenter. Do you want to switch now?',
    'app.sso_link.unit_access_required':
        "You don't have access to the required unit",
    'app.sso_link.login_required': 'You should be logged in to use this link',

    'app.firebase.unknown': 'Unknown error',

    'app.inventory.item_not_found': 'Inventory item not found',
    'app.inventory.closed_list_not_available_offline':
        'Closed inventory available only online',
    'app.inventory_list_not_loaded':
        'Inventory list not loaded. Online connection required.',
    'app.inventory_list_offline_search': 'Online connection required.',
    'app.inventory_list_is_not_valid': 'Inventory list is not locked by you.',
    'app.inventory_list_item_not_found': 'Item not found in inventory list',
    'app.inventory_list.is_not_locked_by_user':
        'Inventory list is not locked by you',

    'app.login.otp_token_expired_error':
        'OTP token expired. Please, login again',
    'app.login.canceled_error': 'Login was canceled',
    'app.login.unknown_user_status_error': 'User status unknown',
    'app.cost_center_not_available': 'Cost center is not available',
    'app.camera_permission_not_granted': 'Camera permission not granted',
    'app.barcode_scanner_unknown_error': 'Barcode scanner unknown error',
    'app.search_service_unavailable_error': 'Search service unavailable',
    'app.search_service_unknown_error': 'Search service error',
    'app.search_service_timeout_error':
        'Search service is currently unavailable. Please, try again later',
    'app.no_internet_connection_error': 'Please check your internet connection',
    'app.cannot_launch_url_error': 'Error opening link',

    'app.bluetooth_device.already_connected':
        'Bluetooth device is already connected',
    'app.bluetooth_device.connection_error':
        "Can't connect to the device. Make sure it is enabled and configured.",
    'app.bluetooth_device.change_settings_error':
        'Unable to configure Bluetooth device. Please, contact support for assistance.',
    'app.bluetooth_device.not_connected':
        'Bluetooth device is not connected. Please try to reconnect again.',
    'app.bluetooth_device.not_valid':
        'Device validation failed. Make sure that selected device is the right one.',
    'app.bluetooth_device.scanning_error':
        'An error occurred while scanning for the Bluetooth devices',
    'app.bluetooth_device.already_added': 'Bluetooth device is already added.',
    'app.bluetooth_device.measurement_format_error':
        'Measurement format is not recognized',
    'app.bluetooth_device.testo_thermometer.activation_failed':
        'Testo thermometer activation failed. Please try again or contact support.',
    'app.bluetooth_device.testo_thermometer.activation_key_retrieval_failed':
        'Activation key retrieval failed. Please check your connection and try again, or contact support.',
    'app.bluetooth_device.mt_scale.continuous_mode_enabled':
        'Please disable continuous weight sending in the scale settings and try again.',
    'app.bluetooth_device.mt_scale.not_mt_sic_protocol':
        'Please MT-SIC protocol in the scale settings and try again.',
    'app.bluetooth_device.digi_scale.continuous_mode_disabled':
        'Please make sure that device is enabled. If so, enable continuous weight sending in the scale settings and try again.',
    'app.bluetooth_device.digi_scale.response_header_code_or_unit_disabled':
        'Please enable response header code and unit in the scale settings and try again.',

    'app.pdf.not_loaded': 'Error while loading PDF. Please try again.',
    'app.image.not_loaded': 'Error while loading image. Please try again.',
    'app.pdf.print_error':
        'Error happened while printing the PDF. Please try again.',
    'app.pdf.generation_error':
        'Error happened while generating the PDF. Please try again.',

    'app.db.unknown': 'Unknown database error',

    'app.order_list.not_found': 'Order list not found',
    'app.order_list.product_not_found_in_cart':
        'Product not found in the shopping cart',

    'app.reports.load_error': 'Report load error',

    'app.file.load_error': 'File load error',
    'app.file.save_error': 'File save error',
    'app.file.update_error': 'File update error',
    'app.file.share_error':
        'Error happened while sharing the file. Please try again.',

    'app.photo.camera_error': 'Unable to use camera',
    'app.photo.pick_error': 'Unable to select image',

    'app.camera.operation_error': 'Camera error',
    'app.camera.access_denied': 'The app does not have access to the camera',
    'app.camera.access_denied_settings':
        'The app does not have access to the camera. Please, enable it in the Settings app.',
    'app.camera.access_restricted': 'Camera access is restricted',

    'app.common.unexpected_error':
        'An unexpected error happened. Please try again.',

    //Global product labels
    'fl.AQUACULTURE_STEWARDSHIP_COUNCIL': 'Aquaculture Stewardship Council',
    'fl.AUSTRALIAN_HALAL_CERTIFICATION': 'Australian Halal Certification',
    'fl.BIO_ORGANIC_SUISSE': 'Bio Organic Suisse',
    'fl.BIO_ORGANIC_GERMANY': 'Bio Organic Germany',
    'fl.BIO_ORGANIC_EU': 'Bio Organic EU',
    'fl.FAIRTRADE_OESTERREICH': 'Fairtrade Oesterreich',
    'fl.FOREST_STEWARDSHIP_COUNCIL': 'Forest Stewardship Council',
    'fl.MARINE_STEWARDSHIP_COUNCIL': 'Marine Stewardship Council',
    'fl.PRODUCT_OF_AUSTRALIA': 'Product of Australia',
    'fl.PROGROS': 'Progros',
    'fl.RAINFOREST_ALLIANCE': 'Rainforest Alliance',
    'fl.UTZ_CERTIFIED': 'UTZ Certified',
    'fl.VEGAN_AUSTRALIA_CERTIFIED': 'Vegan Australia Certified',
    'fl.HALAL_CERTIFICATION': 'Halal Certification',
  };
}
